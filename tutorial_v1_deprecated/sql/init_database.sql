-- ============================================
-- WQ项目数据库初始化脚本
-- 基于新的用户账户表设计
-- 注意：此脚本会删除所有现有表和数据，请谨慎使用
-- ============================================
-- ============================================
-- 清理现有数据库结构（按依赖关系逆序删除）
-- ============================================
-- 删除所有触发器
DROP TRIGGER IF EXISTS tr_user_accounts_updated_at;

DROP TRIGGER IF EXISTS tr_account_dataset_usage_updated_at;

DROP TRIGGER IF EXISTS tr_alpha_digging_process_updated_at;

DROP TRIGGER IF EXISTS tr_regions_updated_at;

DROP TRIGGER IF EXISTS tr_url_config_updated_at;

DROP TRIGGER IF EXISTS tr_market_universes_updated_at;

DROP TRIGGER IF EXISTS tr_instrument_types_updated_at;

DROP TRIGGER IF EXISTS tr_dataset_categories_updated_at;

DROP TRIGGER IF EXISTS tr_delay_config_updated_at;

-- 删除所有视图
DROP VIEW IF EXISTS v_user_accounts_overview;

DROP VIEW IF EXISTS v_datasets_overview;

DROP VIEW IF EXISTS v_account_dataset_usage_stats;

DROP VIEW IF EXISTS v_account_alpha_daily_stats;

DROP VIEW IF EXISTS v_account_alpha_monthly_stats;

DROP VIEW IF EXISTS v_alpha_digging_stats;

DROP VIEW IF EXISTS v_alpha_digging_success_rate;

DROP VIEW IF EXISTS v_region_summary;

DROP VIEW IF EXISTS v_market_universe_overview;

DROP VIEW IF EXISTS v_config_stats;

DROP VIEW IF EXISTS v_url_summary;

-- 删除所有索引（明确删除，避免表删除时的依赖问题）
DROP INDEX IF EXISTS idx_user_email;

DROP INDEX IF EXISTS idx_user_name;

DROP INDEX IF EXISTS idx_dataset_name;

DROP INDEX IF EXISTS idx_account_dataset_usage_account;

DROP INDEX IF EXISTS idx_account_dataset_usage_dataset;

DROP INDEX IF EXISTS idx_account_dataset_usage_time;

DROP INDEX IF EXISTS idx_account_alpha_account_name;

DROP INDEX IF EXISTS idx_account_alpha_date;

DROP INDEX IF EXISTS idx_account_alpha_dataset;

DROP INDEX IF EXISTS idx_account_alpha_alpha_id;

DROP INDEX IF EXISTS idx_account_alpha_composite;

DROP INDEX IF EXISTS idx_alpha_digging_account;

DROP INDEX IF EXISTS idx_alpha_digging_alpha_id;

DROP INDEX IF EXISTS idx_alpha_digging_status;

DROP INDEX IF EXISTS idx_alpha_digging_time;

DROP INDEX IF EXISTS idx_alpha_digging_date_created;

DROP INDEX IF EXISTS idx_alpha_digging_region;

DROP INDEX IF EXISTS idx_alpha_digging_status_time;

DROP INDEX IF EXISTS idx_alpha_digging_account_status;

DROP INDEX IF EXISTS idx_region_code;

DROP INDEX IF EXISTS idx_region_name;

DROP INDEX IF EXISTS idx_region_instrument;

DROP INDEX IF EXISTS idx_market_universe_region;

DROP INDEX IF EXISTS idx_market_universe_instrument;

DROP INDEX IF EXISTS idx_market_universe_code;

DROP INDEX IF EXISTS idx_market_universe_composite;

DROP INDEX IF EXISTS idx_instrument_type_code;

DROP INDEX IF EXISTS idx_instrument_type_active;

DROP INDEX IF EXISTS idx_dataset_category_code;

DROP INDEX IF EXISTS idx_dataset_category_active;

DROP INDEX IF EXISTS idx_delay_value;

DROP INDEX IF EXISTS idx_delay_active;

DROP INDEX IF EXISTS idx_url_key;

DROP INDEX IF EXISTS idx_url_active;

-- 删除所有表（按外键依赖关系逆序）
-- 1. 首先删除有外键依赖的表
DROP TABLE IF EXISTS account_dataset_usage;

DROP TABLE IF EXISTS account_alpha_factors;

DROP TABLE IF EXISTS alpha_digging_process;

DROP TABLE IF EXISTS market_universes;

-- 2. 删除被依赖的表
DROP TABLE IF EXISTS user_accounts;

DROP TABLE IF EXISTS datasets;

DROP TABLE IF EXISTS regions;

DROP TABLE IF EXISTS instrument_types;

DROP TABLE IF EXISTS dataset_categories;

DROP TABLE IF EXISTS delay_config;

DROP TABLE IF EXISTS url_config;

-- ============================================
-- 重新创建数据库结构
-- ============================================
-- 用户账户表
CREATE TABLE
    IF NOT EXISTS user_accounts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        email VARCHAR(255) NOT NULL UNIQUE, -- 用户邮箱（作为登录用户名）
        password VARCHAR(255) NOT NULL, -- 用户密码
        name VARCHAR(100) NOT NULL, -- 用户姓名/别名
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- 数据集表
CREATE TABLE
    IF NOT EXISTS datasets (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        name VARCHAR(100) NOT NULL UNIQUE, -- 数据集名称
        description TEXT, -- 数据集描述
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- 账户使用数据集记录表
CREATE TABLE
    IF NOT EXISTS account_dataset_usage (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL, -- 用户账户ID
        dataset_id INTEGER NOT NULL, -- 数据集ID
        start_time TIMESTAMP NOT NULL, -- 开始使用时间
        end_time TIMESTAMP, -- 结束使用时间（NULL表示仍在使用）
        success_factor_count INTEGER DEFAULT 0, -- 成功因子数量
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES user_accounts (id) ON DELETE CASCADE,
        FOREIGN KEY (dataset_id) REFERENCES datasets (id) ON DELETE CASCADE
    );

-- 账户因子Alpha表
CREATE TABLE
    IF NOT EXISTS account_alpha_factors (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_name VARCHAR(100) NOT NULL, -- 账户名称
        date DATE NOT NULL, -- 日期
        dataset_name VARCHAR(100) NOT NULL, -- 数据集名称
        success_alpha_id VARCHAR(100) NOT NULL, -- 成功的Alpha ID
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (dataset_name) REFERENCES datasets (name) ON DELETE CASCADE
    );

-- 因子挖掘过程表（基于submitable_alpha.csv结构）
CREATE TABLE
    IF NOT EXISTS alpha_digging_process (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        account_id INTEGER NOT NULL, -- 用户账户ID
        alpha_id VARCHAR(100), -- Alpha ID（来自原始CSV的id字段）
        type VARCHAR(50), -- 类型
        author VARCHAR(100), -- 作者
        instrument_type VARCHAR(50), -- 工具类型
        region VARCHAR(50), -- 地区
        universe VARCHAR(100), -- 宇宙
        delay INTEGER, -- 延迟
        decay REAL, -- 衰减
        neutralization VARCHAR(100), -- 中性化
        truncation REAL, -- 截断
        pasteurization VARCHAR(100), -- 巴氏杀菌
        unit_handling VARCHAR(100), -- 单位处理
        nan_handling VARCHAR(100), -- NaN处理
        language VARCHAR(50), -- 语言
        visualization VARCHAR(100), -- 可视化
        code TEXT, -- 代码
        description TEXT, -- 描述
        operator_count INTEGER, -- 操作符数量
        date_created TIMESTAMP, -- 创建日期
        date_submitted TIMESTAMP, -- 提交日期
        date_modified TIMESTAMP, -- 修改日期
        name VARCHAR(200), -- 名称
        favorite BOOLEAN DEFAULT FALSE, -- 是否收藏
        hidden BOOLEAN DEFAULT FALSE, -- 是否隐藏
        color VARCHAR(50), -- 颜色
        category VARCHAR(100), -- 类别
        tags TEXT, -- 标签（原始tags字段）
        classifications VARCHAR(200), -- 分类
        grade VARCHAR(50), -- 等级
        stage VARCHAR(50), -- 阶段
        status VARCHAR(50), -- 状态
        pnl REAL, -- 盈亏
        book_size REAL, -- 账面规模
        long_count INTEGER, -- 多头数量
        short_count INTEGER, -- 空头数量
        turnover REAL, -- 换手率
        returns REAL, -- 收益
        drawdown REAL, -- 回撤
        margin REAL, -- 保证金
        fitness REAL, -- 适应度
        sharpe REAL, -- 夏普比率
        start_date DATE, -- 开始日期
        checks VARCHAR(200), -- 检查
        os VARCHAR(100), -- 操作系统
        train REAL, -- 训练
        test REAL, -- 测试
        prod REAL, -- 生产
        competitions VARCHAR(200), -- 竞赛
        themes VARCHAR(200), -- 主题
        team VARCHAR(100), -- 团队
        pyramids INTEGER, -- 金字塔
        self_corr REAL, -- 自相关
        -- 新增的标签字段
        tag1 VARCHAR(100), -- 标签1
        tag2 VARCHAR(100), -- 标签2
        tag3 VARCHAR(100), -- 标签3
        tag4 VARCHAR(100), -- 标签4
        tag5 VARCHAR(100), -- 标签5
        tag6 VARCHAR(100), -- 标签6
        tag7 VARCHAR(100), -- 标签7
        tag8 VARCHAR(100), -- 标签8
        tag9 VARCHAR(100), -- 标签9
        tag10 VARCHAR(100), -- 标签10
        -- 挖掘过程相关字段
        digging_time TIMESTAMP NOT NULL, -- 挖掘时间
        digging_status VARCHAR(50) NOT NULL DEFAULT 'in_progress', -- 挖掘状态: in_progress, completed, failed, cancelled
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (account_id) REFERENCES user_accounts (id) ON DELETE CASCADE
    );

-- 地区配置表
CREATE TABLE
    IF NOT EXISTS regions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        region_code VARCHAR(10) NOT NULL, -- 地区代码: USA, ASI, EUR等
        region_name VARCHAR(100) NOT NULL, -- 地区名称: USA, ASI, EUR等
        instrument_type VARCHAR(50) NOT NULL, -- 工具类型: EQUITY, CRYPTO
        description TEXT, -- 地区描述
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        UNIQUE (region_code, instrument_type)
    );

-- 市场宇宙配置表（基于UNIVERSE_DICT）
CREATE TABLE
    IF NOT EXISTS market_universes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        region_code VARCHAR(10) NOT NULL, -- 地区代码
        instrument_type VARCHAR(50) NOT NULL, -- 工具类型
        universe_code VARCHAR(50) NOT NULL, -- 市场代码: TOP3000, MINVOL1M等
        description TEXT, -- 市场描述
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (region_code, instrument_type) REFERENCES regions (region_code, instrument_type) ON DELETE CASCADE,
        UNIQUE (region_code, instrument_type, universe_code)
    );

-- 工具类型配置表
CREATE TABLE
    IF NOT EXISTS instrument_types (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type_code VARCHAR(20) NOT NULL UNIQUE, -- 类型代码: EQUITY, CRYPTO
        type_name VARCHAR(100) NOT NULL, -- 类型名称
        description TEXT, -- 类型描述
        is_active BOOLEAN DEFAULT 1, -- 是否激活
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- 数据集类别配置表
CREATE TABLE
    IF NOT EXISTS dataset_categories (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        category_code VARCHAR(50) NOT NULL UNIQUE, -- 类别代码: pv, fundamental等
        category_name VARCHAR(100) NOT NULL, -- 类别名称
        description TEXT, -- 类别描述
        is_active BOOLEAN DEFAULT 1, -- 是否激活
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- 延迟配置表
CREATE TABLE
    IF NOT EXISTS delay_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        delay_value INTEGER NOT NULL UNIQUE, -- 延迟值: 0, 1等
        description TEXT, -- 延迟描述
        is_active BOOLEAN DEFAULT 1, -- 是否激活
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- URL配置表
CREATE TABLE
    IF NOT EXISTS url_config (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        url_key VARCHAR(100) NOT NULL UNIQUE, -- URL键: api_base, auth, alphas等
        url_path VARCHAR(500) NOT NULL, -- URL路径
        description TEXT, -- URL描述
        is_active BOOLEAN DEFAULT 1, -- 是否激活
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
    );

-- ============================================
-- 索引创建
-- ============================================
-- 用户账户表索引
CREATE INDEX IF NOT EXISTS idx_user_email ON user_accounts (email);

CREATE INDEX IF NOT EXISTS idx_user_name ON user_accounts (name);

-- 数据集表索引
CREATE INDEX IF NOT EXISTS idx_dataset_name ON datasets (name);

-- 账户使用数据集记录表索引
CREATE INDEX IF NOT EXISTS idx_account_dataset_usage_account ON account_dataset_usage (account_id);

CREATE INDEX IF NOT EXISTS idx_account_dataset_usage_dataset ON account_dataset_usage (dataset_id);

CREATE INDEX IF NOT EXISTS idx_account_dataset_usage_time ON account_dataset_usage (start_time, end_time);

-- 账户因子Alpha表索引
CREATE INDEX IF NOT EXISTS idx_account_alpha_account_name ON account_alpha_factors (account_name);

CREATE INDEX IF NOT EXISTS idx_account_alpha_date ON account_alpha_factors (date);

CREATE INDEX IF NOT EXISTS idx_account_alpha_dataset ON account_alpha_factors (dataset_name);

CREATE INDEX IF NOT EXISTS idx_account_alpha_alpha_id ON account_alpha_factors (success_alpha_id);

CREATE INDEX IF NOT EXISTS idx_account_alpha_composite ON account_alpha_factors (account_name, date, dataset_name);

-- 因子挖掘过程表索引
CREATE INDEX IF NOT EXISTS idx_alpha_digging_account ON alpha_digging_process (account_id);

CREATE INDEX IF NOT EXISTS idx_alpha_digging_alpha_id ON alpha_digging_process (alpha_id);

CREATE INDEX IF NOT EXISTS idx_alpha_digging_status ON alpha_digging_process (digging_status);

CREATE INDEX IF NOT EXISTS idx_alpha_digging_time ON alpha_digging_process (digging_time);

CREATE INDEX IF NOT EXISTS idx_alpha_digging_date_created ON alpha_digging_process (date_created);

CREATE INDEX IF NOT EXISTS idx_alpha_digging_region ON alpha_digging_process (region);

CREATE INDEX IF NOT EXISTS idx_alpha_digging_status_time ON alpha_digging_process (digging_status, digging_time);

CREATE INDEX IF NOT EXISTS idx_alpha_digging_account_status ON alpha_digging_process (account_id, digging_status);

-- 地区表索引
CREATE INDEX IF NOT EXISTS idx_region_code ON regions (region_code);

CREATE INDEX IF NOT EXISTS idx_region_name ON regions (region_name);

CREATE INDEX IF NOT EXISTS idx_region_instrument ON regions (region_code, instrument_type);

-- 市场宇宙表索引
CREATE INDEX IF NOT EXISTS idx_market_universe_region ON market_universes (region_code);

CREATE INDEX IF NOT EXISTS idx_market_universe_instrument ON market_universes (instrument_type);

CREATE INDEX IF NOT EXISTS idx_market_universe_code ON market_universes (universe_code);

CREATE INDEX IF NOT EXISTS idx_market_universe_composite ON market_universes (region_code, instrument_type, universe_code);

-- 工具类型表索引
CREATE INDEX IF NOT EXISTS idx_instrument_type_code ON instrument_types (type_code);

CREATE INDEX IF NOT EXISTS idx_instrument_type_active ON instrument_types (is_active);

-- 数据集类别表索引
CREATE INDEX IF NOT EXISTS idx_dataset_category_code ON dataset_categories (category_code);

CREATE INDEX IF NOT EXISTS idx_dataset_category_active ON dataset_categories (is_active);

-- 延迟配置表索引
CREATE INDEX IF NOT EXISTS idx_delay_value ON delay_config (delay_value);

CREATE INDEX IF NOT EXISTS idx_delay_active ON delay_config (is_active);

-- URL配置表索引
CREATE INDEX IF NOT EXISTS idx_url_key ON url_config (url_key);

CREATE INDEX IF NOT EXISTS idx_url_active ON url_config (is_active);

-- ============================================
-- 基于config.json的初始数据插入
-- ============================================
-- 插入数据集数据
INSERT
OR REPLACE INTO datasets (name, description)
VALUES
    ('news18', '新闻数据集18'),
    ('equity', '股票数据集'),
    ('futures', '期货数据集'),
    ('crypto', '加密货币数据集'),
    ('options', '期权数据集');

-- 插入工具类型配置数据（基于INSTRUMENT_TYPE_LIST）
INSERT
OR REPLACE INTO instrument_types (type_code, type_name, description, is_active)
VALUES
    ('EQUITY', '股票', '股票工具类型', 1),
    ('CRYPTO', '加密货币', '加密货币工具类型', 1);

-- 插入数据集类别配置数据（基于DATASET_CATEGORY_LIST）
INSERT
OR REPLACE INTO dataset_categories (
    category_code,
    category_name,
    description,
    is_active
)
VALUES
    ('pv', '价格成交量', '价格和成交量相关数据', 1),
    ('fundamental', '基本面', '基本面分析数据', 1),
    ('analyst', '分析师', '分析师研究数据', 1),
    ('socialmedia', '社交媒体', '社交媒体情感数据', 1),
    ('news', '新闻', '新闻舆情数据', 1),
    ('option', '期权', '期权相关数据', 1),
    ('model', '模型', '量化模型数据', 1),
    ('shortinterest', '空头头寸', '空头持仓数据', 1),
    ('institutions', '机构', '机构投资者数据', 1),
    ('other', '其他', '其他类别数据', 1),
    ('sentiment', '情感', '市场情感数据', 1),
    ('insiders', '内部人', '内部交易数据', 1),
    ('earnings', '财报', '财报相关数据', 1),
    ('macro', '宏观', '宏观经济数据', 1),
    ('imbalance', '失衡', '市场失衡数据', 1),
    ('risk', '风险', '风险管理数据', 1);

-- 插入延迟配置数据（基于DELAY_LIST）
INSERT
OR REPLACE INTO delay_config (delay_value, description, is_active)
VALUES
    (0, '无延迟', 1),
    (1, '1天延迟', 1);

-- 插入地区配置数据（基于UNIVERSE_DICT中的地区和工具类型）
INSERT
OR REPLACE INTO regions (
    region_code,
    region_name,
    instrument_type,
    description
)
VALUES
    ('USA', '美国市场', 'EQUITY', '美国股票市场'),
    ('GLB', '全球市场', 'EQUITY', '全球股票市场'),
    ('EUR', '欧洲市场', 'EQUITY', '欧洲股票市场'),
    ('ASI', '亚洲市场', 'EQUITY', '亚洲股票市场'),
    ('CHN', '中国市场', 'EQUITY', '中国股票市场'),
    ('KOR', '韩国市场', 'EQUITY', '韩国股票市场'),
    ('TWN', '台湾市场', 'EQUITY', '台湾股票市场'),
    ('HKG', '香港市场', 'EQUITY', '香港股票市场'),
    ('JPN', '日本市场', 'EQUITY', '日本股票市场'),
    ('AMR', '美洲市场', 'EQUITY', '美洲股票市场'),
    ('GLB', '全球市场', 'CRYPTO', '全球加密货币市场');

-- 插入市场宇宙配置数据（基于UNIVERSE_DICT的详细映射）
INSERT
OR REPLACE INTO market_universes (
    region_code,
    instrument_type,
    universe_code,
    description
)
VALUES
    -- 美国股票市场
    ('USA', 'EQUITY', 'TOP3000', '美国前3000只股票'),
    ('USA', 'EQUITY', 'TOP1000', '美国前1000只股票'),
    ('USA', 'EQUITY', 'TOP500', '美国前500只股票'),
    ('USA', 'EQUITY', 'TOP200', '美国前200只股票'),
    (
        'USA',
        'EQUITY',
        'ILLIQUID_MINVOL1M',
        '美国低流动性最小成交量1M'
    ),
    ('USA', 'EQUITY', 'TOPSP500', '美国标普500'),
    -- 全球股票市场
    ('GLB', 'EQUITY', 'TOP3000', '全球前3000只股票'),
    ('GLB', 'EQUITY', 'MINVOL1M', '全球最小成交量1M'),
    -- 欧洲股票市场
    ('EUR', 'EQUITY', 'TOP1200', '欧洲前1200只股票'),
    ('EUR', 'EQUITY', 'TOP800', '欧洲前800只股票'),
    ('EUR', 'EQUITY', 'TOP400', '欧洲前400只股票'),
    (
        'EUR',
        'EQUITY',
        'ILLIQUID_MINVOL1M',
        '欧洲低流动性最小成交量1M'
    ),
    -- 亚洲股票市场
    ('ASI', 'EQUITY', 'MINVOL1M', '亚洲最小成交量1M'),
    (
        'ASI',
        'EQUITY',
        'ILLIQUID_MINVOL1M',
        '亚洲低流动性最小成交量1M'
    ),
    -- 中国股票市场
    ('CHN', 'EQUITY', 'TOP2000U', '中国前2000只股票'),
    -- 韩国股票市场
    ('KOR', 'EQUITY', 'TOP600', '韩国前600只股票'),
    -- 台湾股票市场
    ('TWN', 'EQUITY', 'TOP500', '台湾前500只股票'),
    ('TWN', 'EQUITY', 'TOP100', '台湾前100只股票'),
    -- 香港股票市场
    ('HKG', 'EQUITY', 'TOP800', '香港前800只股票'),
    ('HKG', 'EQUITY', 'TOP500', '香港前500只股票'),
    -- 日本股票市场
    ('JPN', 'EQUITY', 'TOP1600', '日本前1600只股票'),
    ('JPN', 'EQUITY', 'TOP1200', '日本前1200只股票'),
    -- 美洲股票市场
    ('AMR', 'EQUITY', 'TOP600', '美洲前600只股票'),
    -- 全球加密货币市场
    ('GLB', 'CRYPTO', 'TOP50', '全球前50加密货币'),
    ('GLB', 'CRYPTO', 'TOP20', '全球前20加密货币'),
    ('GLB', 'CRYPTO', 'TOP10', '全球前10加密货币'),
    ('GLB', 'CRYPTO', 'TOP5', '全球前5加密货币');

-- 插入URL配置数据
INSERT
OR REPLACE INTO url_config (url_key, url_path, description, is_active)
VALUES
    (
        'api_base',
        'https://api.worldquantbrain.com',
        'API基础URL',
        1
    ),
    ('auth', '/authentication', '认证接口', 1),
    ('operators', '/operators', '操作符接口', 1),
    ('alphas', '/alphas', 'Alpha接口', 1),
    (
        'alpha_submit',
        '/alphas/{alpha_id}/submit',
        'Alpha提交接口',
        1
    ),
    (
        'alpha_check',
        '/alphas/{alpha_id}/check',
        'Alpha检查接口',
        1
    ),
    (
        'alpha_user',
        '/users/self/alphas',
        '用户Alpha接口',
        1
    ),
    ('simulations', '/simulations', '模拟接口', 1),
    ('data_sets', '/data-sets', '数据集接口', 1),
    ('data_fields', '/data-fields', '数据字段接口', 1);

-- ============================================
-- 数据完整性检查视图
-- ============================================
-- 用户账户概览视图
CREATE VIEW
    IF NOT EXISTS v_user_accounts_overview AS
SELECT
    COUNT(*) as total_users,
    COUNT(
        CASE
            WHEN created_at >= datetime ('now', '-7 days') THEN 1
        END
    ) as new_users_last_7_days,
    MIN(created_at) as earliest_user,
    MAX(created_at) as latest_user
FROM
    user_accounts;

-- 数据集概览视图
CREATE VIEW
    IF NOT EXISTS v_datasets_overview AS
SELECT
    COUNT(*) as total_datasets,
    GROUP_CONCAT (name, ', ') as dataset_names
FROM
    datasets;

-- 账户数据集使用统计视图
CREATE VIEW
    IF NOT EXISTS v_account_dataset_usage_stats AS
SELECT
    ua.name as account_name,
    ds.name as dataset_name,
    COUNT(*) as usage_count,
    SUM(adu.success_factor_count) as total_success_factors,
    MAX(adu.start_time) as last_usage_time
FROM
    account_dataset_usage adu
    JOIN user_accounts ua ON adu.account_id = ua.id
    JOIN datasets ds ON adu.dataset_id = ds.id
GROUP BY
    ua.name,
    ds.name;

-- 账户Alpha因子统计视图（按日期）
CREATE VIEW
    IF NOT EXISTS v_account_alpha_daily_stats AS
SELECT
    account_name,
    date,
    dataset_name,
    COUNT(*) as daily_success_count,
    COUNT(DISTINCT success_alpha_id) as unique_alpha_count
FROM
    account_alpha_factors
GROUP BY
    account_name,
    date,
    dataset_name
ORDER BY
    account_name,
    date DESC;

-- 账户Alpha因子月度统计视图
CREATE VIEW
    IF NOT EXISTS v_account_alpha_monthly_stats AS
SELECT
    account_name,
    strftime ('%Y-%m', date) as month,
    dataset_name,
    COUNT(*) as monthly_success_count,
    COUNT(DISTINCT success_alpha_id) as unique_alpha_count,
    COUNT(DISTINCT date) as active_days
FROM
    account_alpha_factors
GROUP BY
    account_name,
    strftime ('%Y-%m', date),
    dataset_name
ORDER BY
    account_name,
    month DESC;

-- 因子挖掘过程统计视图
CREATE VIEW
    IF NOT EXISTS v_alpha_digging_stats AS
SELECT
    ua.name as account_name,
    adp.digging_status,
    COUNT(*) as count,
    AVG(adp.fitness) as avg_fitness,
    AVG(adp.sharpe) as avg_sharpe,
    MAX(adp.digging_time) as latest_digging_time
FROM
    alpha_digging_process adp
    JOIN user_accounts ua ON adp.account_id = ua.id
GROUP BY
    ua.name,
    adp.digging_status
ORDER BY
    ua.name,
    adp.digging_status;

-- 因子挖掘成功率视图
CREATE VIEW
    IF NOT EXISTS v_alpha_digging_success_rate AS
SELECT
    ua.name as account_name,
    COUNT(*) as total_attempts,
    COUNT(
        CASE
            WHEN adp.digging_status = 'completed' THEN 1
        END
    ) as successful_attempts,
    ROUND(
        COUNT(
            CASE
                WHEN adp.digging_status = 'completed' THEN 1
            END
        ) * 100.0 / COUNT(*),
        2
    ) as success_rate_percent,
    AVG(
        CASE
            WHEN adp.digging_status = 'completed' THEN adp.fitness
        END
    ) as avg_success_fitness
FROM
    alpha_digging_process adp
    JOIN user_accounts ua ON adp.account_id = ua.id
GROUP BY
    ua.name
ORDER BY
    success_rate_percent DESC;

-- 地区信息视图
CREATE VIEW
    IF NOT EXISTS v_region_summary AS
SELECT
    r.region_code,
    r.region_name,
    r.instrument_type,
    r.description,
    COUNT(mu.universe_code) as universe_count,
    GROUP_CONCAT (mu.universe_code, ', ') as available_universes,
    r.created_at
FROM
    regions r
    LEFT JOIN market_universes mu ON r.region_code = mu.region_code
    AND r.instrument_type = mu.instrument_type
GROUP BY
    r.region_code,
    r.instrument_type
ORDER BY
    r.region_code,
    r.instrument_type;

-- 市场宇宙概览视图
CREATE VIEW
    IF NOT EXISTS v_market_universe_overview AS
SELECT
    instrument_type,
    COUNT(DISTINCT region_code) as region_count,
    COUNT(DISTINCT universe_code) as universe_count,
    COUNT(*) as total_combinations,
    GROUP_CONCAT (DISTINCT region_code, ', ') as regions
FROM
    market_universes
GROUP BY
    instrument_type
ORDER BY
    instrument_type;

-- 配置数据统计视图
CREATE VIEW
    IF NOT EXISTS v_config_stats AS
SELECT
    'instrument_types' as config_type,
    COUNT(*) as total_count,
    COUNT(
        CASE
            WHEN is_active = 1 THEN 1
        END
    ) as active_count
FROM
    instrument_types
UNION ALL
SELECT
    'dataset_categories' as config_type,
    COUNT(*) as total_count,
    COUNT(
        CASE
            WHEN is_active = 1 THEN 1
        END
    ) as active_count
FROM
    dataset_categories
UNION ALL
SELECT
    'delay_config' as config_type,
    COUNT(*) as total_count,
    COUNT(
        CASE
            WHEN is_active = 1 THEN 1
        END
    ) as active_count
FROM
    delay_config
UNION ALL
SELECT
    'regions' as config_type,
    COUNT(*) as total_count,
    COUNT(*) as active_count
FROM
    regions
UNION ALL
SELECT
    'market_universes' as config_type,
    COUNT(*) as total_count,
    COUNT(*) as active_count
FROM
    market_universes
ORDER BY
    config_type;

-- URL配置视图
CREATE VIEW
    IF NOT EXISTS v_url_summary AS
SELECT
    url_key,
    url_path,
    is_active,
    CASE
        WHEN url_path LIKE 'http%' THEN '完整URL'
        WHEN url_path LIKE '/%' THEN '相对路径'
        ELSE '其他'
    END as url_type
FROM
    url_config
ORDER BY
    url_key;

-- ============================================
-- 触发器：自动更新时间戳
-- ============================================
-- 用户账户表更新触发器
CREATE TRIGGER IF NOT EXISTS tr_user_accounts_updated_at AFTER
UPDATE ON user_accounts FOR EACH ROW BEGIN
UPDATE user_accounts
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;

-- 账户使用数据集记录表更新触发器
CREATE TRIGGER IF NOT EXISTS tr_account_dataset_usage_updated_at AFTER
UPDATE ON account_dataset_usage FOR EACH ROW BEGIN
UPDATE account_dataset_usage
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;

-- 因子挖掘过程表更新触发器
CREATE TRIGGER IF NOT EXISTS tr_alpha_digging_process_updated_at AFTER
UPDATE ON alpha_digging_process FOR EACH ROW BEGIN
UPDATE alpha_digging_process
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;

-- 地区表更新触发器  
CREATE TRIGGER IF NOT EXISTS tr_regions_updated_at AFTER
UPDATE ON regions FOR EACH ROW BEGIN
UPDATE regions
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;

-- URL配置表更新触发器
CREATE TRIGGER IF NOT EXISTS tr_url_config_updated_at AFTER
UPDATE ON url_config FOR EACH ROW BEGIN
UPDATE url_config
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;

-- 市场宇宙表更新触发器
CREATE TRIGGER IF NOT EXISTS tr_market_universes_updated_at AFTER
UPDATE ON market_universes FOR EACH ROW BEGIN
UPDATE market_universes
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;

-- 工具类型表更新触发器
CREATE TRIGGER IF NOT EXISTS tr_instrument_types_updated_at AFTER
UPDATE ON instrument_types FOR EACH ROW BEGIN
UPDATE instrument_types
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;

-- 数据集类别表更新触发器
CREATE TRIGGER IF NOT EXISTS tr_dataset_categories_updated_at AFTER
UPDATE ON dataset_categories FOR EACH ROW BEGIN
UPDATE dataset_categories
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;

-- 延迟配置表更新触发器
CREATE TRIGGER IF NOT EXISTS tr_delay_config_updated_at AFTER
UPDATE ON delay_config FOR EACH ROW BEGIN
UPDATE delay_config
SET
    updated_at = CURRENT_TIMESTAMP
WHERE
    id = NEW.id;

END;