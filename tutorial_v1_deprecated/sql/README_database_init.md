# WQ 项目数据库初始化脚本使用说明

## 📋 脚本概述

`init_database.sql` 是 WQ 项目的完整数据库初始化脚本，基于 `xin/config.py` 中的配置进行设计。

## ⚠️ 重要警告

**此脚本会完全清除现有数据库中的所有表、视图、索引、触发器和数据！**

执行前请务必：

1. 备份重要数据
2. 确认当前环境允许数据清除
3. 仔细阅读本说明文档

## 🔄 脚本执行流程

### 第一阶段：清理现有结构

1. **删除触发器** - 清除所有自动更新时间戳的触发器
2. **删除视图** - 清除所有统计和查询视图
3. **删除索引** - 清除所有性能优化索引
4. **删除表** - 按外键依赖关系逆序删除所有业务表

### 第二阶段：重新创建结构

1. **创建基础表** - 用户、数据集、配置等核心表
2. **创建关联表** - 包含外键关系的业务表
3. **创建索引** - 性能优化索引
4. **创建视图** - 业务查询和统计视图
5. **创建触发器** - 自动时间戳更新机制
6. **插入初始数据** - 基于 xin/config.py 的配置数据

## 📊 数据库结构概览

### 核心业务表

- `user_accounts` - 用户账户信息
- `datasets` - 数据集定义
- `account_dataset_usage` - 账户数据集使用记录
- `account_alpha_factors` - 账户 Alpha 因子记录
- `alpha_digging_process` - 因子挖掘过程记录

### 配置管理表

- `regions` - 地区配置（支持地区-工具类型组合）
- `market_universes` - 市场宇宙配置（基于 UNIVERSE_DICT）
- `instrument_types` - 工具类型配置（EQUITY, CRYPTO）
- `dataset_categories` - 数据集类别配置（16 个类别）
- `delay_config` - 延迟配置（0 天、1 天）
- `url_config` - API URL 配置

### 统计视图

- `v_region_summary` - 地区市场概览
- `v_market_universe_overview` - 市场宇宙统计
- `v_config_stats` - 配置数据统计
- `v_alpha_digging_stats` - 挖掘过程统计
- `v_alpha_digging_success_rate` - 挖掘成功率分析

## 🚀 执行方式

### SQLite 执行

```bash
# 进入项目目录
cd /path/to/wq

# 执行初始化脚本（会创建/重置数据库）
sqlite3 database.db < sql/init_database.sql
```

### 其他数据库

```bash
# MySQL
mysql -u username -p database_name < sql/init_database.sql

# PostgreSQL
psql -U username -d database_name -f sql/init_database.sql
```

## 📈 配置数据统计

执行脚本后将包含以下配置数据：

| 配置类型   | 数量  | 说明                                |
| ---------- | ----- | ----------------------------------- |
| 工具类型   | 2 个  | EQUITY, CRYPTO                      |
| 数据集类别 | 16 个 | pv, fundamental, analyst 等完整类别 |
| 延迟配置   | 2 个  | 0 天、1 天延迟                      |
| 地区配置   | 11 个 | 地区-工具类型组合                   |
| 市场宇宙   | 26 个 | 精确的市场配置映射                  |
| URL 配置   | 10 个 | 完整的 API 端点配置                 |

## ✅ 执行后验证

执行以下查询验证初始化是否成功：

```sql
-- 查看配置数据统计
SELECT * FROM v_config_stats;

-- 查看地区市场概览
SELECT * FROM v_region_summary;

-- 查看市场宇宙概览
SELECT * FROM v_market_universe_overview;

-- 验证用户账户
SELECT email, name FROM user_accounts;
```

## 🔧 故障排除

### 常见问题

1. **外键约束错误** - 确保按脚本顺序执行，不要单独执行部分语句
2. **权限不足** - 确保数据库用户有 CREATE、DROP 权限
3. **表已存在错误** - 脚本已包含 IF EXISTS 检查，正常情况不会出现

### 手动清理

如果需要手动清理特定对象：

```sql
-- 查看所有表
.tables  -- SQLite
SHOW TABLES;  -- MySQL

-- 清理特定表
DROP TABLE IF EXISTS table_name;
```

## 📝 维护说明

- 修改配置时请同步更新 `xin/config.py` 和此脚本
- 新增配置项时请添加对应的索引和触发器
- 重大结构变更时请更新本文档

---

**最后更新时间：2024 年**  
**适用版本：WQ 项目 v1.0+**
