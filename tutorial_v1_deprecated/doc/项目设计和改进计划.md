# WQ 项目整体设计和改进计划

## 📋 项目概述

本项目的核心目标是高效挖掘因子，通过重构优化现有的多脚本分散式架构，建立统一的、容错性强的因子挖掘系统。

## 🔍 现状分析

### XIN 目录（旧架构）分析

#### 📁 现有脚本功能详解

##### 1. 第一轮挖掘 (digging_1step.py)

**功能特性**:

- 基于数据集字段生成一阶因子表达式
- 异步并发提交因子进行回测，支持并发控制
- 支持断点续传机制，避免重复计算
- 使用文件记录已完成的因子状态
- 灵活的参数配置（region, universe, delay 等）

##### 2. 第二轮挖掘 (digging_2step.py)

**功能特性**:

- 基于第一轮结果生成二阶因子
- 根据 turnover 智能调整 decay 参数
- 支持多种组合操作生成复杂因子
- 渐进式优化策略，提升因子质量

##### 3. 因子检查 (check.py)

**功能特性**:

- 自动检查因子的自相关性和产品相关性
- 支持多线程并发检查
- 生成可提交因子列表
- 多维度质量评估机制

##### 4. 第三阶段挖掘 (digging_3step.py)

**功能特性**:

- 基于第二阶段结果生成交易条件因子
- 使用 trade_when_factory 创建复杂的交易逻辑
- 支持异步并发处理，可控制并发数量
- SessionManager 会话管理和自动刷新机制
- 断点续传：读取已完成的 alpha 表达式避免重复处理
- 动态配置：从 user_info.txt 读取 dataset_id
- 多参数支持：region、universe、delay、decay 等

##### 5. 智能提交系统 (s7.py)

**功能特性**:

- 30 分钟超时的智能提交机制
- 持续监控模式：自动检测 submitable_alpha.csv 的新因子
- 按自相关性排序优化提交顺序
- 服务器超时自动重连和会话管理
- 详细的提交统计和进度报告
- 错误分类处理：403 永久拒绝、408 超时重试
- 实时状态显示和累计成功率统计

##### 6. 传统提交 (submit_alpha.py)

**功能特性**:

- 批量提交通过检查的因子
- 重试机制处理提交失败
- 状态跟踪和结果记录
- 自动化提交流程

#### ✅ 优点

1. **功能完整性**：包含完整的 1-2-3 阶段因子挖掘流程
2. **异步并发**：使用 aiohttp 实现高效的并发模拟
3. **断点续传**：支持从中断处继续执行
4. **质量检测**：有自相关性和生产相关性检测机制
5. **自动提交**：支持批量因子提交功能
6. **丰富字段**：包含多种推荐字段和算子组合
7. **智能参数调整**：decay 参数根据 turnover 自动调整
8. **多维度评估**：自相关性和产品相关性双重检测
9. **交易条件因子**：第三阶段支持复杂的 trade_when 交易逻辑
10. **会话管理**：SessionManager 自动处理会话过期和刷新
11. **智能提交**：30 分钟超时机制和持续监控模式
12. **错误分类**：针对不同错误类型采用不同处理策略
13. **实时监控**：详细的进度报告和统计信息
14. **优化排序**：按自相关性排序提高提交成功率

#### ❌ 存在问题

##### 🏗️ 架构层面问题

1. **多脚本独立运行**：需要手动协调 digging_1step.py → digging_2step.py → digging_3step.py → check.py → s7.py/submit_alpha.py 的执行顺序
2. **状态管理分散**：各个阶段的状态信息分散在不同的 txt/csv 文件中
3. **缺乏统一调度**：无法实现自动化的端到端流程，需要人工干预

##### 💾 数据存储问题

4. **文件存储临时数据**：使用 txt/csv 文件存储状态，缺乏结构化管理
5. **数据一致性风险**：多个进程同时读写文件可能导致数据不一致
6. **查询效率低**：无法高效查询和统计历史数据

##### 🛡️ 容错性问题

7. **异常捕获不完善**：网络异常、API 限制、会话过期等错误处理不够完善
8. **恢复机制简单**：异常中断后需要手工分析和恢复，缺乏自动重试机制
9. **会话管理脆弱**：会话过期处理机制不够健壮
10. **第三阶段依赖性**：digging_3step.py 严重依赖第二阶段结果，无数据时会阻塞
11. **提交超时处理**：s7.py 虽有 30 分钟超时但缺乏智能退避策略

##### 📊 监控统计问题

10. **缺乏实时监控**：无法实时了解挖掘进度和各阶段处理状态
11. **统计信息缺失**：缺乏整体的性能统计、成功率分析和资源利用情况
12. **资源使用不透明**：无法合理分配和调整并发数、请求频率等资源参数

##### 📱 通知机制问题

13. **通知功能有限**：只有基础的微信通知，缺乏多渠道通知支持
14. **事件驱动不足**：缺乏基于事件的自动通知机制（如高质量因子发现、系统异常等）
15. **提交结果通知缺失**：s7.py 虽有统计但缺乏及时的成功/失败通知

##### 👥 多账户管理问题

15. **单账户限制**：只支持单个账户执行，无法利用多账户并行处理
16. **负载均衡缺失**：无法在多账户间智能分配任务负载

##### 🎯 参数优化问题

17. **缺乏优化反馈**：没有记录各账户执行情况和参数效果，无法进行数据驱动的调优
18. **参数配置静态**：decay、delay 等参数配置固定，缺乏自适应调整机制

### SRC 目录（新架构）分析

#### ✅ 已实现功能

1. **统一数据存储**：基于 Peewee ORM 的 SQLite 数据库
2. **完善数据模型**：用户、数据集、因子等完整模型设计
3. **配置管理**：数据库存储的配置信息，支持动态读取
4. **日志系统**：基于 rich 的美观日志输出
5. **会话管理**：统一的登录认证和会话管理
6. **模块化设计**：清晰的模块分离和依赖注入

#### 🚧 需要补充

1. **因子挖掘核心业务逻辑**
   - 迁移 digging_1step.py 的一阶因子生成逻辑
   - 迁移 digging_2step.py 的二阶因子组合逻辑
   - 迁移 digging_3step.py 的交易条件因子生成逻辑
2. **多阶段挖掘流程管理**
   - 统一的 1-2-3 阶段流程控制
   - 阶段间自动晋级和依赖管理
3. **队列和状态管理系统**
4. **异步任务处理框架**
5. **统计和监控功能**
6. **事件通知系统**
7. **多账户管理和调度系统**
8. **参数优化和执行分析引擎**
9. **智能提交系统**
   - 迁移 s7.py 的智能提交和监控逻辑
   - 优化超时处理和重试机制
10. **会话管理增强**
    - 统一的会话生命周期管理
    - 自动刷新和故障恢复机制

## 🎯 设计目标

### 核心设计原则

1. **统一入口**：一个程序处理所有任务阶段
2. **数据驱动**：基于数据库的队列和状态管理
3. **容错优先**：强大的异常处理和自动恢复机制
4. **可观测性**：全面的统计监控和事件通知
5. **模块化**：清晰的模块分离，便于维护和扩展

### 系统架构目标

- **队列系统**：因子在数据库中按状态和标签分类管理
- **工作进程**：独立的多阶段处理器作为 Worker 运行
- **状态管理**：因子从生成 → 模拟 → 检测 → 提交的完整状态跟踪
- **容错机制**：异常自动捕捉、重试和恢复
- **统计监控**：实时了解处理进度和资源分配
- **事件通知**：重要事件的及时提醒机制
- **多账户协同**：多个账户并行处理，智能负载均衡和故障转移
- **参数优化**：基于历史执行数据的自动参数调优和性能提升

## 🏗️ 技术架构设计

### 数据模型扩展

```python
# 核心队列模型 - 基于现有文件系统分析设计
class AlphaFactorQueueModel(BaseModel):
    """因子队列模型 - 核心队列管理"""

    # 基础信息
    factor_expression = TextField()                    # 因子表达式
    source_dataset = CharField(max_length=50)          # 来源数据集 (analyst4, news18等)

    # 队列管理
    stage = CharField(max_length=20, index=True)       # 'stage1', 'stage2', 'stage3', 'check', 'submit'
    status = CharField(max_length=20, index=True)      # 'pending', 'processing', 'completed', 'failed', 'skipped'
    priority = IntegerField(default=0, index=True)     # 优先级 (0-100, 数值越大越优先)

    # 处理参数
    region = CharField(max_length=10, default='USA')   # 地区
    universe = CharField(max_length=20, default='TOP3000')  # 股票池
    delay = IntegerField(default=1)                    # 延迟天数
    decay = IntegerField(default=6)                    # 衰减参数
    neutralization = CharField(max_length=50, default='SUBINDUSTRY')  # 中性化

    # 标签和分类
    tags = TextField(null=True)                        # JSON格式的标签列表
    batch_id = CharField(max_length=100, null=True, index=True)  # 批次ID

    # 错误处理
    retry_count = IntegerField(default=0)              # 重试次数
    max_retries = IntegerField(default=3)              # 最大重试次数
    last_error = TextField(null=True)                  # 最后一次错误信息
    error_type = CharField(max_length=50, null=True)   # 错误类型分类

    # 处理结果
    alpha_id = CharField(max_length=100, null=True, index=True)  # WQ平台返回的Alpha ID
    sharpe = FloatField(null=True)                     # 夏普比率
    fitness = FloatField(null=True)                    # 适应度
    turnover = FloatField(null=True)                   # 换手率

    # 时间戳
    submitted_at = DateTimeField(null=True)            # 提交到队列时间
    started_at = DateTimeField(null=True)              # 开始处理时间
    completed_at = DateTimeField(null=True)            # 完成处理时间

    class Meta:
        indexes = (
            (('stage', 'status', 'priority'), False),  # 组合索引用于队列查询
            (('source_dataset', 'stage'), False),      # 数据集阶段索引
        )

class AlphaProcessingLogModel(BaseModel):
    """因子处理日志模型 - 详细的处理记录"""

    factor_queue = ForeignKeyField(AlphaFactorQueueModel, backref='processing_logs')
    operation = CharField(max_length=50)               # 'simulate', 'check', 'submit'
    status = CharField(max_length=20)                  # 'started', 'completed', 'failed'

    # 处理详情
    worker_id = CharField(max_length=100, null=True)   # 处理器标识
    session_id = CharField(max_length=100, null=True)  # 会话ID

    # 性能指标
    duration = FloatField(null=True)                   # 处理耗时(秒)
    cpu_usage = FloatField(null=True)                  # CPU使用率
    memory_usage = FloatField(null=True)               # 内存使用量

    # 错误信息
    error_message = TextField(null=True)               # 错误详情
    stack_trace = TextField(null=True)                 # 堆栈信息

    # 请求响应
    request_data = TextField(null=True)                # 请求数据(JSON)
    response_data = TextField(null=True)               # 响应数据(JSON)

class AlphaBatchModel(BaseModel):
    """批次管理模型 - 批量处理管理"""

    batch_id = CharField(max_length=100, unique=True, index=True)
    batch_name = CharField(max_length=200)             # 批次描述名称

    # 批次配置
    source_dataset = CharField(max_length=50)          # 来源数据集
    stage = CharField(max_length=20)                   # 目标阶段
    total_factors = IntegerField(default=0)            # 总因子数量

    # 进度统计
    pending_count = IntegerField(default=0)            # 待处理数量
    processing_count = IntegerField(default=0)         # 处理中数量
    completed_count = IntegerField(default=0)          # 已完成数量
    failed_count = IntegerField(default=0)             # 失败数量

    # 时间管理
    started_at = DateTimeField(null=True)              # 批次开始时间
    estimated_completion = DateTimeField(null=True)    # 预计完成时间
    completed_at = DateTimeField(null=True)            # 实际完成时间

    # 状态管理
    status = CharField(max_length=20, default='pending')  # 'pending', 'running', 'completed', 'failed'

    class Meta:
        indexes = (
            (('source_dataset', 'stage', 'status'), False),
        )

class SystemStatsModel(BaseModel):
    """系统统计模型 - 性能监控数据"""

    # 统计分类
    stat_type = CharField(max_length=50, index=True)   # 'hourly', 'daily', 'worker_performance'
    stat_key = CharField(max_length=100, index=True)   # 具体指标键
    stat_value = TextField()                           # JSON格式的统计值

    # 时间维度
    period_start = DateTimeField(index=True)           # 统计周期开始
    period_end = DateTimeField(index=True)             # 统计周期结束

    # 元数据
    metadata = TextField(null=True)                    # 额外的元数据信息

    class Meta:
        indexes = (
            (('stat_type', 'period_start'), False),    # 时间序列查询
            (('stat_key', 'period_start'), False),     # 指标时间查询
        )

class UserAccountModel(BaseModel):
    """用户账户模型 - 支持多账户管理"""
    username = CharField(unique=True)
    password = CharField()  # 加密存储
    status = CharField(max_length=20)  # 'active', 'disabled', 'error'
    last_login = DateTimeField(null=True)
    error_count = IntegerField(default=0)
    max_concurrent_tasks = IntegerField(default=5)
    preferred_datasets = TextField(null=True)  # JSON存储偏好数据集
    created_at = DateTimeField()
    updated_at = DateTimeField()

class AccountExecutionLogModel(BaseModel):
    """账户执行记录模型 - 用于参数调优"""
    account = ForeignKeyField(UserAccountModel)
    date = DateField()
    dataset_name = CharField(max_length=100)
    stage = CharField(max_length=20)
    factors_processed = IntegerField(default=0)
    factors_submitted = IntegerField(default=0)
    success_rate = FloatField(default=0.0)
    avg_response_time = FloatField(default=0.0)
    error_count = IntegerField(default=0)
    parameters_config = TextField()  # JSON存储参数配置
    performance_score = FloatField(default=0.0)

class ParameterOptimizationModel(BaseModel):
    """参数优化记录模型"""
    account = ForeignKeyField(UserAccountModel)
    parameter_set = TextField()  # JSON存储参数组合
    test_period_start = DateTimeField()
    test_period_end = DateTimeField()
    total_factors = IntegerField()
    success_factors = IntegerField()
    optimization_score = FloatField()
    is_optimal = BooleanField(default=False)
    notes = TextField(null=True)
```

### 核心组件设计

#### 0. 简化日志系统

```python
import logging
from datetime import datetime
from typing import Dict, Any, Optional
from rich.console import Console
from rich.table import Table

class SimpleLogger:
    """简化的处理日志系统"""

    def __init__(self, name: str, quiet: bool = False):
        self.name = name
        self.quiet = quiet
        self.console = Console() if not quiet else None
        self.file_logger = self._setup_file_logger()
        self.stats = {'processed': 0, 'succeeded': 0, 'failed': 0}

    def _setup_file_logger(self) -> logging.Logger:
        """设置文件日志"""
        logger = logging.getLogger(f"wq_{self.name}")
        logger.setLevel(logging.INFO)

        handler = logging.FileHandler(f"logs/wq_{datetime.now().strftime('%Y%m%d')}.log", encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        handler.setFormatter(formatter)
        logger.addHandler(handler)

        return logger

    def info(self, message: str, factor_id: int = None):
        """记录信息"""
        log_msg = f"[{self.name}] {message}"
        if factor_id:
            log_msg = f"[{self.name}] Factor #{factor_id}: {message}"

        self.file_logger.info(log_msg)

        if not self.quiet:
            if factor_id:
                self.console.print(f"  📊 因子 #{factor_id}: [cyan]{message}[/cyan]")
            else:
                self.console.print(f"🚀 {log_msg}")

    def success(self, factor_id: int, result: Dict, duration: float):
        """记录成功"""
        self.stats['processed'] += 1
        self.stats['succeeded'] += 1

        result_text = f"({duration:.2f}s)"
        if 'sharpe' in result:
            result_text += f" Sharpe={result['sharpe']:.2f}"
        if 'fitness' in result:
            result_text += f" Fitness={result['fitness']:.2f}"

        self.file_logger.info(f"Factor {factor_id} succeeded in {duration:.2f}s - {result}")

        if not self.quiet:
            self.console.print(f"  ✅ 因子 #{factor_id} 成功 [green]{result_text}[/green]")

    def error(self, factor_id: int, error: str, duration: float):
        """记录错误"""
        self.stats['processed'] += 1
        self.stats['failed'] += 1

        self.file_logger.error(f"Factor {factor_id} failed in {duration:.2f}s - {error}")

        if not self.quiet:
            self.console.print(f"  ❌ 因子 #{factor_id} 失败 [red]({duration:.2f}s) {error}[/red]")

    def batch_summary(self, batch_size: int, duration: float):
        """批次总结"""
        success_rate = self.stats['succeeded'] / self.stats['processed'] if self.stats['processed'] > 0 else 0

        summary = f"批次完成: {self.stats['succeeded']}/{batch_size} 成功 ({success_rate:.1%}) 耗时 {duration:.1f}s"
        self.file_logger.info(summary)

        if not self.quiet:
            table = Table(title=f"{self.name} 批次摘要")
            table.add_column("指标", style="cyan")
            table.add_column("数值", style="green")
            table.add_row("成功/总数", f"{self.stats['succeeded']}/{batch_size}")
            table.add_row("成功率", f"{success_rate:.1%}")
            table.add_row("总耗时", f"{duration:.1f}s")
            self.console.print(table)

    def queue_status(self, stats: Dict):
        """显示队列状态"""
        if self.quiet:
            return

        table = Table(title="📋 队列状态")
        table.add_column("阶段", style="cyan")
        table.add_column("待处理", style="yellow")
        table.add_column("已完成", style="green")
        table.add_column("失败", style="red")

        for stage, data in stats.items():
            table.add_row(
                stage.upper(),
                str(data.get('pending', 0)),
                str(data.get('completed', 0)),
                str(data.get('failed', 0))
            )

        self.console.print(table)
```

#### 1. 统一任务调度系统

```python
from enum import Enum
from typing import Dict, List, Optional
import asyncio

class FactorStatus(Enum):
    """因子状态枚举"""
    PENDING = "pending"           # 待处理
    PROCESSING = "processing"     # 处理中
    COMPLETED = "completed"       # 已完成
    FAILED = "failed"            # 失败
    CHECKING = "checking"        # 检查中
    SUBMITTABLE = "submittable"  # 可提交
    SUBMITTED = "submitted"      # 已提交

# 注意：FactorDiggingScheduler的完整ML增强设计见后文"统一调度器ML集成设计"部分
```

#### 2. 队列管理器 (AlphaQueue)

```python
class AlphaQueue:
    """因子队列管理器"""

    def __init__(self):
        self.db = get_database()

    def add_factors(self, expressions: List[str], stage: str, tags: List[str],
                   account: Optional[UserAccountModel] = None) -> List[int]:
        """批量添加因子到队列"""
        factor_ids = []
        with self.db.atomic():
            for expr in expressions:
                factor = AlphaFactorQueueModel.create(
                    factor_expression=expr,
                    stage=stage,
                    status=FactorStatus.PENDING.value,
                    tags=','.join(tags) if tags else None,
                    assigned_account=account,
                    created_at=datetime.now()
                )
                factor_ids.append(factor.id)
        return factor_ids

    def get_pending_factors(self, stage: str, limit: int = 50,
                          account: Optional[UserAccountModel] = None) -> List[AlphaFactorQueueModel]:
        """获取待处理因子"""
        query = (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.stage == stage,
                    AlphaFactorQueueModel.status == FactorStatus.PENDING.value
                ))

        if account:
            query = query.where(AlphaFactorQueueModel.assigned_account == account)

        return list(query.limit(limit))

    def update_factor_status(self, factor_id: int, status: str,
                           error: str = None, performance_data: Dict = None):
        """更新因子状态"""
        update_data = {
            'status': status,
            'updated_at': datetime.now()
        }

        if error:
            update_data['last_error'] = error
            update_data['retry_count'] = AlphaFactorQueueModel.retry_count + 1

        if performance_data:
            update_data['performance_data'] = json.dumps(performance_data)

        AlphaFactorQueueModel.update(**update_data).where(
            AlphaFactorQueueModel.id == factor_id
        ).execute()

         def add_factors_batch(self,
                          expressions: List[str],
                          source_dataset: str,
                          stage: str,
                          tags: List[str] = None,
                          priority: int = 0,
                          **processing_params) -> str:
         """批量添加因子到队列"""

         batch_id = f"{source_dataset}_{stage}_{int(time.time())}"
         tags_json = json.dumps(tags) if tags else None

         # 创建批次记录
         batch = AlphaBatchModel.create(
             batch_id=batch_id,
             batch_name=f"{source_dataset} {stage} batch",
             source_dataset=source_dataset,
             stage=stage,
             total_factors=len(expressions),
             pending_count=len(expressions)
         )

         # 批量创建因子记录
         factors = []
         for expr in expressions:
             factors.append(AlphaFactorQueueModel(
                 factor_expression=expr,
                 source_dataset=source_dataset,
                 stage=stage,
                 status='pending',
                 priority=priority,
                 tags=tags_json,
                 batch_id=batch_id,
                 submitted_at=datetime.now(),
                 **processing_params
             ))

         # 使用批量插入提高性能
         AlphaFactorQueueModel.bulk_create(factors, batch_size=100)

         logger.info(f"添加了 {len(expressions)} 个因子到队列 (批次: {batch_id})")
         return batch_id

     def get_pending_factors(self, stage: str = None, limit: int = 50,
                           dataset: str = None, account: Optional[UserAccountModel] = None) -> List[AlphaFactorQueueModel]:
         """获取待处理因子 - 优化版本"""
         query = (AlphaFactorQueueModel
                 .select()
                 .where(AlphaFactorQueueModel.status == 'pending'))

         if stage:
             query = query.where(AlphaFactorQueueModel.stage == stage)

         if dataset:
             query = query.where(AlphaFactorQueueModel.source_dataset == dataset)

         if account:
             query = query.where(AlphaFactorQueueModel.assigned_account == account)

         # 按优先级和提交时间排序
         query = query.order_by(
             AlphaFactorQueueModel.priority.desc(),
             AlphaFactorQueueModel.submitted_at.asc()
         )

         return list(query.limit(limit))

     def update_factor_status(self, factor_id: int, status: str,
                            alpha_id: str = None, error: str = None, **result_data):
         """更新因子状态 - 增强版本"""

         factor = AlphaFactorQueueModel.get_by_id(factor_id)

         # 更新基本状态
         factor.status = status

         if status == 'processing':
             factor.started_at = datetime.now()
         elif status in ['completed', 'failed']:
             factor.completed_at = datetime.now()

         # 更新结果数据
         if alpha_id:
             factor.alpha_id = alpha_id

         if error:
             factor.last_error = error
             factor.error_type = self._classify_error(error)
             factor.retry_count += 1

         # 更新其他结果数据 (sharpe, fitness, turnover等)
         for key, value in result_data.items():
             if hasattr(factor, key):
                 setattr(factor, key, value)

         factor.save()

         # 更新批次统计
         self._update_batch_stats(factor.batch_id)

         # 记录处理日志
         self._log_processing(factor, status, error)

     def get_queue_stats(self) -> Dict[str, Any]:
         """获取队列统计信息 - 完整版本"""

         stats = {}

         # 按状态统计
         status_stats = (AlphaFactorQueueModel
                        .select(AlphaFactorQueueModel.status, fn.COUNT().alias('count'))
                        .group_by(AlphaFactorQueueModel.status))

         stats['by_status'] = {stat.status: stat.count for stat in status_stats}

         # 按阶段统计
         stage_stats = (AlphaFactorQueueModel
                       .select(AlphaFactorQueueModel.stage, fn.COUNT().alias('count'))
                       .group_by(AlphaFactorQueueModel.stage))

         stats['by_stage'] = {stat.stage: stat.count for stat in stage_stats}

         # 按数据集统计
         dataset_stats = (AlphaFactorQueueModel
                         .select(AlphaFactorQueueModel.source_dataset, fn.COUNT().alias('count'))
                         .group_by(AlphaFactorQueueModel.source_dataset))

         stats['by_dataset'] = {stat.source_dataset: stat.count for stat in dataset_stats}

         # 批次统计
         batch_stats = (AlphaBatchModel
                       .select(AlphaBatchModel.status, fn.COUNT().alias('count'))
                       .group_by(AlphaBatchModel.status))

         stats['by_batch_status'] = {stat.status: stat.count for stat in batch_stats}

         return stats

     def cleanup_old_records(self, days: int = 7):
         """清理旧的已完成记录"""

         cutoff_date = datetime.now() - timedelta(days=days)

         # 删除旧的已完成记录
         deleted_count = (AlphaFactorQueueModel
                         .delete()
                         .where(
                             AlphaFactorQueueModel.status == 'completed',
                             AlphaFactorQueueModel.completed_at < cutoff_date
                         )
                         .execute())

         logger.info(f"清理了 {deleted_count} 条旧记录")
         return deleted_count

     def _update_batch_stats(self, batch_id: str):
         """更新批次统计"""
         if not batch_id:
             return

         # 重新计算批次统计
         stats = (AlphaFactorQueueModel
                 .select(AlphaFactorQueueModel.status, fn.COUNT().alias('count'))
                 .where(AlphaFactorQueueModel.batch_id == batch_id)
                 .group_by(AlphaFactorQueueModel.status))

         status_counts = {stat.status: stat.count for stat in stats}

         # 更新批次记录
         batch = AlphaBatchModel.get(AlphaBatchModel.batch_id == batch_id)
         batch.pending_count = status_counts.get('pending', 0)
         batch.processing_count = status_counts.get('processing', 0)
         batch.completed_count = status_counts.get('completed', 0)
         batch.failed_count = status_counts.get('failed', 0)

         # 更新批次状态
         if batch.completed_count + batch.failed_count == batch.total_factors:
             batch.status = 'completed'
             batch.completed_at = datetime.now()
         elif batch.processing_count > 0:
             batch.status = 'running'

         batch.save()

     def _log_processing(self, factor: AlphaFactorQueueModel, status: str, error: str = None):
         """记录处理日志"""

         AlphaProcessingLogModel.create(
             factor_queue=factor,
             operation=factor.stage,
             status=status,
             error_message=error,
             duration=self._calculate_duration(factor)
         )

     def _calculate_duration(self, factor: AlphaFactorQueueModel) -> float:
         """计算处理耗时"""
         if factor.started_at and factor.completed_at:
             return (factor.completed_at - factor.started_at).total_seconds()
         return None

     def _classify_error(self, error: str) -> str:
         """错误类型分类"""
         if 'timeout' in error.lower():
             return 'timeout'
         elif 'rate limit' in error.lower():
             return 'rate_limit'
         elif 'session' in error.lower():
             return 'session_error'
         elif 'network' in error.lower():
             return 'network_error'
         else:
             return 'unknown'
```

#### 3. 增强的 API 客户端和容错机制

```python
class RetryConfig:
    """重试配置类"""
    def __init__(self, max_retries=5, backoff_factor=2, max_backoff=300,
                 status_forcelist=None, method_whitelist=None):
        self.max_retries = max_retries
        self.backoff_factor = backoff_factor
        self.max_backoff = max_backoff
        self.status_forcelist = status_forcelist or [429, 500, 502, 503, 504]
        self.method_whitelist = method_whitelist or ['POST', 'GET', 'PUT']

class CircuitBreaker:
    """熔断器模式实现"""
    def __init__(self, failure_threshold=5, recovery_timeout=300, expected_exception=None):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.expected_exception = expected_exception or Exception
        self.failure_count = 0
        self.last_failure_time = None
        self.state = 'CLOSED'  # CLOSED, OPEN, HALF_OPEN

    def is_open(self):
        if self.state == 'OPEN':
            if time.time() - self.last_failure_time > self.recovery_timeout:
                self.state = 'HALF_OPEN'
                return False
            return True
        return False

    def record_success(self):
        self.failure_count = 0
        self.state = 'CLOSED'

    def record_failure(self):
        self.failure_count += 1
        self.last_failure_time = time.time()
        if self.failure_count >= self.failure_threshold:
            self.state = 'OPEN'

def retry_with_exponential_backoff(max_retries=5, backoff_factor=2, max_backoff=300):
    """指数退避重试装饰器"""
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            for attempt in range(max_retries + 1):
                try:
                    return await func(*args, **kwargs)
                except Exception as e:
                    if attempt == max_retries:
                        raise

                    # 计算退避时间
                    backoff_time = min(backoff_factor ** attempt, max_backoff)
                    jitter = random.uniform(0.1, 0.3) * backoff_time  # 添加抖动
                    wait_time = backoff_time + jitter

                    logger.warning(f"重试 {attempt + 1}/{max_retries}，等待 {wait_time:.2f}s: {e}")
                    await asyncio.sleep(wait_time)

            return None
        return wrapper
    return decorator

class RobustAPIClient:
    """增强的API客户端 - 具备完善的容错机制"""

    def __init__(self):
        self.session_manager = SessionManager()
        self.retry_config = RetryConfig(
            max_retries=5,
            backoff_factor=2,
            status_forcelist=[429, 500, 502, 503, 504]
        )
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=5,
            recovery_timeout=300,
            expected_exception=aiohttp.ClientError
        )
        self.request_cache = RequestCache()  # 添加请求缓存

    @retry_with_exponential_backoff(max_retries=5)
    async def robust_request(self, endpoint: str, data: Dict,
                           account: UserAccountModel, method: str = 'POST') -> Dict:
        """带重试和熔断的API请求"""

        # 检查缓存（对于GET请求）
        if method == 'GET':
            cached_result = await self.request_cache.get(endpoint, data)
            if cached_result:
                return cached_result

        try:
            if self.circuit_breaker.is_open():
                raise CircuitBreakerOpenError("API circuit breaker is open")

            session = await self.session_manager.get_session(account)

            # 根据方法类型发送请求
            if method == 'POST':
                response = await session.post(endpoint, json=data, timeout=30)
            elif method == 'GET':
                response = await session.get(endpoint, params=data, timeout=30)
            else:
                raise ValueError(f"不支持的HTTP方法: {method}")

            # 检查响应状态
            if response.status in self.retry_config.status_forcelist:
                raise aiohttp.ClientResponseError(
                    request_info=response.request_info,
                    history=response.history,
                    status=response.status
                )

            result = await response.json()
            self.circuit_breaker.record_success()

            # 缓存GET请求结果
            if method == 'GET':
                await self.request_cache.set(endpoint, data, result, ttl=300)

            return result

        except asyncio.TimeoutError as e:
            await self.handle_timeout_error(e, endpoint, account)
            raise
        except aiohttp.ClientResponseError as e:
            if e.status == 401:  # 会话过期
                await self.handle_session_expired(account)
            await self.handle_http_error(e, endpoint, account)
            raise
        except Exception as e:
            self.circuit_breaker.record_failure()
            await self.handle_api_error(e, endpoint, data, account)
            raise

    async def handle_session_expired(self, account: UserAccountModel):
        """处理会话过期"""
        logger.warning(f"账户 {account.username} 会话过期，重新登录")
        await self.session_manager.refresh_session(account)

    async def handle_timeout_error(self, error: asyncio.TimeoutError,
                                 endpoint: str, account: UserAccountModel):
        """处理超时错误"""
        logger.error(f"API请求超时: {endpoint}, 账户: {account.username}")
        # 记录超时统计
        await self.metrics_collector.record_timeout(endpoint, account)

    async def handle_http_error(self, error: aiohttp.ClientResponseError,
                              endpoint: str, account: UserAccountModel):
        """处理HTTP错误"""
        error_info = {
            'status': error.status,
            'endpoint': endpoint,
            'account': account.username,
            'message': str(error)
        }
        logger.error(f"HTTP错误: {error_info}")

        # 根据错误状态码分类处理
        if error.status == 429:  # 限流
            await self.handle_rate_limit(account)
        elif error.status in [500, 502, 503, 504]:  # 服务器错误
            await self.handle_server_error(error, account)

    async def handle_rate_limit(self, account: UserAccountModel):
        """处理限流"""
        logger.warning(f"账户 {account.username} 遇到限流，暂停请求")
        account.last_rate_limit = datetime.now()
        account.save()

    async def handle_server_error(self, error: aiohttp.ClientResponseError,
                                account: UserAccountModel):
        """处理服务器错误"""
        logger.error(f"服务器错误 {error.status}，账户: {account.username}")
        # 可以考虑暂时禁用该账户

class RequestCache:
    """请求缓存系统 - 减少重复API调用"""

    def __init__(self, max_size=10000, default_ttl=300):
        self.cache = {}
        self.timestamps = {}
        self.max_size = max_size
        self.default_ttl = default_ttl
        self.hit_count = 0
        self.miss_count = 0

    def _generate_key(self, endpoint: str, data: Dict) -> str:
        """生成缓存键"""
        data_str = json.dumps(data, sort_keys=True) if data else ""
        return hashlib.md5(f"{endpoint}:{data_str}".encode()).hexdigest()

    async def get(self, endpoint: str, data: Dict) -> Optional[Dict]:
        """获取缓存数据"""
        key = self._generate_key(endpoint, data)

        if key in self.cache:
            # 检查是否过期
            if time.time() - self.timestamps[key] < self.cache[key]['ttl']:
                self.hit_count += 1
                return self.cache[key]['data']
            else:
                # 清理过期数据
                del self.cache[key]
                del self.timestamps[key]

        self.miss_count += 1
        return None

    async def set(self, endpoint: str, data: Dict, result: Dict, ttl: int = None):
        """设置缓存数据"""
        key = self._generate_key(endpoint, data)
        ttl = ttl or self.default_ttl

        # 检查缓存大小，执行LRU清理
        if len(self.cache) >= self.max_size:
            await self._evict_lru()

        self.cache[key] = {
            'data': result,
            'ttl': ttl
        }
        self.timestamps[key] = time.time()

    async def _evict_lru(self):
        """LRU清理策略"""
        if not self.timestamps:
            return

        # 找到最旧的10%数据并清理
        sorted_keys = sorted(self.timestamps.items(), key=lambda x: x[1])
        evict_count = max(1, len(sorted_keys) // 10)

        for key, _ in sorted_keys[:evict_count]:
            if key in self.cache:
                del self.cache[key]
            if key in self.timestamps:
                del self.timestamps[key]

    def get_stats(self) -> Dict:
        """获取缓存统计"""
        total_requests = self.hit_count + self.miss_count
        hit_rate = self.hit_count / total_requests if total_requests > 0 else 0

        return {
            'hit_count': self.hit_count,
            'miss_count': self.miss_count,
            'hit_rate': hit_rate,
            'cache_size': len(self.cache),
            'max_size': self.max_size
        }

class SessionManager:
    """增强的会话管理器"""

    def __init__(self):
        self.sessions = {}  # account_id -> session info
        self.session_locks = {}  # account_id -> asyncio.Lock
        self.login_semaphore = asyncio.Semaphore(3)  # 限制并发登录数

    async def get_session(self, account: UserAccountModel) -> aiohttp.ClientSession:
        """获取账户会话"""
        account_id = account.id

        # 为每个账户创建锁
        if account_id not in self.session_locks:
            self.session_locks[account_id] = asyncio.Lock()

        async with self.session_locks[account_id]:
            # 检查现有会话是否有效
            if account_id in self.sessions:
                session_info = self.sessions[account_id]
                if await self._is_session_valid(session_info, account):
                    return session_info['session']
                else:
                    await self._close_session(account_id)

            # 创建新会话
            return await self._create_new_session(account)

    async def _is_session_valid(self, session_info: Dict, account: UserAccountModel) -> bool:
        """检查会话是否仍然有效"""
        session = session_info['session']
        created_time = session_info['created_at']

        # 检查会话年龄（3小时过期）
        if time.time() - created_time > 3 * 3600:
            return False

        # 通过API调用验证会话
        try:
            async with session.get('/api/user/profile', timeout=5) as response:
                return response.status == 200
        except:
            return False

    async def _create_new_session(self, account: UserAccountModel) -> aiohttp.ClientSession:
        """创建新的会话"""
        async with self.login_semaphore:
            connector = aiohttp.TCPConnector(
                limit=20,
                limit_per_host=10,
                ttl_dns_cache=300,
                use_dns_cache=True,
            )

            session = aiohttp.ClientSession(
                connector=connector,
                timeout=aiohttp.ClientTimeout(total=30),
                headers={
                    'User-Agent': 'WQ-Factor-Mining-System/1.0',
                    'Accept': 'application/json',
                }
            )

            # 执行登录
            login_success = await self._perform_login(session, account)
            if not login_success:
                await session.close()
                raise LoginError(f"账户 {account.username} 登录失败")

            # 保存会话信息
            self.sessions[account.id] = {
                'session': session,
                'created_at': time.time(),
                'account': account
            }

            logger.info(f"为账户 {account.username} 创建新会话")
            return session

    async def _perform_login(self, session: aiohttp.ClientSession,
                           account: UserAccountModel) -> bool:
        """执行登录操作"""
        login_data = {
            'username': account.username,
            'password': self._decrypt_password(account.password)
        }

        try:
            async with session.post('/api/auth/login', json=login_data) as response:
                if response.status == 200:
                    # 更新账户最后登录时间
                    account.last_login = datetime.now()
                    account.error_count = 0
                    account.save()
                    return True
                else:
                    logger.error(f"登录失败: {response.status}")
                    return False
        except Exception as e:
            logger.error(f"登录异常: {e}")
            return False

    def _decrypt_password(self, encrypted_password: str) -> str:
        """解密密码（需要实现具体的解密逻辑）"""
        # 这里应该实现实际的解密逻辑
        return encrypted_password

    async def refresh_session(self, account: UserAccountModel):
        """刷新会话"""
        account_id = account.id
        if account_id in self.sessions:
            await self._close_session(account_id)
        # 下次调用get_session时会自动创建新会话

    async def _close_session(self, account_id: int):
        """关闭会话"""
        if account_id in self.sessions:
            session_info = self.sessions[account_id]
            await session_info['session'].close()
            del self.sessions[account_id]

    async def close_all_sessions(self):
        """关闭所有会话"""
        for account_id in list(self.sessions.keys()):
            await self._close_session(account_id)
```

#### 4. 多阶段处理器 (Workers)

```python
class BaseWorker:
    """工作进程基类"""

    def __init__(self, worker_name: str, quiet: bool = False):
        self.worker_name = worker_name
        self.api_client = RobustAPIClient()
        self.queue = AlphaQueue()
        self.monitor = FactorDiggingMonitor()
        self.max_concurrent = 10
        self.logger = SimpleLogger(worker_name, quiet)

        async def process_batch(self, factors: List[AlphaFactorQueueModel],
                          accounts: List[UserAccountModel]):
        """批量处理因子"""
        if not factors:
            return

        self.logger.info(f"开始处理批次，{len(factors)} 个因子")

        semaphore = asyncio.Semaphore(self.max_concurrent)
        tasks = []
        start_time = time.time()

        for factor in factors:
            account = self.select_optimal_account(accounts, factor)
            task = self.process_single_factor(factor, account, semaphore)
            tasks.append(task)

        await asyncio.gather(*tasks, return_exceptions=True)

        duration = time.time() - start_time
        self.logger.batch_summary(len(factors), duration)

        async def process_single_factor(self, factor: AlphaFactorQueueModel,
                                  account: UserAccountModel, semaphore: asyncio.Semaphore):
        """处理单个因子"""
        start_time = time.time()

        async with semaphore:
            try:
                self.logger.info(f"开始处理 - 账户: {account.username}", factor.id)

                # 更新状态为处理中
                self.queue.update_factor_status(factor.id, FactorStatus.PROCESSING.value)

                # 调用具体的处理逻辑
                result = await self.execute_factor_processing(factor, account)

                duration = time.time() - start_time

                # 更新成功状态
                self.queue.update_factor_status(
                    factor.id,
                    FactorStatus.COMPLETED.value,
                    performance_data=result
                )

                self.logger.success(factor.id, result, duration)

                # 记录执行指标
                await self.record_execution_metrics(factor, account, result)

            except Exception as e:
                duration = time.time() - start_time
                self.logger.error(factor.id, str(e), duration)
                                 await self.handle_error(factor, account, e)
                                  account: UserAccountModel, semaphore: asyncio.Semaphore):
        """处理单个因子"""
        async with semaphore:
            try:
                # 更新状态为处理中
                self.queue.update_factor_status(factor.id, FactorStatus.PROCESSING.value)

                # 调用具体的处理逻辑
                result = await self.execute_factor_processing(factor, account)

                # 更新成功状态
                self.queue.update_factor_status(
                    factor.id,
                    FactorStatus.COMPLETED.value,
                    performance_data=result
                )

                # 记录执行指标
                await self.record_execution_metrics(factor, account, result)

            except Exception as e:
                await self.handle_error(factor, account, e)

    async def handle_error(self, factor: AlphaFactorQueueModel,
                         account: UserAccountModel, error: Exception):
        """错误处理"""
        if self.should_retry(factor, error):
            # 重试逻辑
            self.queue.update_factor_status(
                factor.id,
                FactorStatus.PENDING.value,
                error=str(error)
            )
            logger.warning(f"因子 {factor.id} 处理失败，将重试: {error}")
        else:
            # 标记为失败
            self.queue.update_factor_status(
                factor.id,
                FactorStatus.FAILED.value,
                error=str(error)
            )
            logger.error(f"因子 {factor.id} 处理失败，不再重试: {error}")

        # 记录错误统计
        await self.monitor.record_error(factor, account, error)

    async def analyze_batch_results_with_logging(self, results: List, batch_duration: float):
        """分析批次结果 - 增强日志版本"""
        success_count = 0
        error_summary = defaultdict(int)
        total_factors = len(results)

        for result in results:
            if isinstance(result, dict):
                if result.get('success', False):
                    success_count += 1
                else:
                    error_type = result.get('error_type', 'Unknown')
                    error_summary[error_type] += 1
            elif isinstance(result, Exception):
                error_summary[type(result).__name__] += 1

        # 记录批次统计
        batch_stats = {
            'total_factors': total_factors,
            'success_count': success_count,
            'failed_count': total_factors - success_count,
            'success_rate': success_count / total_factors if total_factors > 0 else 0,
            'batch_duration': batch_duration,
            'avg_duration_per_factor': batch_duration / total_factors if total_factors > 0 else 0,
            'error_summary': dict(error_summary)
        }

        # 记录到详细日志
        self.detailed_logger.process_logger.log_batch_summary(
            total_factors, success_count, batch_duration
        )

        # 如果有错误，记录错误汇总
        if error_summary:
            error_details = "; ".join([f"{error_type}: {count}" for error_type, count in error_summary.items()])
            self.detailed_logger.process_logger.log_error_detail(
                "BatchErrors",
                f"批次处理中发生的错误汇总: {error_details}",
                context={'batch_id': self.processing_stats['current_batch_id']}
            )

        # 更新全局统计
        self.processing_stats.update({
            'last_batch_success_rate': batch_stats['success_rate'],
            'last_batch_duration': batch_duration,
            'last_batch_size': total_factors
        })

        return batch_stats

    def log_worker_status(self):
        """记录Worker当前状态"""
        if hasattr(self, 'detailed_logger'):
            current_time = datetime.now()
            uptime = (current_time - self.processing_stats['start_time']).total_seconds() if self.processing_stats['start_time'] else 0

            worker_status = {
                'worker_name': self.worker_name,
                'uptime_seconds': uptime,
                'total_processed': self.processing_stats['total_processed'],
                'total_succeeded': self.processing_stats['total_succeeded'],
                'total_failed': self.processing_stats['total_failed'],
                'overall_success_rate': (
                    self.processing_stats['total_succeeded'] / self.processing_stats['total_processed']
                    if self.processing_stats['total_processed'] > 0 else 0
                ),
                'current_batch': self.processing_stats.get('current_batch_id', 'None'),
                'last_batch_success_rate': self.processing_stats.get('last_batch_success_rate', 0)
            }

            self.detailed_logger.process_logger.log_system_metrics(worker_status)

    def should_retry(self, factor: AlphaFactorQueueModel, error: Exception) -> bool:
        """判断是否应该重试"""
        if factor.retry_count >= 3:
            return False

        # 网络相关错误可以重试
        if isinstance(error, (asyncio.TimeoutError, aiohttp.ClientError)):
            return True

        # API限制错误可以重试
        if isinstance(error, APIRateLimitError):
            return True

        return False

class Step1Worker(BaseWorker):
    """第一阶段因子挖掘处理器 - 集成详细日志"""

    def __init__(self):
        super().__init__("Step1Worker")

        async def execute_factor_processing(self, factor: AlphaFactorQueueModel,
                                      account: UserAccountModel) -> Dict:
        """执行第一阶段因子处理"""

        # 构建API请求数据
        request_data = {
            'expression': factor.factor_expression,
            'universe': factor.universe or 'TOP3000',
            'region': factor.region or 'CN',
            'delay': factor.delay or 1,
            'decay': factor.decay or 9
        }

        # 调用WQ API进行回测
        response = await self.api_client.robust_request(
            '/alpha/simulation',
            request_data,
            account
        )

        # 解析结果
        performance = self.parse_simulation_result(response)

        # 判断是否符合进入第二阶段的条件
        if self.meets_step2_criteria(performance):
            # 自动添加到第二阶段队列
            await self.promote_to_step2(factor, performance)

        return performance

    def meets_step2_criteria(self, performance: Dict) -> bool:
        """判断是否符合第二阶段条件"""
        return (performance.get('sharpe', 0) > 1.5 and
                performance.get('turnover', 0) < 0.3)

class Step2Worker(BaseWorker):
    """第二阶段因子挖掘处理器"""

    async def execute_factor_processing(self, factor: AlphaFactorQueueModel,
                                      account: UserAccountModel) -> Dict:
        """执行第二阶段因子处理"""
        # 基于第一阶段结果调整decay参数
        turnover = factor.performance_data.get('turnover', 0.2)
        optimal_decay = self.calculate_optimal_decay(turnover)

        request_data = {
            'expression': factor.factor_expression,
            'universe': factor.universe or 'TOP3000',
            'region': factor.region or 'CN',
            'delay': factor.delay or 1,
            'decay': optimal_decay
        }

        response = await self.api_client.robust_request(
            '/alpha/simulation',
            request_data,
            account
        )

        performance = self.parse_simulation_result(response)

        # 判断是否可以进入第三阶段
        if self.meets_step3_criteria(performance):
            await self.promote_to_step3(factor, performance)

        return performance

    def calculate_optimal_decay(self, turnover: float) -> int:
        """根据turnover计算最优decay"""
        if turnover < 0.1:
            return 15
        elif turnover < 0.2:
            return 12
        elif turnover < 0.3:
            return 9
        else:
            return 6

    def meets_step3_criteria(self, performance: Dict) -> bool:
        """判断是否符合第三阶段条件"""
        return (performance.get('sharpe', 0) > 1.0 and
                performance.get('fitness', 0) > 0.75)

class Step3Worker(BaseWorker):
    """第三阶段因子挖掘处理器 - 交易条件因子生成"""

    async def execute_factor_processing(self, factor: AlphaFactorQueueModel,
                                      account: UserAccountModel) -> Dict:
        """执行第三阶段因子处理 - 生成trade_when因子"""

        # 生成trade_when交易条件因子
        trade_when_factors = self.generate_trade_when_factors(factor.factor_expression)

        best_performance = None
        best_factor_expr = None

        # 测试每个trade_when因子的性能
        for trade_expr in trade_when_factors:
            try:
                request_data = {
                    'expression': trade_expr,
                    'universe': factor.universe or 'TOP3000',
                    'region': factor.region or 'USA',
                    'delay': factor.delay or 1,
                    'decay': factor.decay or 6,
                    'neutralization': factor.neutralization or 'SUBINDUSTRY'
                }

                response = await self.api_client.robust_request(
                    '/alpha/simulation',
                    request_data,
                    account
                )

                performance = self.parse_simulation_result(response)

                # 选择最佳性能的因子
                if (best_performance is None or
                    performance.get('sharpe', 0) > best_performance.get('sharpe', 0)):
                    best_performance = performance
                    best_factor_expr = trade_expr

            except Exception as e:
                logger.warning(f"trade_when因子测试失败: {trade_expr}, 错误: {e}")
                continue

        if best_performance and best_factor_expr:
            # 创建最佳trade_when因子记录
            await self.create_trade_when_factor(factor, best_factor_expr, best_performance)

            # 判断是否可以进入检查阶段
            if self.meets_check_criteria(best_performance):
                await self.promote_to_check(factor, best_performance)

            return best_performance
        else:
            raise ValueError("未能生成有效的trade_when因子")

    def generate_trade_when_factors(self, base_expression: str) -> List[str]:
        """生成trade_when交易条件因子"""
        trade_when_templates = [
            "trade_when({expr}, {expr} > delay({expr}, 1))",
            "trade_when({expr}, {expr} > delay({expr}, 2))",
            "trade_when({expr}, {expr} > ts_mean({expr}, 5))",
            "trade_when({expr}, {expr} > ts_mean({expr}, 10))",
            "trade_when({expr}, abs({expr}) > ts_std({expr}, 20))",
            "trade_when({expr}, {expr} > rank({expr}))",
        ]

        trade_when_factors = []
        for template in trade_when_templates:
            trade_expr = template.format(expr=base_expression)
            trade_when_factors.append(trade_expr)

        return trade_when_factors

    async def create_trade_when_factor(self, parent_factor: AlphaFactorQueueModel,
                                     trade_expr: str, performance: Dict):
        """创建trade_when因子记录"""
        AlphaFactorQueueModel.create(
            factor_expression=trade_expr,
            source_dataset=parent_factor.source_dataset,
            stage='stage3',
            status='completed',
            priority=parent_factor.priority + 10,  # 提高优先级
            region=parent_factor.region,
            universe=parent_factor.universe,
            delay=parent_factor.delay,
            decay=parent_factor.decay,
            neutralization=parent_factor.neutralization,
            tags=f"{parent_factor.tags},trade_when",
            submitted_at=datetime.now(),
            completed_at=datetime.now(),
            sharpe=performance.get('sharpe'),
            fitness=performance.get('fitness'),
            turnover=performance.get('turnover')
        )

class CheckWorker(BaseWorker):
    """因子检测处理器"""

    async def execute_factor_processing(self, factor: AlphaFactorQueueModel,
                                      account: UserAccountModel) -> Dict:
        """执行因子质量检测"""
        # 自相关性检测
        autocorr_result = await self.check_autocorrelation(factor, account)

        # 产品相关性检测
        product_corr_result = await self.check_product_correlation(factor, account)

        check_result = {
            'autocorr_passed': autocorr_result['passed'],
            'product_corr_passed': product_corr_result['passed'],
            'overall_passed': (autocorr_result['passed'] and
                             product_corr_result['passed'])
        }

        if check_result['overall_passed']:
            await self.promote_to_submit(factor, check_result)

        return check_result

class SubmitWorker(BaseWorker):
    """智能因子提交处理器 - 基于s7.py智能提交逻辑"""

    def __init__(self):
        super().__init__()
        self.submit_timeout = 30 * 60  # 30分钟超时
        self.retry_interval = 10  # 重试间隔

    async def execute_factor_processing(self, factor: AlphaFactorQueueModel,
                                      account: UserAccountModel) -> Dict:
        """执行智能因子提交 - 30分钟超时机制"""

        if not factor.alpha_id:
            raise ValueError(f"因子 {factor.id} 缺少alpha_id，无法提交")

        submit_result = await self.submit_alpha_with_timeout(factor.alpha_id, account)

        # 根据提交结果更新因子状态
        if submit_result['status_code'] == 200:
            submit_result.update({
                'submitted': True,
                'submitted_at': datetime.now().isoformat(),
                'factor_id': factor.id
            })

            # 发送成功通知
            await self.notify_successful_submission(factor, submit_result)

        elif submit_result['status_code'] == 403:
            # 永久拒绝，标记为失败
            submit_result['submitted'] = False
            submit_result['rejection_reason'] = submit_result['message']

        elif submit_result['status_code'] == 408:
            # 超时，可以重试
            submit_result['submitted'] = False
            submit_result['timeout'] = True

        return submit_result

    async def submit_alpha_with_timeout(self, alpha_id: str,
                                      account: UserAccountModel) -> Dict:
        """30分钟超时的智能提交"""
        submit_url = f"/alphas/{alpha_id}/submit"

        # 第一阶段：发起提交请求
        for attempt in range(1, 6):
            try:
                response = await self.api_client.robust_request(
                    submit_url, {}, account, method='POST'
                )
                if response.status == 201:
                    break
                elif response.status == 403:
                    return {'status_code': 403, 'message': '提交被永久拒绝'}

                await asyncio.sleep(3)
            except Exception as e:
                if attempt == 5:
                    return {'status_code': 408, 'message': '服务器连接超时'}

        # 第二阶段：轮询提交状态（30分钟超时）
        start_time = datetime.now()
        timeout = start_time + timedelta(minutes=30)
        last_status_time = start_time

        while datetime.now() < timeout:
            try:
                response = await self.api_client.robust_request(
                    submit_url, {}, account, method='GET'
                )

                elapsed = datetime.now() - start_time

                # 每5分钟显示等待状态
                if (datetime.now() - last_status_time).seconds >= 300:
                    logger.info(f"因子 {alpha_id} 持续提交中，已等待 {elapsed.seconds // 60} 分钟")
                    last_status_time = datetime.now()

                if response.status == 200:
                    if 'Retry-After' in response.headers:
                        await asyncio.sleep(float(response.headers['Retry-After']))
                    else:
                        return {'status_code': 200, 'message': '提交成功'}

                elif response.status == 403:
                    return {'status_code': 403, 'message': '合规检查未通过'}

                else:
                    await asyncio.sleep(self.retry_interval)

            except Exception as e:
                logger.warning(f"提交状态查询异常: {e}")
                await asyncio.sleep(self.retry_interval)

        return {'status_code': 408, 'message': '超时终止（30分钟未完成）'}

    async def process_batch(self, factors: List[AlphaFactorQueueModel],
                          accounts: List[UserAccountModel]):
        """智能批量提交 - 按自相关性排序"""

        # 按自相关性排序（如果有的话）
        sorted_factors = sorted(factors,
                              key=lambda f: f.performance_data.get('self_corr', 0)
                              if f.performance_data else 0)

        # 调用父类的批量处理方法
        await super().process_batch(sorted_factors, accounts)

    async def notify_successful_submission(self, factor: AlphaFactorQueueModel,
                                         result: Dict):
        """发送提交成功通知"""
        notification_data = {
            'type': 'factor_submitted',
            'factor_id': factor.id,
            'alpha_id': factor.alpha_id,
            'factor_expression': factor.factor_expression[:100] + '...' if len(factor.factor_expression) > 100 else factor.factor_expression,
            'dataset': factor.source_dataset,
            'stage': factor.stage,
            'sharpe': factor.sharpe,
            'fitness': factor.fitness,
            'submitted_at': result['submitted_at']
        }

        # 发送通知（需要集成通知管理器）
        await self.notification_manager.send_notification(
            message=f"🎉 因子提交成功！\n"
                   f"📊 Alpha ID: {factor.alpha_id}\n"
                   f"📈 Sharpe: {factor.sharpe}\n"
                   f"🎯 Fitness: {factor.fitness}",
            channels=['wechat', 'email'],
            priority='high'
        )
```

#### 5. 实时监控和统计系统

```python
class FactorDiggingMonitor:
    """因子挖掘监控系统"""

    def __init__(self):
        self.metrics_collector = MetricsCollector()
        self.dashboard = Dashboard()
        self.alert_manager = AlertManager()

    async def update_stats(self):
        """更新实时统计数据"""
        stats = await self.get_real_time_stats()
        await self.check_alerts(stats)

    async def get_real_time_stats(self) -> Dict:
        """获取实时统计数据"""
        current_time = datetime.now()
        hour_ago = current_time - timedelta(hours=1)
        day_ago = current_time - timedelta(days=1)

        # 各阶段处理统计
        stage_stats = {}
        for stage in ['stage1', 'stage2', 'stage3', 'check', 'submit']:
            stage_stats[stage] = {
                'processing': await self.get_processing_count(stage),
                'completed_hour': await self.get_completed_count(stage, hour_ago),
                'failed_hour': await self.get_failed_count(stage, hour_ago),
                'avg_processing_time': await self.get_avg_processing_time(stage),
                'success_rate': await self.calculate_success_rate(stage, hour_ago),
                'queue_depth': await self.get_queue_depth(stage),
                'estimated_completion_time': await self.estimate_completion_time(stage),
                'throughput_per_hour': await self.get_throughput_per_hour(stage)
            }

        # 账户统计
        account_stats = {}
        for account in UserAccountModel.select().where(UserAccountModel.status == 'active'):
            account_stats[account.username] = {
                'factors_processed_hour': await self.get_account_processed_count(account, hour_ago),
                'success_rate': await self.get_account_success_rate(account, hour_ago),
                'avg_response_time': await self.get_account_avg_response_time(account, hour_ago),
                'current_load': await self.get_account_current_load(account),
                'health_score': await self.get_account_health_score(account),
                'errors_last_hour': await self.get_account_errors_count(account, hour_ago),
                'best_performing_dataset': await self.get_account_best_dataset(account),
                'utilization_rate': await self.get_account_utilization_rate(account)
            }

        # 系统整体统计
        system_stats = {
            'total_factors_in_queue': await self.get_total_queue_size(),
            'total_processing': await self.get_total_processing_count(),
            'total_completed_today': await self.get_total_completed_today(),
            'total_submitted_today': await self.get_total_submitted_today(),
            'system_error_rate': await self.calculate_system_error_rate(),
            'peak_concurrent_processing': await self.get_peak_concurrent_processing(),
            'avg_system_latency': await self.get_avg_system_latency(),
            'database_performance': await self.get_database_performance(),
            'memory_usage': await self.get_memory_usage(),
            'cpu_usage': await self.get_cpu_usage(),
            'active_sessions': await self.get_active_sessions_count(),
            'failed_sessions': await self.get_failed_sessions_count()
        }

        # 性能趋势（对比昨天同时段）
        performance_trends = {
            'completion_rate_trend': await self.calculate_completion_rate_trend(),
            'response_time_trend': await self.calculate_response_time_trend(),
            'error_rate_trend': await self.calculate_error_rate_trend(),
            'throughput_trend': await self.calculate_throughput_trend()
        }

        return {
            'timestamp': current_time.isoformat(),
            'stage_stats': stage_stats,
            'account_stats': account_stats,
            'system_stats': system_stats,
            'performance_trends': performance_trends,
            'data_quality': await self.assess_data_quality()
        }

    async def get_processing_count(self, stage: str) -> int:
        """获取某阶段正在处理的因子数量"""
        return (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.stage == stage,
                    AlphaFactorQueueModel.status == 'processing'
                )
                .count())

    async def get_completed_count(self, stage: str, since: datetime) -> int:
        """获取某阶段自指定时间以来完成的因子数量"""
        return (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.stage == stage,
                    AlphaFactorQueueModel.status == 'completed',
                    AlphaFactorQueueModel.completed_at >= since
                )
                .count())

    async def get_failed_count(self, stage: str, since: datetime) -> int:
        """获取某阶段自指定时间以来失败的因子数量"""
        return (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.stage == stage,
                    AlphaFactorQueueModel.status == 'failed',
                    AlphaFactorQueueModel.completed_at >= since
                )
                .count())

    async def get_avg_processing_time(self, stage: str) -> float:
        """获取某阶段的平均处理时间"""
        hour_ago = datetime.now() - timedelta(hours=1)

        logs = (AlphaProcessingLogModel
                .select()
                .join(AlphaFactorQueueModel)
                .where(
                    AlphaFactorQueueModel.stage == stage,
                    AlphaProcessingLogModel.status == 'completed',
                    AlphaProcessingLogModel.created_at >= hour_ago,
                    AlphaProcessingLogModel.duration.is_null(False)
                ))

        durations = [log.duration for log in logs if log.duration]
        return sum(durations) / len(durations) if durations else 0.0

    async def calculate_success_rate(self, stage: str, since: datetime) -> float:
        """计算某阶段的成功率"""
        total = (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.stage == stage,
                    AlphaFactorQueueModel.started_at >= since
                )
                .count())

        if total == 0:
            return 0.0

        completed = await self.get_completed_count(stage, since)
        return completed / total

    async def get_queue_depth(self, stage: str) -> int:
        """获取某阶段的队列深度"""
        return (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.stage == stage,
                    AlphaFactorQueueModel.status == 'pending'
                )
                .count())

    async def estimate_completion_time(self, stage: str) -> Optional[str]:
        """估算某阶段队列完成时间"""
        queue_depth = await self.get_queue_depth(stage)
        if queue_depth == 0:
            return None

        # 计算平均处理速度（因子/小时）
        throughput = await self.get_throughput_per_hour(stage)
        if throughput == 0:
            return "无法估算"

        hours_remaining = queue_depth / throughput
        estimated_time = datetime.now() + timedelta(hours=hours_remaining)

        return estimated_time.strftime('%Y-%m-%d %H:%M:%S')

    async def get_throughput_per_hour(self, stage: str) -> float:
        """获取某阶段每小时的处理量"""
        hour_ago = datetime.now() - timedelta(hours=1)
        completed = await self.get_completed_count(stage, hour_ago)
        return float(completed)

    async def get_account_processed_count(self, account: UserAccountModel, since: datetime) -> int:
        """获取账户处理的因子数量"""
        return (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.assigned_account == account,
                    AlphaFactorQueueModel.started_at >= since
                )
                .count())

    async def get_account_success_rate(self, account: UserAccountModel, since: datetime) -> float:
        """获取账户成功率"""
        total = await self.get_account_processed_count(account, since)
        if total == 0:
            return 0.0

        completed = (AlphaFactorQueueModel
                    .select()
                    .where(
                        AlphaFactorQueueModel.assigned_account == account,
                        AlphaFactorQueueModel.status == 'completed',
                        AlphaFactorQueueModel.completed_at >= since
                    )
                    .count())

        return completed / total

    async def get_account_avg_response_time(self, account: UserAccountModel, since: datetime) -> float:
        """获取账户平均响应时间"""
        logs = (AlphaProcessingLogModel
                .select()
                .join(AlphaFactorQueueModel)
                .where(
                    AlphaFactorQueueModel.assigned_account == account,
                    AlphaProcessingLogModel.created_at >= since,
                    AlphaProcessingLogModel.duration.is_null(False)
                ))

        durations = [log.duration for log in logs if log.duration]
        return sum(durations) / len(durations) if durations else 0.0

    async def get_account_current_load(self, account: UserAccountModel) -> int:
        """获取账户当前负载"""
        return (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.assigned_account == account,
                    AlphaFactorQueueModel.status == 'processing'
                )
                .count())

    async def get_account_health_score(self, account: UserAccountModel) -> float:
        """获取账户健康分数"""
        health_checker = AccountHealthChecker()
        health_info = await health_checker.check_account_health(account)
        return health_info.get('overall_score', 0.0)

    async def get_account_errors_count(self, account: UserAccountModel, since: datetime) -> int:
        """获取账户错误次数"""
        return (AlphaProcessingLogModel
                .select()
                .join(AlphaFactorQueueModel)
                .where(
                    AlphaFactorQueueModel.assigned_account == account,
                    AlphaProcessingLogModel.status == 'failed',
                    AlphaProcessingLogModel.created_at >= since
                )
                .count())

    async def get_account_best_dataset(self, account: UserAccountModel) -> Optional[str]:
        """获取账户表现最好的数据集"""
        week_ago = datetime.now() - timedelta(days=7)

        logs = (AccountExecutionLogModel
                .select()
                .where(
                    AccountExecutionLogModel.account == account,
                    AccountExecutionLogModel.date >= week_ago.date()
                ))

        dataset_performance = defaultdict(lambda: {'total': 0, 'success': 0})

        for log in logs:
            dataset = log.dataset_name
            dataset_performance[dataset]['total'] += log.factors_processed
            dataset_performance[dataset]['success'] += log.factors_submitted

        best_dataset = None
        best_rate = 0

        for dataset, stats in dataset_performance.items():
            if stats['total'] > 10:  # 至少处理过10个因子
                rate = stats['success'] / stats['total']
                if rate > best_rate:
                    best_rate = rate
                    best_dataset = dataset

        return best_dataset

    async def get_account_utilization_rate(self, account: UserAccountModel) -> float:
        """获取账户利用率"""
        max_concurrent = account.max_concurrent_tasks or 5
        current_load = await self.get_account_current_load(account)
        return current_load / max_concurrent

    async def get_total_queue_size(self) -> int:
        """获取总队列大小"""
        return (AlphaFactorQueueModel
                .select()
                .where(AlphaFactorQueueModel.status == 'pending')
                .count())

    async def get_total_processing_count(self) -> int:
        """获取总处理中数量"""
        return (AlphaFactorQueueModel
                .select()
                .where(AlphaFactorQueueModel.status == 'processing')
                .count())

    async def get_total_completed_today(self) -> int:
        """获取今日完成总数"""
        today = datetime.now().date()
        return (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.status == 'completed',
                    fn.DATE(AlphaFactorQueueModel.completed_at) == today
                )
                .count())

    async def get_total_submitted_today(self) -> int:
        """获取今日提交总数"""
        today = datetime.now().date()
        return (AlphaFactorQueueModel
                .select()
                .where(
                    AlphaFactorQueueModel.status == 'submitted',
                    fn.DATE(AlphaFactorQueueModel.completed_at) == today
                )
                .count())

    async def calculate_system_error_rate(self) -> float:
        """计算系统错误率"""
        hour_ago = datetime.now() - timedelta(hours=1)

        total = (AlphaProcessingLogModel
                .select()
                .where(AlphaProcessingLogModel.created_at >= hour_ago)
                .count())

        if total == 0:
            return 0.0

        errors = (AlphaProcessingLogModel
                 .select()
                 .where(
                     AlphaProcessingLogModel.created_at >= hour_ago,
                     AlphaProcessingLogModel.status == 'failed'
                 )
                 .count())

        return errors / total

    async def get_peak_concurrent_processing(self) -> int:
        """获取峰值并发处理数"""
        # 查询过去24小时内每小时的最大并发数
        day_ago = datetime.now() - timedelta(days=1)

        # 这里简化实现，实际中可能需要更复杂的时序数据查询
        max_concurrent = 0
        for hour_offset in range(24):
            check_time = day_ago + timedelta(hours=hour_offset)
            concurrent = (AlphaFactorQueueModel
                         .select()
                         .where(
                             AlphaFactorQueueModel.status == 'processing',
                             AlphaFactorQueueModel.started_at <= check_time,
                             (AlphaFactorQueueModel.completed_at.is_null()) |
                             (AlphaFactorQueueModel.completed_at >= check_time)
                         )
                         .count())
            max_concurrent = max(max_concurrent, concurrent)

        return max_concurrent

    async def get_avg_system_latency(self) -> float:
        """获取系统平均延迟"""
        hour_ago = datetime.now() - timedelta(hours=1)

        logs = (AlphaProcessingLogModel
                .select()
                .where(
                    AlphaProcessingLogModel.created_at >= hour_ago,
                    AlphaProcessingLogModel.duration.is_null(False)
                ))

        durations = [log.duration for log in logs if log.duration]
        return sum(durations) / len(durations) if durations else 0.0

    async def get_database_performance(self) -> Dict:
        """获取数据库性能指标"""
        import psutil
        import time

        # 简单的数据库性能测试
        start_time = time.time()
        test_query = AlphaFactorQueueModel.select().limit(1)
        list(test_query)  # 执行查询
        query_time = time.time() - start_time

        return {
            'query_response_time': query_time,
            'connection_pool_size': 'N/A',  # 实际实现中获取连接池信息
            'active_connections': 'N/A'
        }

    async def get_memory_usage(self) -> Dict:
        """获取内存使用情况"""
        import psutil

        memory = psutil.virtual_memory()
        return {
            'total': memory.total,
            'available': memory.available,
            'percent': memory.percent,
            'used': memory.used
        }

    async def get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        import psutil
        return psutil.cpu_percent(interval=1)

    async def get_active_sessions_count(self) -> int:
        """获取活跃会话数"""
        # 这里需要与SessionManager集成
        session_manager = SessionManager()
        return len(session_manager.sessions)

    async def get_failed_sessions_count(self) -> int:
        """获取失败会话数"""
        hour_ago = datetime.now() - timedelta(hours=1)
        return (UserAccountModel
                .select()
                .where(
                    UserAccountModel.status == 'login_failed',
                    UserAccountModel.updated_at >= hour_ago
                )
                .count())

    async def calculate_completion_rate_trend(self) -> float:
        """计算完成率趋势"""
        now = datetime.now()
        current_hour = now.replace(minute=0, second=0, microsecond=0)
        yesterday_hour = current_hour - timedelta(days=1)

        current_completed = await self.get_completed_count('all', current_hour)
        yesterday_completed = await self.get_completed_count('all', yesterday_hour)

        if yesterday_completed == 0:
            return 0.0

        return (current_completed - yesterday_completed) / yesterday_completed

    async def calculate_response_time_trend(self) -> float:
        """计算响应时间趋势"""
        now = datetime.now()
        hour_ago = now - timedelta(hours=1)
        two_hours_ago = now - timedelta(hours=2)

        current_avg = await self.get_avg_system_latency()

        # 获取前一小时的平均响应时间
        logs = (AlphaProcessingLogModel
                .select()
                .where(
                    AlphaProcessingLogModel.created_at.between(two_hours_ago, hour_ago),
                    AlphaProcessingLogModel.duration.is_null(False)
                ))

        durations = [log.duration for log in logs if log.duration]
        previous_avg = sum(durations) / len(durations) if durations else current_avg

        if previous_avg == 0:
            return 0.0

        return (current_avg - previous_avg) / previous_avg

    async def calculate_error_rate_trend(self) -> float:
        """计算错误率趋势"""
        current_rate = await self.calculate_system_error_rate()

        # 计算前一小时的错误率
        now = datetime.now()
        hour_ago = now - timedelta(hours=1)
        two_hours_ago = now - timedelta(hours=2)

        total_prev = (AlphaProcessingLogModel
                     .select()
                     .where(AlphaProcessingLogModel.created_at.between(two_hours_ago, hour_ago))
                     .count())

        if total_prev == 0:
            return 0.0

        errors_prev = (AlphaProcessingLogModel
                      .select()
                      .where(
                          AlphaProcessingLogModel.created_at.between(two_hours_ago, hour_ago),
                          AlphaProcessingLogModel.status == 'failed'
                      )
                      .count())

        prev_rate = errors_prev / total_prev

        if prev_rate == 0:
            return 0.0

        return (current_rate - prev_rate) / prev_rate

    async def calculate_throughput_trend(self) -> float:
        """计算吞吐量趋势"""
        now = datetime.now()
        hour_ago = now - timedelta(hours=1)
        two_hours_ago = now - timedelta(hours=2)

        current_throughput = await self.get_throughput_per_hour('all')

        prev_completed = (AlphaFactorQueueModel
                         .select()
                         .where(
                             AlphaFactorQueueModel.status == 'completed',
                             AlphaFactorQueueModel.completed_at.between(two_hours_ago, hour_ago)
                         )
                         .count())

        if prev_completed == 0:
            return 0.0

        return (current_throughput - prev_completed) / prev_completed

    async def assess_data_quality(self) -> Dict:
        """评估数据质量"""
        quality_issues = []

        # 检查是否有长时间未更新的因子
        stale_factors = (AlphaFactorQueueModel
                        .select()
                        .where(
                            AlphaFactorQueueModel.status == 'processing',
                            AlphaFactorQueueModel.started_at < datetime.now() - timedelta(hours=2)
                        )
                        .count())

        if stale_factors > 0:
            quality_issues.append(f"{stale_factors} 个因子处理时间过长")

        # 检查重试次数过高的因子
        high_retry_factors = (AlphaFactorQueueModel
                             .select()
                             .where(AlphaFactorQueueModel.retry_count > 3)
                             .count())

        if high_retry_factors > 0:
            quality_issues.append(f"{high_retry_factors} 个因子重试次数过高")

        # 检查数据一致性
        orphaned_logs = (AlphaProcessingLogModel
                        .select()
                        .join(AlphaFactorQueueModel, JOIN.LEFT_OUTER)
                        .where(AlphaFactorQueueModel.id.is_null())
                        .count())

        if orphaned_logs > 0:
            quality_issues.append(f"{orphaned_logs} 条孤立的处理日志")

        return {
            'overall_score': max(0, 1 - len(quality_issues) * 0.1),
            'issues': quality_issues,
            'last_check': datetime.now().isoformat()
        }

    async def record_execution_metrics(self, factor: AlphaFactorQueueModel,
                                     account: UserAccountModel, result: Dict):
        """记录执行指标"""
        # 记录到账户执行日志
        AccountExecutionLogModel.create(
            account=account,
            date=datetime.now().date(),
            dataset_name=factor.tags or 'unknown',
            stage=factor.stage,
            factors_processed=1,
            factors_submitted=1 if factor.stage == 'submit' else 0,
            success_rate=1.0,
            avg_response_time=result.get('response_time', 0),
            error_count=0,
            parameters_config=json.dumps({
                'universe': factor.universe,
                'region': factor.region,
                'delay': factor.delay,
                'decay': factor.decay
            }),
            performance_score=result.get('sharpe', 0)
        )

        # 更新实时指标缓存
        await self.update_realtime_metrics(factor, account, result)

    async def record_error(self, factor: AlphaFactorQueueModel,
                         account: UserAccountModel, error: Exception):
        """记录错误统计"""
        error_type = type(error).__name__

        # 记录错误到日志
        AlphaProcessingLogModel.create(
            factor_queue=factor,
            operation=f"{factor.stage}_processing",
            status='failed',
            error_message=str(error)
        )

        # 更新账户错误计数
        account.error_count += 1
        if account.error_count > 10:
            account.status = 'error'
            await self.alert_manager.send_account_error_alert(account)
        account.save()

        # 记录错误统计
        await self.metrics_collector.increment_error_count(
            error_type, factor.stage, account.username
        )

    async def generate_daily_report(self) -> Dict:
        """生成日报"""
        today = datetime.now().date()

        # 今日处理统计
        daily_processing = {
            'factors_generated': self.get_daily_generated_count(today),
            'factors_processed': self.get_daily_processed_count(today),
            'factors_submitted': self.get_daily_submitted_count(today),
            'success_rate': self.calculate_daily_success_rate(today)
        }

        # 各账户表现
        account_performance = {}
        for account in UserAccountModel.select():
            account_performance[account.username] = {
                'processed': self.get_account_daily_processed(account, today),
                'submitted': self.get_account_daily_submitted(account, today),
                'success_rate': self.get_account_daily_success_rate(account, today),
                'top_factors': self.get_account_top_factors(account, today)
            }

        # 质量分析
        quality_analysis = {
            'avg_sharpe': self.get_daily_avg_sharpe(today),
            'top_performing_factors': self.get_top_performing_factors(today),
            'quality_distribution': self.get_quality_distribution(today)
        }

        # 错误分析
        error_analysis = {
            'total_errors': self.get_daily_error_count(today),
            'error_distribution': self.get_error_distribution(today),
            'problematic_accounts': self.get_problematic_accounts(today)
        }

        return {
            'date': today.isoformat(),
            'daily_processing': daily_processing,
            'account_performance': account_performance,
            'quality_analysis': quality_analysis,
            'error_analysis': error_analysis,
            'recommendations': await self.generate_recommendations()
        }

class AlertManager:
    """告警管理器"""

    def __init__(self):
        self.notification_manager = NotificationManager()
        self.alert_thresholds = {
            'error_rate': 0.1,  # 10%错误率阈值
            'processing_delay': 300,  # 5分钟处理延迟阈值
            'queue_backlog': 1000,  # 队列积压阈值
            'account_error_count': 10  # 账户错误计数阈值
        }

    async def check_alerts(self, stats: Dict):
        """检查告警条件"""
        # 检查系统错误率
        if stats['system_stats']['system_error_rate'] > self.alert_thresholds['error_rate']:
            await self.send_error_rate_alert(stats['system_stats']['system_error_rate'])

        # 检查队列积压
        if stats['system_stats']['total_factors_in_queue'] > self.alert_thresholds['queue_backlog']:
            await self.send_queue_backlog_alert(stats['system_stats']['total_factors_in_queue'])

        # 检查账户异常
        for account_name, account_stats in stats['account_stats'].items():
            if account_stats['success_rate'] < 0.8:  # 成功率低于80%
                await self.send_account_performance_alert(account_name, account_stats)

    async def send_error_rate_alert(self, error_rate: float):
        """发送错误率告警"""
        message = f"🚨 系统错误率告警\n当前错误率: {error_rate:.2%}\n请检查系统状态"
        await self.notification_manager.send_alert(message, priority='high')

    async def send_queue_backlog_alert(self, queue_size: int):
        """发送队列积压告警"""
        message = f"📊 队列积压告警\n当前队列大小: {queue_size}\n建议增加处理能力"
        await self.notification_manager.send_alert(message, priority='medium')
```

#### 4. 事件驱动通知系统

```python
from enum import Enum
from typing import Any, Callable, Dict, List
import asyncio

class EventType(Enum):
    """事件类型枚举"""
    FACTOR_SUBMITTED = "factor_submitted"
    HIGH_QUALITY_FACTOR_FOUND = "high_quality_factor_found"
    BATCH_COMPLETED = "batch_completed"
    SYSTEM_ERROR = "system_error"
    ACCOUNT_ERROR = "account_error"
    RATE_LIMIT_HIT = "rate_limit_hit"
    SESSION_EXPIRED = "session_expired"
    PERFORMANCE_DEGRADED = "performance_degraded"

class FactorEvent:
    """因子事件类"""
    def __init__(self, event_type: EventType, data: Dict[str, Any],
                 source: str = None, timestamp: float = None):
        self.type = event_type
        self.data = data
        self.source = source or "system"
        self.timestamp = timestamp or time.time()
        self.event_id = self._generate_event_id()

    def _generate_event_id(self) -> str:
        """生成唯一事件ID"""
        return f"{self.type.value}_{int(self.timestamp)}_{random.randint(1000, 9999)}"

class EventNotificationSystem:
    """事件驱动的通知系统"""

    def __init__(self):
        self.notification_channels = {
            'wechat': WeChatNotifier(),
            'email': EmailNotifier(),
            'webhook': WebhookNotifier(),
            'database': DatabaseNotifier()
        }
        self.event_handlers = {}  # event_type -> [handler_functions]
        self.event_filters = {}   # event_type -> filter_function
        self.event_queue = asyncio.Queue(maxsize=10000)
        self.processing_task = None

    def register_handler(self, event_type: EventType, handler: Callable):
        """注册事件处理器"""
        if event_type not in self.event_handlers:
            self.event_handlers[event_type] = []
        self.event_handlers[event_type].append(handler)

    def register_filter(self, event_type: EventType, filter_func: Callable):
        """注册事件过滤器"""
        self.event_filters[event_type] = filter_func

    async def emit_event(self, event: FactorEvent):
        """发出事件"""
        try:
            # 应用过滤器
            if event.type in self.event_filters:
                if not self.event_filters[event.type](event):
                    return  # 事件被过滤掉

            await self.event_queue.put(event)
        except asyncio.QueueFull:
            logger.error("事件队列已满，丢弃事件")

    async def start_processing(self):
        """开始处理事件队列"""
        if self.processing_task:
            return

        self.processing_task = asyncio.create_task(self._process_events())

    async def _process_events(self):
        """处理事件队列"""
        while True:
            try:
                event = await self.event_queue.get()
                await self._handle_event(event)
                self.event_queue.task_done()
            except Exception as e:
                logger.error(f"处理事件时出错: {e}")

    async def _handle_event(self, event: FactorEvent):
        """处理单个事件"""
        # 调用注册的处理器
        if event.type in self.event_handlers:
            tasks = []
            for handler in self.event_handlers[event.type]:
                tasks.append(handler(event))
            await asyncio.gather(*tasks, return_exceptions=True)

        # 根据事件类型发送通知
        await self._route_event_notification(event)

    async def _route_event_notification(self, event: FactorEvent):
        """路由事件通知"""
        notification_config = self._get_notification_config(event.type)

        for channel_name in notification_config['channels']:
            if channel_name in self.notification_channels:
                channel = self.notification_channels[channel_name]
                message = self._format_message(event, channel_name)

                try:
                    await channel.send_notification(
                        message=message,
                        priority=notification_config['priority'],
                        event_id=event.event_id
                    )
                except Exception as e:
                    logger.error(f"发送通知失败 ({channel_name}): {e}")

    def _get_notification_config(self, event_type: EventType) -> Dict:
        """获取通知配置"""
        config_map = {
            EventType.FACTOR_SUBMITTED: {
                'channels': ['wechat', 'database'],
                'priority': 'medium'
            },
            EventType.HIGH_QUALITY_FACTOR_FOUND: {
                'channels': ['wechat', 'email', 'database'],
                'priority': 'high'
            },
            EventType.SYSTEM_ERROR: {
                'channels': ['wechat', 'email', 'webhook'],
                'priority': 'urgent'
            },
            EventType.BATCH_COMPLETED: {
                'channels': ['database'],
                'priority': 'low'
            },
            EventType.ACCOUNT_ERROR: {
                'channels': ['wechat', 'database'],
                'priority': 'high'
            }
        }

        return config_map.get(event_type, {
            'channels': ['database'],
            'priority': 'low'
        })

    def _format_message(self, event: FactorEvent, channel: str) -> str:
        """格式化消息"""
        formatters = {
            EventType.FACTOR_SUBMITTED: self._format_factor_submitted,
            EventType.HIGH_QUALITY_FACTOR_FOUND: self._format_high_quality_factor,
            EventType.SYSTEM_ERROR: self._format_system_error,
            EventType.BATCH_COMPLETED: self._format_batch_completed,
            EventType.ACCOUNT_ERROR: self._format_account_error
        }

        formatter = formatters.get(event.type, self._format_default)
        return formatter(event, channel)

    def _format_factor_submitted(self, event: FactorEvent, channel: str) -> str:
        """格式化因子提交消息"""
        data = event.data
        if channel == 'wechat':
            return (f"🎉 因子提交成功！\n"
                   f"📊 Alpha ID: {data.get('alpha_id')}\n"
                   f"📈 Sharpe: {data.get('sharpe', 'N/A')}\n"
                   f"🎯 Fitness: {data.get('fitness', 'N/A')}\n"
                   f"📅 时间: {datetime.now().strftime('%H:%M:%S')}")
        else:
            return f"Factor submitted: {data.get('alpha_id')}"

    def _format_high_quality_factor(self, event: FactorEvent, channel: str) -> str:
        """格式化高质量因子消息"""
        data = event.data
        if channel == 'wechat':
            return (f"⭐ 发现高质量因子！\n"
                   f"📊 Sharpe: {data.get('sharpe')}\n"
                   f"💎 Fitness: {data.get('fitness')}\n"
                   f"🔢 表达式: {data.get('expression', '')[:50]}...")
        else:
            return f"High quality factor found: Sharpe={data.get('sharpe')}"

    def _format_system_error(self, event: FactorEvent, channel: str) -> str:
        """格式化系统错误消息"""
        data = event.data
        return (f"🚨 系统错误\n"
               f"错误类型: {data.get('error_type')}\n"
               f"错误消息: {data.get('message')}\n"
               f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    def _format_batch_completed(self, event: FactorEvent, channel: str) -> str:
        """格式化批次完成消息"""
        data = event.data
        return (f"批次处理完成\n"
               f"总数: {data.get('total')}\n"
               f"成功: {data.get('success')}\n"
               f"失败: {data.get('failed')}")

    def _format_account_error(self, event: FactorEvent, channel: str) -> str:
        """格式化账户错误消息"""
        data = event.data
        return (f"⚠️ 账户异常\n"
               f"账户: {data.get('username')}\n"
               f"错误: {data.get('error_message')}")

    def _format_default(self, event: FactorEvent, channel: str) -> str:
        """默认消息格式"""
        return f"Event: {event.type.value}, Data: {event.data}"

class NotificationManager:
    """通知管理器 - 统一管理各种通知渠道"""

    def __init__(self):
        self.event_system = EventNotificationSystem()
        self._setup_default_handlers()

    def _setup_default_handlers(self):
        """设置默认事件处理器"""
        # 注册统计处理器
        self.event_system.register_handler(
            EventType.FACTOR_SUBMITTED,
            self._handle_factor_submitted_stats
        )

        # 注册高质量因子处理器
        self.event_system.register_handler(
            EventType.HIGH_QUALITY_FACTOR_FOUND,
            self._handle_high_quality_factor
        )

        # 注册错误处理器
        self.event_system.register_handler(
            EventType.SYSTEM_ERROR,
            self._handle_system_error
        )

    async def _handle_factor_submitted_stats(self, event: FactorEvent):
        """处理因子提交统计"""
        # 更新统计数据库
        pass

    async def _handle_high_quality_factor(self, event: FactorEvent):
        """处理高质量因子"""
        # 可以触发特殊处理流程
        pass

    async def _handle_system_error(self, event: FactorEvent):
        """处理系统错误"""
        # 记录错误日志，可能触发自动修复
        pass

    async def notify_factor_submitted(self, factor_info: Dict):
        """通知因子提交"""
        event = FactorEvent(
            EventType.FACTOR_SUBMITTED,
            factor_info,
            source="submit_worker"
        )
        await self.event_system.emit_event(event)

    async def notify_batch_completed(self, batch_stats: Dict):
        """通知批次完成"""
        event = FactorEvent(
            EventType.BATCH_COMPLETED,
            batch_stats,
            source="batch_processor"
        )
        await self.event_system.emit_event(event)

    async def notify_error_threshold_exceeded(self, error_rate: float):
        """通知错误率过高"""
        event = FactorEvent(
            EventType.SYSTEM_ERROR,
            {
                'error_type': 'high_error_rate',
                'error_rate': error_rate,
                'threshold': 0.1,
                'message': f'错误率 {error_rate:.2%} 超过阈值 10%'
            },
            source="monitor"
        )
        await self.event_system.emit_event(event)

    async def send_daily_report(self, report: str):
        """发送日报"""
        # 直接通过邮件发送日报
        email_channel = self.event_system.notification_channels['email']
        await email_channel.send_notification(
            message=report,
            subject="WQ系统日报",
            priority='low'
        )
```

#### 5. 多账户调度系统

```python
import random
from collections import defaultdict, deque
from typing import Dict, List, Optional, Tuple
import asyncio
from datetime import datetime, timedelta

class AccountHealthChecker:
    """账户健康检查器"""

    def __init__(self):
        self.health_cache = {}  # account_id -> health_info
        self.check_interval = 300  # 5分钟检查一次
        self.health_threshold = 0.8  # 健康度阈值

    async def check_account_health(self, account: UserAccountModel) -> Dict:
        """检查账户健康状态"""
        health_info = {
            'account_id': account.id,
            'username': account.username,
            'overall_score': 0.0,
            'last_check': datetime.now(),
            'issues': []
        }

        # 1. 检查登录状态
        login_score = await self._check_login_status(account)
        health_info['login_score'] = login_score

        # 2. 检查最近错误率
        error_score = await self._check_error_rate(account)
        health_info['error_score'] = error_score

        # 3. 检查响应时间
        response_score = await self._check_response_time(account)
        health_info['response_score'] = response_score

        # 4. 检查限流状态
        rate_limit_score = await self._check_rate_limit_status(account)
        health_info['rate_limit_score'] = rate_limit_score

        # 综合评分
        health_info['overall_score'] = (
            login_score * 0.3 +
            error_score * 0.3 +
            response_score * 0.2 +
            rate_limit_score * 0.2
        )

        # 判断健康状态
        if health_info['overall_score'] >= self.health_threshold:
            health_info['status'] = 'healthy'
        elif health_info['overall_score'] >= 0.5:
            health_info['status'] = 'degraded'
        else:
            health_info['status'] = 'unhealthy'

        self.health_cache[account.id] = health_info
        return health_info

    async def _check_login_status(self, account: UserAccountModel) -> float:
        """检查登录状态"""
        try:
            # 简单的API调用测试登录状态
            session_manager = SessionManager()
            session = await session_manager.get_session(account)

            async with session.get('/api/user/profile', timeout=5) as response:
                return 1.0 if response.status == 200 else 0.0
        except:
            return 0.0

    async def _check_error_rate(self, account: UserAccountModel) -> float:
        """检查最近错误率"""
        try:
            # 查询最近1小时的错误率
            hour_ago = datetime.now() - timedelta(hours=1)

            total_logs = (AlphaProcessingLogModel
                         .select()
                         .join(AlphaFactorQueueModel)
                         .where(
                             AlphaProcessingLogModel.created_at >= hour_ago,
                             AlphaFactorQueueModel.assigned_account == account
                         )
                         .count())

            if total_logs == 0:
                return 1.0  # 没有日志则认为是健康的

            error_logs = (AlphaProcessingLogModel
                         .select()
                         .join(AlphaFactorQueueModel)
                         .where(
                             AlphaProcessingLogModel.created_at >= hour_ago,
                             AlphaProcessingLogModel.status == 'failed',
                             AlphaFactorQueueModel.assigned_account == account
                         )
                         .count())

            error_rate = error_logs / total_logs
            return max(0.0, 1.0 - error_rate * 2)  # 错误率越高分数越低

        except:
            return 0.5  # 查询失败时返回中性分数

class MultiAccountScheduler:
    """多账户任务调度器"""

    def __init__(self):
        self.account_loads = defaultdict(int)  # account_id -> current_load
        self.account_performance = defaultdict(lambda: {
            'success_rate': 1.0,
            'avg_response_time': 1.0,
            'last_activity': datetime.now(),
            'consecutive_errors': 0
        })
        self.health_checker = AccountHealthChecker()
        self.load_balancer = LoadBalancer()
        self.rotation_strategy = RotationStrategy()

    async def get_available_accounts(self, min_health_score: float = 0.7) -> List[UserAccountModel]:
        """获取可用账户列表"""
        accounts = (UserAccountModel
                   .select()
                   .where(UserAccountModel.status == 'active'))

        available_accounts = []

        for account in accounts:
            # 检查健康状态
            health_info = await self.health_checker.check_account_health(account)

            if health_info['overall_score'] >= min_health_score:
                # 检查当前负载
                current_load = self.account_loads[account.id]
                max_load = account.max_concurrent_tasks or 5

                if current_load < max_load:
                    available_accounts.append(account)

        # 按健康分数和负载排序
        available_accounts.sort(
            key=lambda a: (
                self.health_checker.health_cache.get(a.id, {}).get('overall_score', 0),
                -self.account_loads[a.id]  # 负载越低越优先
            ),
            reverse=True
        )

        return available_accounts

    async def assign_factors_to_accounts(self, factors: List[AlphaFactorQueueModel]) -> Dict[int, List[AlphaFactorQueueModel]]:
        """将因子分配给账户"""
        available_accounts = await self.get_available_accounts()

        if not available_accounts:
            raise NoAvailableAccountsError("没有可用的账户")

        # 使用负载均衡算法分配
        assignment = await self.load_balancer.distribute_factors(
            factors, available_accounts, self.account_loads
        )

        # 更新负载统计
        for account_id, assigned_factors in assignment.items():
            self.account_loads[account_id] += len(assigned_factors)

        return assignment

    async def get_optimal_account(self, task_type: str, dataset: str,
                                performance_priority: bool = False) -> UserAccountModel:
        """获取最优账户"""
        available_accounts = await self.get_available_accounts()

        if not available_accounts:
            raise NoAvailableAccountsError("没有可用的账户")

        # 如果优先考虑性能，选择在该数据集上表现最好的账户
        if performance_priority:
            best_account = None
            best_score = -1

            for account in available_accounts:
                score = await self._calculate_performance_score(account, dataset)
                if score > best_score:
                    best_score = score
                    best_account = account

            return best_account or available_accounts[0]

        # 默认选择负载最低的健康账户
        return min(available_accounts, key=lambda a: self.account_loads[a.id])

    async def _calculate_performance_score(self, account: UserAccountModel, dataset: str) -> float:
        """计算账户在特定数据集上的性能分数"""
        try:
            # 查询最近的执行记录
            recent_logs = (AccountExecutionLogModel
                          .select()
                          .where(
                              AccountExecutionLogModel.account == account,
                              AccountExecutionLogModel.dataset_name == dataset,
                              AccountExecutionLogModel.date >= datetime.now().date() - timedelta(days=7)
                          ))

            if not recent_logs:
                return 0.5  # 没有历史记录时返回中性分数

            # 计算综合分数
            total_score = 0
            total_weight = 0

            for log in recent_logs:
                # 基于成功率、响应时间等计算分数
                success_weight = log.success_rate or 0
                response_weight = max(0, 1 - (log.avg_response_time or 1) / 10)

                log_score = (success_weight * 0.7 + response_weight * 0.3)
                day_weight = 1.0 / (1 + (datetime.now().date() - log.date).days)

                total_score += log_score * day_weight
                total_weight += day_weight

            return total_score / total_weight if total_weight > 0 else 0.5

        except:
            return 0.5

    async def balance_workload_across_accounts(self):
        """在账户间平衡工作负载"""
        available_accounts = await self.get_available_accounts()

        if len(available_accounts) < 2:
            return  # 账户数量不足，无需平衡

        # 获取当前负载分布
        loads = [(account.id, self.account_loads[account.id]) for account in available_accounts]
        loads.sort(key=lambda x: x[1])

        # 计算平均负载
        total_load = sum(load for _, load in loads)
        avg_load = total_load / len(loads)

        # 重新分配过载账户的任务
        for account_id, load in loads:
            if load > avg_load * 1.5:  # 负载超过平均值50%
                await self._redistribute_account_load(account_id, available_accounts)

    async def _redistribute_account_load(self, overloaded_account_id: int,
                                       available_accounts: List[UserAccountModel]):
        """重新分配过载账户的任务"""
        # 获取该账户的pending任务
        pending_factors = (AlphaFactorQueueModel
                          .select()
                          .where(
                              AlphaFactorQueueModel.assigned_account_id == overloaded_account_id,
                              AlphaFactorQueueModel.status == 'pending'
                          )
                          .limit(10))  # 重新分配部分任务

        if not pending_factors:
            return

        # 找到负载最低的其他账户
        other_accounts = [acc for acc in available_accounts if acc.id != overloaded_account_id]
        if not other_accounts:
            return

        target_account = min(other_accounts, key=lambda a: self.account_loads[a.id])

        # 重新分配任务
        for factor in pending_factors:
            factor.assigned_account = target_account
            factor.save()

        # 更新负载统计
        redistribution_count = len(list(pending_factors))
        self.account_loads[overloaded_account_id] -= redistribution_count
        self.account_loads[target_account.id] += redistribution_count

    async def handle_account_error(self, account: UserAccountModel, error: Exception):
        """处理账户错误"""
        error_type = type(error).__name__

        # 更新性能统计
        perf = self.account_performance[account.id]
        perf['consecutive_errors'] += 1
        perf['last_error'] = {
            'type': error_type,
            'message': str(error),
            'timestamp': datetime.now()
        }

        # 根据错误类型采取不同措施
        if isinstance(error, LoginError):
            await self._handle_login_error(account)
        elif isinstance(error, RateLimitError):
            await self._handle_rate_limit_error(account)
        elif perf['consecutive_errors'] >= 5:
            await self._handle_repeated_errors(account)

        # 减少该账户的负载
        if self.account_loads[account.id] > 0:
            self.account_loads[account.id] = max(0, self.account_loads[account.id] - 1)

    async def _handle_login_error(self, account: UserAccountModel):
        """处理登录错误"""
        logger.warning(f"账户 {account.username} 登录失败，暂时禁用")
        account.status = 'login_failed'
        account.error_count += 1
        account.save()

        # 重新分配该账户的任务
        await self._redistribute_account_load(account.id, await self.get_available_accounts())

    async def _handle_rate_limit_error(self, account: UserAccountModel):
        """处理限流错误"""
        logger.warning(f"账户 {account.username} 遇到限流，暂停使用")
        account.last_rate_limit = datetime.now()
        account.save()

        # 设置冷却时间
        self.account_performance[account.id]['cooldown_until'] = datetime.now() + timedelta(minutes=30)

    async def _handle_repeated_errors(self, account: UserAccountModel):
        """处理重复错误"""
        logger.error(f"账户 {account.username} 连续错误过多，临时禁用")
        account.status = 'error'
        account.save()

    async def rotate_accounts_for_load_balancing(self):
        """账户轮换以平衡负载"""
        await self.rotation_strategy.execute_rotation(
            self.account_loads,
            self.account_performance,
            await self.get_available_accounts()
        )

    def update_account_performance(self, account_id: int, success: bool,
                                 response_time: float):
        """更新账户性能统计"""
        perf = self.account_performance[account_id]

        # 更新成功率（使用指数移动平均）
        alpha = 0.1
        if success:
            perf['success_rate'] = perf['success_rate'] * (1 - alpha) + 1.0 * alpha
            perf['consecutive_errors'] = 0
        else:
            perf['success_rate'] = perf['success_rate'] * (1 - alpha) + 0.0 * alpha
            perf['consecutive_errors'] += 1

        # 更新响应时间
        perf['avg_response_time'] = perf['avg_response_time'] * (1 - alpha) + response_time * alpha
        perf['last_activity'] = datetime.now()

class LoadBalancer:
    """负载均衡器"""

    def __init__(self):
        self.algorithm = 'weighted_round_robin'  # 可选: round_robin, least_connections, weighted

    async def distribute_factors(self, factors: List[AlphaFactorQueueModel],
                               accounts: List[UserAccountModel],
                               current_loads: Dict[int, int]) -> Dict[int, List[AlphaFactorQueueModel]]:
        """分发因子到账户"""

        if self.algorithm == 'round_robin':
            return self._round_robin_distribution(factors, accounts)
        elif self.algorithm == 'least_connections':
            return self._least_connections_distribution(factors, accounts, current_loads)
        elif self.algorithm == 'weighted_round_robin':
            return self._weighted_round_robin_distribution(factors, accounts, current_loads)
        else:
            raise ValueError(f"未知的负载均衡算法: {self.algorithm}")

    def _round_robin_distribution(self, factors: List[AlphaFactorQueueModel],
                                accounts: List[UserAccountModel]) -> Dict[int, List[AlphaFactorQueueModel]]:
        """轮询分发"""
        assignment = defaultdict(list)

        for i, factor in enumerate(factors):
            account = accounts[i % len(accounts)]
            assignment[account.id].append(factor)

        return dict(assignment)

    def _least_connections_distribution(self, factors: List[AlphaFactorQueueModel],
                                      accounts: List[UserAccountModel],
                                      current_loads: Dict[int, int]) -> Dict[int, List[AlphaFactorQueueModel]]:
        """最少连接分发"""
        assignment = defaultdict(list)
        load_counts = {acc.id: current_loads.get(acc.id, 0) for acc in accounts}

        for factor in factors:
            # 选择当前负载最小的账户
            min_account_id = min(load_counts.keys(), key=lambda k: load_counts[k])
            assignment[min_account_id].append(factor)
            load_counts[min_account_id] += 1

        return dict(assignment)

    def _weighted_round_robin_distribution(self, factors: List[AlphaFactorQueueModel],
                                         accounts: List[UserAccountModel],
                                         current_loads: Dict[int, int]) -> Dict[int, List[AlphaFactorQueueModel]]:
        """加权轮询分发"""
        assignment = defaultdict(list)

        # 计算账户权重（基于最大并发数和当前负载）
        weights = []
        for account in accounts:
            max_concurrent = account.max_concurrent_tasks or 5
            current_load = current_loads.get(account.id, 0)
            available_capacity = max(1, max_concurrent - current_load)
            weights.append(available_capacity)

        # 使用加权轮询算法
        total_weight = sum(weights)
        if total_weight == 0:
            return self._round_robin_distribution(factors, accounts)

        weight_index = 0
        current_weight = 0

        for factor in factors:
            while current_weight <= 0:
                weight_index = (weight_index + 1) % len(accounts)
                current_weight += weights[weight_index]

            account = accounts[weight_index]
            assignment[account.id].append(factor)
            current_weight -= total_weight

        return dict(assignment)

class RotationStrategy:
    """账户轮换策略"""

    def __init__(self):
        self.rotation_interval = timedelta(hours=2)  # 每2小时轮换一次
        self.last_rotation = datetime.now()

    async def execute_rotation(self, account_loads: Dict[int, int],
                             account_performance: Dict[int, Dict],
                             available_accounts: List[UserAccountModel]):
        """执行账户轮换"""

        if datetime.now() - self.last_rotation < self.rotation_interval:
            return  # 还未到轮换时间

        # 识别需要休息的账户（高负载或性能下降）
        accounts_to_rest = []
        accounts_to_activate = []

        for account in available_accounts:
            perf = account_performance.get(account.id, {})
            current_load = account_loads.get(account.id, 0)

            # 需要休息的条件
            if (current_load > 10 or  # 负载过高
                perf.get('success_rate', 1.0) < 0.8 or  # 成功率低
                perf.get('consecutive_errors', 0) > 3):  # 连续错误
                accounts_to_rest.append(account)

        # 从不活跃的账户中选择一些来激活
        inactive_accounts = (UserAccountModel
                           .select()
                           .where(UserAccountModel.status == 'inactive'))

        for account in inactive_accounts[:len(accounts_to_rest)]:
            accounts_to_activate.append(account)

        # 执行轮换
        for account in accounts_to_rest:
            account.status = 'resting'
            account.save()
            logger.info(f"账户 {account.username} 进入休息状态")

        for account in accounts_to_activate:
            account.status = 'active'
            account.save()
            logger.info(f"账户 {account.username} 重新激活")

        self.last_rotation = datetime.now()

class AccountManager:
    """账户管理器"""

    def __init__(self):
        self.crypto_manager = CryptoManager()
        self.validator = AccountValidator()

    async def add_account(self, username: str, password: str, config: Dict) -> UserAccountModel:
        """添加新账户"""

        # 验证账户信息
        validation_result = await self.validator.validate_account_info(username, password)
        if not validation_result['valid']:
            raise ValidationError(validation_result['message'])

        # 加密密码
        encrypted_password = self.crypto_manager.encrypt_password(password)

        # 创建账户记录
        account = UserAccountModel.create(
            username=username,
            password=encrypted_password,
            status='pending_verification',
            max_concurrent_tasks=config.get('max_concurrent_tasks', 5),
            preferred_datasets=json.dumps(config.get('preferred_datasets', [])),
            created_at=datetime.now(),
            updated_at=datetime.now()
        )

        # 验证账户可用性
        if await self.authenticate_account(account):
            account.status = 'active'
            account.last_login = datetime.now()
            account.save()
            logger.info(f"成功添加账户: {username}")
        else:
            account.status = 'authentication_failed'
            account.save()
            logger.error(f"账户认证失败: {username}")

        return account

    async def update_account_status(self, account_id: int, status: str):
        """更新账户状态"""
        valid_statuses = ['active', 'inactive', 'error', 'resting', 'disabled']

        if status not in valid_statuses:
            raise ValueError(f"无效的账户状态: {status}")

        account = UserAccountModel.get_by_id(account_id)
        old_status = account.status
        account.status = status
        account.updated_at = datetime.now()
        account.save()

        logger.info(f"账户 {account.username} 状态从 {old_status} 更新为 {status}")

    async def authenticate_account(self, account: UserAccountModel) -> bool:
        """验证账户认证"""
        try:
            # 解密密码
            password = self.crypto_manager.decrypt_password(account.password)

            # 尝试登录
            session_manager = SessionManager()
            session = aiohttp.ClientSession()

            login_data = {
                'username': account.username,
                'password': password
            }

            async with session.post('/api/auth/login', json=login_data) as response:
                success = response.status == 200

            await session.close()
            return success

        except Exception as e:
            logger.error(f"账户认证异常: {e}")
            return False

    async def get_account_capabilities(self, account: UserAccountModel) -> Dict:
        """获取账户能力信息"""
        capabilities = {
            'max_concurrent_tasks': account.max_concurrent_tasks,
            'preferred_datasets': json.loads(account.preferred_datasets or '[]'),
            'status': account.status,
            'error_count': account.error_count,
            'last_login': account.last_login.isoformat() if account.last_login else None
        }

        # 添加实时性能信息
        scheduler = MultiAccountScheduler()
        if account.id in scheduler.account_performance:
            perf = scheduler.account_performance[account.id]
            capabilities.update({
                'success_rate': perf['success_rate'],
                'avg_response_time': perf['avg_response_time'],
                'consecutive_errors': perf['consecutive_errors']
            })

        return capabilities

    async def monitor_account_performance(self, account: UserAccountModel) -> Dict:
        """监控账户性能"""

        # 查询最近24小时的执行记录
        yesterday = datetime.now() - timedelta(days=1)

        recent_logs = (AccountExecutionLogModel
                      .select()
                      .where(
                          AccountExecutionLogModel.account == account,
                          AccountExecutionLogModel.created_at >= yesterday
                      ))

        performance_data = {
            'account_id': account.id,
            'username': account.username,
            'period': '24h',
            'total_tasks': 0,
            'successful_tasks': 0,
            'failed_tasks': 0,
            'avg_response_time': 0.0,
            'datasets_used': set(),
            'peak_concurrent_tasks': 0
        }

        if recent_logs:
            total_response_time = 0
            for log in recent_logs:
                performance_data['total_tasks'] += log.factors_processed
                performance_data['successful_tasks'] += log.factors_submitted
                performance_data['failed_tasks'] += log.error_count
                total_response_time += log.avg_response_time
                performance_data['datasets_used'].add(log.dataset_name)

            performance_data['avg_response_time'] = total_response_time / len(list(recent_logs))

        performance_data['datasets_used'] = list(performance_data['datasets_used'])
        performance_data['success_rate'] = (
            performance_data['successful_tasks'] / max(1, performance_data['total_tasks'])
        )

        return performance_data

class CryptoManager:
    """加密管理器"""

    def __init__(self):
        self.key = self._load_encryption_key()

    def _load_encryption_key(self):
        """加载加密密钥"""
        # 实际实现应该从安全的地方加载密钥
        return "your_secret_key_here"

    def encrypt_password(self, password: str) -> str:
        """加密密码"""
        # 实际实现应该使用更安全的加密算法
        import base64
        return base64.b64encode(password.encode()).decode()

    def decrypt_password(self, encrypted_password: str) -> str:
        """解密密码"""
        # 实际实现应该使用对应的解密算法
        import base64
        return base64.b64decode(encrypted_password.encode()).decode()

class AccountValidator:
    """账户验证器"""

    async def validate_account_info(self, username: str, password: str) -> Dict:
        """验证账户信息"""
        result = {'valid': True, 'message': ''}

        # 检查用户名格式
        if not username or len(username) < 3:
            result['valid'] = False
            result['message'] = '用户名长度至少3个字符'
            return result

        # 检查密码强度
        if not password or len(password) < 6:
            result['valid'] = False
            result['message'] = '密码长度至少6个字符'
            return result

        # 检查用户名是否已存在
        existing_account = (UserAccountModel
                           .select()
                           .where(UserAccountModel.username == username)
                           .first())

        if existing_account:
            result['valid'] = False
            result['message'] = '用户名已存在'

        return result
```

#### 6. 参数优化系统

```python
import numpy as np
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import GridSearchCV
from typing import Dict, List, Tuple, Any
import json

class ParameterOptimizer:
    """参数优化器 - 基于历史数据调优"""

    def __init__(self):
        self.execution_analyzer = ExecutionAnalyzer()
        self.ml_models = {}  # 每个数据集一个模型
        self.optimization_cache = {}
        self.parameter_ranges = {
            'delay': [1, 2, 3, 4, 5],
            'decay': [3, 6, 9, 12, 15, 18, 21],
            'universe': ['TOP500', 'TOP1000', 'TOP3000', 'TOP5000'],
            'region': ['CN', 'USA', 'EU'],
            'neutralization': ['SUBINDUSTRY', 'INDUSTRY', 'NONE']
        }

    async def analyze_account_performance(self, account: UserAccountModel,
                                        days_back: int = 30) -> Dict:
        """分析账户性能"""

        cutoff_date = datetime.now() - timedelta(days=days_back)

        # 获取执行记录
        execution_logs = (AccountExecutionLogModel
                         .select()
                         .where(
                             AccountExecutionLogModel.account == account,
                             AccountExecutionLogModel.date >= cutoff_date.date()
                         ))

        performance_analysis = {
            'account_id': account.id,
            'analysis_period': f'{days_back} days',
            'total_days': 0,
            'avg_daily_processed': 0,
            'avg_success_rate': 0,
            'avg_response_time': 0,
            'best_performing_datasets': [],
            'optimal_parameters': {},
            'performance_trends': [],
            'recommendations': []
        }

        if not execution_logs:
            performance_analysis['recommendations'].append(
                "账户缺乏历史执行数据，建议先运行基础配置收集数据"
            )
            return performance_analysis

        # 计算基础统计
        daily_stats = defaultdict(lambda: {
            'processed': 0, 'submitted': 0, 'response_time': [], 'errors': 0
        })

        for log in execution_logs:
            day_key = log.date.isoformat()
            daily_stats[day_key]['processed'] += log.factors_processed
            daily_stats[day_key]['submitted'] += log.factors_submitted
            daily_stats[day_key]['response_time'].append(log.avg_response_time)
            daily_stats[day_key]['errors'] += log.error_count

        # 聚合分析
        total_processed = sum(stats['processed'] for stats in daily_stats.values())
        total_submitted = sum(stats['submitted'] for stats in daily_stats.values())

        performance_analysis.update({
            'total_days': len(daily_stats),
            'avg_daily_processed': total_processed / len(daily_stats),
            'avg_success_rate': total_submitted / max(1, total_processed),
            'avg_response_time': np.mean([
                np.mean(stats['response_time']) for stats in daily_stats.values()
                if stats['response_time']
            ])
        })

        # 数据集性能分析
        dataset_performance = await self._analyze_dataset_performance(account, execution_logs)
        performance_analysis['best_performing_datasets'] = dataset_performance

        # 参数优化分析
        optimal_params = await self._find_optimal_parameters(account, execution_logs)
        performance_analysis['optimal_parameters'] = optimal_params

        # 性能趋势分析
        trends = await self._analyze_performance_trends(daily_stats)
        performance_analysis['performance_trends'] = trends

        # 生成建议
        recommendations = await self._generate_recommendations(performance_analysis)
        performance_analysis['recommendations'] = recommendations

        return performance_analysis

    async def _analyze_dataset_performance(self, account: UserAccountModel,
                                         execution_logs) -> List[Dict]:
        """分析各数据集的性能"""

        dataset_stats = defaultdict(lambda: {
            'total_processed': 0,
            'total_submitted': 0,
            'total_response_time': 0,
            'count': 0,
            'errors': 0
        })

        for log in execution_logs:
            dataset = log.dataset_name
            stats = dataset_stats[dataset]
            stats['total_processed'] += log.factors_processed
            stats['total_submitted'] += log.factors_submitted
            stats['total_response_time'] += log.avg_response_time
            stats['errors'] += log.error_count
            stats['count'] += 1

        # 计算综合性能分数
        dataset_performance = []
        for dataset, stats in dataset_stats.items():
            if stats['count'] > 0:
                success_rate = stats['total_submitted'] / max(1, stats['total_processed'])
                avg_response_time = stats['total_response_time'] / stats['count']
                error_rate = stats['errors'] / max(1, stats['total_processed'])

                # 综合评分（成功率70%，响应时间20%，错误率10%）
                performance_score = (
                    success_rate * 0.7 +
                    max(0, 1 - avg_response_time / 10) * 0.2 +
                    max(0, 1 - error_rate) * 0.1
                )

                dataset_performance.append({
                    'dataset': dataset,
                    'success_rate': success_rate,
                    'avg_response_time': avg_response_time,
                    'error_rate': error_rate,
                    'performance_score': performance_score,
                    'total_processed': stats['total_processed']
                })

        # 按性能分数排序
        dataset_performance.sort(key=lambda x: x['performance_score'], reverse=True)
        return dataset_performance[:5]  # 返回前5个表现最好的数据集

    async def suggest_optimal_parameters(self, account: UserAccountModel,
                                       dataset: str, use_ml: bool = True) -> Dict:
        """建议最优参数"""

        cache_key = f"{account.id}_{dataset}"
        if cache_key in self.optimization_cache:
            cached_result = self.optimization_cache[cache_key]
            if (datetime.now() - cached_result['timestamp']).hours < 6:
                return cached_result['parameters']

        if use_ml and await self._has_sufficient_data(account, dataset):
            optimal_params = await self._ml_parameter_optimization(account, dataset)
        else:
            optimal_params = await self._heuristic_parameter_optimization(account, dataset)

        # 缓存结果
        self.optimization_cache[cache_key] = {
            'parameters': optimal_params,
            'timestamp': datetime.now()
        }

        return optimal_params

    async def _has_sufficient_data(self, account: UserAccountModel, dataset: str) -> bool:
        """检查是否有足够的数据进行机器学习优化"""
        count = (AccountExecutionLogModel
                .select()
                .where(
                    AccountExecutionLogModel.account == account,
                    AccountExecutionLogModel.dataset_name == dataset
                )
                .count())
        return count >= 50  # 至少需要50条记录

    async def _ml_parameter_optimization(self, account: UserAccountModel,
                                       dataset: str) -> Dict:
        """使用机器学习进行参数优化"""

        # 获取训练数据
        training_data = await self._prepare_training_data(account, dataset)

        if len(training_data) < 10:
            return await self._heuristic_parameter_optimization(account, dataset)

        # 准备特征和目标变量
        X, y = self._prepare_features_and_targets(training_data)

        # 训练模型
        model_key = f"{account.id}_{dataset}"
        if model_key not in self.ml_models:
            self.ml_models[model_key] = RandomForestRegressor(
                n_estimators=100,
                random_state=42,
                max_depth=10
            )

        model = self.ml_models[model_key]
        model.fit(X, y)

        # 寻找最优参数组合
        best_params = await self._optimize_with_ml_model(model, X.columns)

        return best_params

    async def _prepare_training_data(self, account: UserAccountModel, dataset: str) -> List[Dict]:
        """准备训练数据"""
        logs = (AccountExecutionLogModel
               .select()
               .where(
                   AccountExecutionLogModel.account == account,
                   AccountExecutionLogModel.dataset_name == dataset
               ))

        training_data = []
        for log in logs:
            try:
                params = json.loads(log.parameters_config)
                training_data.append({
                    'delay': params.get('delay', 1),
                    'decay': params.get('decay', 6),
                    'universe': params.get('universe', 'TOP3000'),
                    'region': params.get('region', 'CN'),
                    'neutralization': params.get('neutralization', 'SUBINDUSTRY'),
                    'success_rate': log.success_rate,
                    'response_time': log.avg_response_time,
                    'performance_score': log.performance_score
                })
            except (json.JSONDecodeError, AttributeError):
                continue

        return training_data

    def _prepare_features_and_targets(self, training_data: List[Dict]) -> Tuple[Any, Any]:
        """准备机器学习的特征和目标变量"""
        import pandas as pd

        df = pd.DataFrame(training_data)

        # 编码分类变量
        categorical_features = ['universe', 'region', 'neutralization']
        for feature in categorical_features:
            df[feature] = pd.Categorical(df[feature]).codes

        # 特征列
        feature_columns = ['delay', 'decay', 'universe', 'region', 'neutralization']
        X = df[feature_columns]

        # 目标变量：综合性能分数
        y = df['performance_score']

        return X, y

    async def _optimize_with_ml_model(self, model, feature_columns) -> Dict:
        """使用训练好的模型寻找最优参数"""
        import pandas as pd
        from itertools import product

        # 生成参数组合
        param_combinations = list(product(
            self.parameter_ranges['delay'],
            self.parameter_ranges['decay'],
            [0, 1, 2, 3],  # universe编码
            [0, 1, 2],     # region编码
            [0, 1, 2]      # neutralization编码
        ))

        best_score = -1
        best_params = None

        # 评估每个参数组合
        for combo in param_combinations[:100]:  # 限制组合数量
            feature_vector = pd.DataFrame([combo], columns=feature_columns)
            predicted_score = model.predict(feature_vector)[0]

            if predicted_score > best_score:
                best_score = predicted_score
                best_params = {
                    'delay': combo[0],
                    'decay': combo[1],
                    'universe': self.parameter_ranges['universe'][combo[2]],
                    'region': self.parameter_ranges['region'][combo[3]],
                    'neutralization': self.parameter_ranges['neutralization'][combo[4]],
                    'predicted_score': predicted_score
                }

        return best_params or await self._get_default_parameters()

    async def _heuristic_parameter_optimization(self, account: UserAccountModel,
                                              dataset: str) -> Dict:
        """基于启发式规则的参数优化"""

        # 获取最近的成功案例
        recent_successful = (AccountExecutionLogModel
                           .select()
                           .where(
                               AccountExecutionLogModel.account == account,
                               AccountExecutionLogModel.dataset_name == dataset,
                               AccountExecutionLogModel.success_rate > 0.8
                           )
                           .order_by(AccountExecutionLogModel.date.desc())
                           .limit(10))

        if recent_successful:
            # 分析成功案例的参数模式
            param_stats = defaultdict(list)

            for log in recent_successful:
                try:
                    params = json.loads(log.parameters_config)
                    for key, value in params.items():
                        if key in self.parameter_ranges:
                            param_stats[key].append(value)
                except (json.JSONDecodeError, AttributeError):
                    continue

            # 选择最常用的参数
            optimal_params = {}
            for param, values in param_stats.items():
                if param in ['delay', 'decay']:
                    optimal_params[param] = int(np.median(values))
                else:
                    # 对于分类参数，选择最常见的
                    optimal_params[param] = max(set(values), key=values.count)

            return optimal_params

        return await self._get_default_parameters()

    async def _get_default_parameters(self) -> Dict:
        """获取默认参数"""
        return {
            'delay': 1,
            'decay': 6,
            'universe': 'TOP3000',
            'region': 'CN',
            'neutralization': 'SUBINDUSTRY'
        }

    async def record_execution_metrics(self, account: UserAccountModel,
                                     metrics: Dict):
        """记录执行指标"""

        AccountExecutionLogModel.create(
            account=account,
            date=datetime.now().date(),
            dataset_name=metrics.get('dataset', 'unknown'),
            stage=metrics.get('stage', 'unknown'),
            factors_processed=metrics.get('factors_processed', 0),
            factors_submitted=metrics.get('factors_submitted', 0),
            success_rate=metrics.get('success_rate', 0.0),
            avg_response_time=metrics.get('avg_response_time', 0.0),
            error_count=metrics.get('error_count', 0),
            parameters_config=json.dumps(metrics.get('parameters', {})),
            performance_score=metrics.get('performance_score', 0.0)
        )

    async def identify_performance_patterns(self, timeframe: str = '7d') -> List[Dict]:
        """识别性能模式"""

        if timeframe == '7d':
            cutoff = datetime.now() - timedelta(days=7)
        elif timeframe == '30d':
            cutoff = datetime.now() - timedelta(days=30)
        else:
            cutoff = datetime.now() - timedelta(days=1)

        # 查询时间段内的所有执行记录
        logs = (AccountExecutionLogModel
               .select()
               .where(AccountExecutionLogModel.date >= cutoff.date()))

        patterns = []

        # 1. 时间模式分析
        hourly_performance = defaultdict(list)
        for log in logs:
            hour = log.created_at.hour if log.created_at else 12
            hourly_performance[hour].append(log.success_rate)

        best_hours = []
        for hour, rates in hourly_performance.items():
            if len(rates) >= 3:  # 至少有3个数据点
                avg_rate = np.mean(rates)
                if avg_rate > 0.8:
                    best_hours.append({'hour': hour, 'avg_success_rate': avg_rate})

        if best_hours:
            patterns.append({
                'type': 'time_pattern',
                'description': '最佳执行时间段',
                'data': sorted(best_hours, key=lambda x: x['avg_success_rate'], reverse=True)
            })

        # 2. 参数组合模式
        param_performance = defaultdict(list)
        for log in logs:
            try:
                params = json.loads(log.parameters_config)
                key = f"decay_{params.get('decay', 6)}_delay_{params.get('delay', 1)}"
                param_performance[key].append(log.success_rate)
            except:
                continue

        best_param_combos = []
        for combo, rates in param_performance.items():
            if len(rates) >= 5:
                avg_rate = np.mean(rates)
                if avg_rate > 0.8:
                    best_param_combos.append({
                        'combination': combo,
                        'avg_success_rate': avg_rate,
                        'sample_size': len(rates)
                    })

        if best_param_combos:
            patterns.append({
                'type': 'parameter_pattern',
                'description': '最佳参数组合',
                'data': sorted(best_param_combos, key=lambda x: x['avg_success_rate'], reverse=True)
            })

        return patterns

    async def generate_optimization_recommendations(self) -> List[Dict]:
        """生成优化建议"""

        recommendations = []

        # 分析所有账户的性能
        all_accounts = UserAccountModel.select().where(UserAccountModel.status == 'active')

        for account in all_accounts:
            analysis = await self.analyze_account_performance(account, days_back=7)

            # 基于分析结果生成建议
            if analysis['avg_success_rate'] < 0.7:
                recommendations.append({
                    'type': 'performance_improvement',
                    'account_id': account.id,
                    'priority': 'high',
                    'message': f"账户 {account.username} 成功率较低 ({analysis['avg_success_rate']:.2%})，建议优化参数配置",
                    'suggested_actions': [
                        '检查最优参数配置',
                        '分析失败原因',
                        '考虑降低并发数',
                        '更换表现更好的数据集'
                    ]
                })

            if analysis['avg_response_time'] > 5.0:
                recommendations.append({
                    'type': 'performance_optimization',
                    'account_id': account.id,
                    'priority': 'medium',
                    'message': f"账户 {account.username} 响应时间较慢 ({analysis['avg_response_time']:.2f}s)",
                    'suggested_actions': [
                        '检查网络连接质量',
                        '优化请求参数',
                        '减少并发请求数',
                        '考虑使用响应更快的数据集'
                    ]
                })

        return recommendations

    async def test_parameter_combinations(self, account: UserAccountModel,
                                        param_sets: List[Dict],
                                        test_factors: int = 10) -> List[Dict]:
        """测试参数组合效果"""

        results = []

        for i, params in enumerate(param_sets):
            logger.info(f"测试参数组合 {i+1}/{len(param_sets)}: {params}")

            # 创建测试因子
            test_expressions = self._generate_test_expressions(test_factors)

            test_result = {
                'parameters': params,
                'test_id': f"test_{int(time.time())}_{i}",
                'success_count': 0,
                'total_count': len(test_expressions),
                'avg_response_time': 0,
                'errors': []
            }

            start_time = time.time()
            response_times = []

            # 测试每个因子
            for expr in test_expressions:
                try:
                    # 模拟API调用（实际实现中应该调用真实API）
                    response_time = await self._simulate_factor_test(expr, params, account)
                    response_times.append(response_time)
                    test_result['success_count'] += 1

                except Exception as e:
                    test_result['errors'].append(str(e))

            test_result['avg_response_time'] = np.mean(response_times) if response_times else 0
            test_result['success_rate'] = test_result['success_count'] / test_result['total_count']
            test_result['total_time'] = time.time() - start_time

            results.append(test_result)

            # 避免过于频繁的测试
            await asyncio.sleep(30)

        return results

    def _generate_test_expressions(self, count: int) -> List[str]:
        """生成测试用的因子表达式"""
        base_expressions = [
            "rank(close/delay(close,1))",
            "rank(volume)",
            "rank(ts_mean(close,5))",
            "rank(ts_std(close,10))",
            "rank(high-low)"
        ]

        test_expressions = []
        for i in range(count):
            base = base_expressions[i % len(base_expressions)]
            # 添加一些变化
            variations = [
                f"({base})",
                f"rank({base})",
                f"ts_mean({base}, 3)",
                f"delta({base}, 1)"
            ]
            test_expressions.append(variations[i % len(variations)])

        return test_expressions

    async def _simulate_factor_test(self, expression: str, params: Dict,
                                  account: UserAccountModel) -> float:
        """模拟因子测试（实际实现应该调用真实API）"""
        # 模拟网络延迟和处理时间
        base_latency = random.uniform(0.5, 2.0)
        param_factor = 1.0

        # 根据参数调整延迟（decay越大处理越慢）
        if 'decay' in params:
            param_factor += params['decay'] * 0.1

        simulated_time = base_latency * param_factor
        await asyncio.sleep(simulated_time)

        # 模拟一定的失败率
        if random.random() < 0.1:  # 10%失败率
            raise Exception("模拟API调用失败")

        return simulated_time

class ExecutionAnalyzer:
    """执行分析器 - 分析成功率和性能指标"""

    def __init__(self):
        self.cache = {}
        self.cache_ttl = 3600  # 1小时缓存

    async def calculate_success_rate_by_dataset(self, account: UserAccountModel,
                                              days_back: int = 30) -> Dict:
        """按数据集计算成功率"""

        cache_key = f"success_rate_{account.id}_{days_back}"
        if cache_key in self.cache:
            cached_time, cached_data = self.cache[cache_key]
            if time.time() - cached_time < self.cache_ttl:
                return cached_data

        cutoff_date = datetime.now() - timedelta(days=days_back)

        logs = (AccountExecutionLogModel
               .select()
               .where(
                   AccountExecutionLogModel.account == account,
                   AccountExecutionLogModel.date >= cutoff_date.date()
               ))

        dataset_stats = defaultdict(lambda: {'processed': 0, 'submitted': 0})

        for log in logs:
            dataset = log.dataset_name
            dataset_stats[dataset]['processed'] += log.factors_processed
            dataset_stats[dataset]['submitted'] += log.factors_submitted

        success_rates = {}
        for dataset, stats in dataset_stats.items():
            if stats['processed'] > 0:
                success_rates[dataset] = {
                    'success_rate': stats['submitted'] / stats['processed'],
                    'total_processed': stats['processed'],
                    'total_submitted': stats['submitted']
                }

        # 缓存结果
        self.cache[cache_key] = (time.time(), success_rates)

        return success_rates

    async def analyze_response_time_patterns(self, account: UserAccountModel) -> Dict:
        """分析响应时间模式"""

        logs = (AccountExecutionLogModel
               .select()
               .where(
                   AccountExecutionLogModel.account == account,
                   AccountExecutionLogModel.date >= (datetime.now() - timedelta(days=14)).date()
               ))

        # 按小时分析响应时间
        hourly_times = defaultdict(list)
        daily_times = defaultdict(list)

        for log in logs:
            if log.created_at:
                hour = log.created_at.hour
                day = log.created_at.weekday()
                hourly_times[hour].append(log.avg_response_time)
                daily_times[day].append(log.avg_response_time)

        analysis = {
            'hourly_patterns': {},
            'daily_patterns': {},
            'recommendations': []
        }

        # 分析每小时的模式
        for hour, times in hourly_times.items():
            if len(times) >= 3:
                analysis['hourly_patterns'][hour] = {
                    'avg_time': np.mean(times),
                    'std_time': np.std(times),
                    'sample_size': len(times)
                }

        # 分析每天的模式
        weekdays = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        for day, times in daily_times.items():
            if len(times) >= 3:
                analysis['daily_patterns'][weekdays[day]] = {
                    'avg_time': np.mean(times),
                    'std_time': np.std(times),
                    'sample_size': len(times)
                }

        # 生成建议
        if analysis['hourly_patterns']:
            best_hours = sorted(
                analysis['hourly_patterns'].items(),
                key=lambda x: x[1]['avg_time']
            )[:3]

            analysis['recommendations'].append(
                f"最佳执行时间: {', '.join([f'{h}:00' for h, _ in best_hours])}"
            )

        return analysis

    async def identify_optimal_execution_windows(self, account: UserAccountModel) -> List[Dict]:
        """识别最优执行时间窗口"""

        # 获取最近30天的执行记录
        logs = (AccountExecutionLogModel
               .select()
               .where(
                   AccountExecutionLogModel.account == account,
                   AccountExecutionLogModel.date >= (datetime.now() - timedelta(days=30)).date()
               ))

        # 按时间段分析性能
        time_windows = []

        # 按小时分组
        hourly_performance = defaultdict(lambda: {
            'success_rates': [], 'response_times': [], 'error_counts': []
        })

        for log in logs:
            if log.created_at:
                hour = log.created_at.hour
                hourly_performance[hour]['success_rates'].append(log.success_rate)
                hourly_performance[hour]['response_times'].append(log.avg_response_time)
                hourly_performance[hour]['error_counts'].append(log.error_count)

        # 评估每个时间窗口
        for hour, metrics in hourly_performance.items():
            if len(metrics['success_rates']) >= 5:  # 至少5个数据点
                avg_success_rate = np.mean(metrics['success_rates'])
                avg_response_time = np.mean(metrics['response_times'])
                avg_errors = np.mean(metrics['error_counts'])

                # 综合评分
                score = (
                    avg_success_rate * 0.6 +
                    max(0, 1 - avg_response_time / 10) * 0.3 +
                    max(0, 1 - avg_errors / 10) * 0.1
                )

                time_windows.append({
                    'hour': hour,
                    'time_range': f"{hour:02d}:00-{(hour+1)%24:02d}:00",
                    'avg_success_rate': avg_success_rate,
                    'avg_response_time': avg_response_time,
                    'avg_errors': avg_errors,
                    'performance_score': score,
                    'sample_size': len(metrics['success_rates'])
                })

        # 按性能分数排序
        time_windows.sort(key=lambda x: x['performance_score'], reverse=True)

        return time_windows

    async def compare_account_performance(self) -> Dict:
        """比较账户性能"""

        active_accounts = UserAccountModel.select().where(UserAccountModel.status == 'active')

        account_comparison = []

        for account in active_accounts:
            # 获取最近7天的统计
            week_ago = datetime.now() - timedelta(days=7)

            logs = (AccountExecutionLogModel
                   .select()
                   .where(
                       AccountExecutionLogModel.account == account,
                       AccountExecutionLogModel.date >= week_ago.date()
                   ))

            if logs:
                total_processed = sum(log.factors_processed for log in logs)
                total_submitted = sum(log.factors_submitted for log in logs)
                avg_response_time = np.mean([log.avg_response_time for log in logs])
                total_errors = sum(log.error_count for log in logs)

                account_comparison.append({
                    'account_id': account.id,
                    'username': account.username,
                    'total_processed': total_processed,
                    'total_submitted': total_submitted,
                    'success_rate': total_submitted / max(1, total_processed),
                    'avg_response_time': avg_response_time,
                    'total_errors': total_errors,
                    'error_rate': total_errors / max(1, total_processed)
                })

        # 按成功率排序
        account_comparison.sort(key=lambda x: x['success_rate'], reverse=True)

        comparison_result = {
            'account_rankings': account_comparison,
            'summary': {
                'total_accounts': len(account_comparison),
                'avg_success_rate': np.mean([acc['success_rate'] for acc in account_comparison]),
                'best_performer': account_comparison[0] if account_comparison else None,
                'worst_performer': account_comparison[-1] if account_comparison else None
            }
        }

        return comparison_result

    async def generate_performance_report(self, account: UserAccountModel) -> str:
        """生成性能报告"""

        # 收集各种分析数据
        success_rates = await self.calculate_success_rate_by_dataset(account)
        response_patterns = await self.analyze_response_time_patterns(account)
        optimal_windows = await self.identify_optimal_execution_windows(account)

        # 生成报告文本
        report_lines = [
            f"# 账户性能报告 - {account.username}",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "",
            "## 📊 数据集成功率分析"
        ]

        if success_rates:
            for dataset, stats in success_rates.items():
                report_lines.append(
                    f"- **{dataset}**: {stats['success_rate']:.2%} "
                    f"({stats['total_submitted']}/{stats['total_processed']})"
                )
        else:
            report_lines.append("- 暂无数据")

        report_lines.extend([
            "",
            "## ⏰ 最优执行时间窗口"
        ])

        if optimal_windows:
            for window in optimal_windows[:5]:
                report_lines.append(
                    f"- **{window['time_range']}**: "
                    f"成功率 {window['avg_success_rate']:.2%}, "
                    f"响应时间 {window['avg_response_time']:.2f}s"
                )
        else:
            report_lines.append("- 暂无足够数据")

        report_lines.extend([
            "",
            "## 📈 响应时间模式",
            ""
        ])

        if response_patterns['recommendations']:
            for rec in response_patterns['recommendations']:
                report_lines.append(f"- {rec}")
        else:
            report_lines.append("- 无特殊模式")

        return "\n".join(report_lines)
```

## 🔄 数据迁移策略

### 现有文件系统分析

当前 xin 目录的文件存储结构：

```
xin/records/
├── start_date.txt                               # 检测起始日期
├── submitable_alpha.csv                         # 可提交因子列表
├── analyst4_usa_1step_checked_alpha_id.txt      # 已检测因子ID
├── analyst4_usa_1step_simulated_alpha_expression.txt   # 已模拟表达式
└── analyst4_usa_2step_simulated_alpha_expression.txt   # 二级表达式
```

### 数据迁移器实现

```python
class DataMigrator:
    """数据迁移器 - 将旧系统数据迁移到新数据库"""

    def __init__(self):
        self.db = get_database()
        self.file_parser = FileParser()
        self.logger = get_logger(__name__)

    async def migrate_existing_data(self, records_path: str = "xin/records/"):
        """迁移现有的文件数据到数据库"""

        migration_report = {
            'start_time': datetime.now(),
            'files_processed': 0,
            'factors_migrated': 0,
            'errors': []
        }

        try:
            # 1. 迁移已模拟的因子表达式
            simulated_count = await self._migrate_simulated_expressions(records_path)
            migration_report['factors_migrated'] += simulated_count

            # 2. 迁移已检测的因子ID
            checked_count = await self._migrate_checked_factors(records_path)

            # 3. 迁移可提交的因子
            submittable_count = await self._migrate_submittable_factors(records_path)

            # 4. 创建初始批次
            batch_count = await self._create_initial_batches()

            # 5. 验证迁移结果
            validation_result = await self._validate_migration()

            migration_report.update({
                'end_time': datetime.now(),
                'simulated_factors': simulated_count,
                'checked_factors': checked_count,
                'submittable_factors': submittable_count,
                'initial_batches': batch_count,
                'validation': validation_result
            })

        except Exception as e:
            migration_report['errors'].append(str(e))
            self.logger.error(f"数据迁移失败: {e}")

        return migration_report

    async def _migrate_simulated_expressions(self, records_path: str) -> int:
        """迁移已模拟的表达式"""
        patterns = [
            "*_1step_simulated_alpha_expression.txt",
            "*_2step_simulated_alpha_expression.txt"
        ]

        total_migrated = 0

        for pattern in patterns:
            for file_path in glob.glob(os.path.join(records_path, pattern)):
                stage = "stage1" if "1step" in file_path else "stage2"
                dataset = self._extract_dataset_from_filename(file_path)

                expressions = []
                with open(file_path, 'r', encoding='utf-8') as f:
                    for line in f:
                        expression = line.strip()
                        if expression:
                            expressions.append(expression)

                if expressions:
                    # 使用队列管理器批量添加
                    queue_manager = AlphaQueue(self.db)
                    batch_id = queue_manager.add_factors_batch(
                        expressions=expressions,
                        source_dataset=dataset,
                        stage=stage,
                        tags=['migrated_from_file'],
                        priority=50  # 中等优先级
                    )

                    # 更新状态为已完成（因为这些是历史数据）
                    for expr in expressions:
                        factor = AlphaFactorQueueModel.get(
                            AlphaFactorQueueModel.factor_expression == expr,
                            AlphaFactorQueueModel.batch_id == batch_id
                        )
                        factor.status = 'completed'
                        factor.completed_at = datetime.now()
                        factor.save()

                    total_migrated += len(expressions)
                    self.logger.info(f"迁移了 {len(expressions)} 个因子从 {file_path}")

        return total_migrated

    async def _migrate_checked_factors(self, records_path: str) -> int:
        """迁移已检测的因子"""
        checked_files = glob.glob(os.path.join(records_path, "*_checked_alpha_id.txt"))
        total_checked = 0

        for file_path in checked_files:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line in f:
                    alpha_id = line.strip()
                    if alpha_id:
                        # 根据alpha_id找到对应的因子记录
                        try:
                            factor = AlphaFactorQueueModel.get(
                                AlphaFactorQueueModel.alpha_id == alpha_id
                            )
                            factor.status = 'checked'
                            factor.save()
                            total_checked += 1
                        except AlphaFactorQueueModel.DoesNotExist:
                            self.logger.warning(f"未找到alpha_id为 {alpha_id} 的因子记录")

        return total_checked

    async def _migrate_submittable_factors(self, records_path: str) -> int:
        """迁移可提交的因子"""
        csv_file = os.path.join(records_path, "submittable_alpha.csv")
        total_submittable = 0

        if os.path.exists(csv_file):
            try:
                df = pd.read_csv(csv_file)
                for _, row in df.iterrows():
                    alpha_id = row.get('alpha_id')
                    if alpha_id:
                        try:
                            factor = AlphaFactorQueueModel.get(
                                AlphaFactorQueueModel.alpha_id == alpha_id
                            )
                            factor.status = 'submittable'

                            # 更新质量指标
                            if 'sharpe' in row:
                                factor.sharpe = row['sharpe']
                            if 'fitness' in row:
                                factor.fitness = row['fitness']
                            if 'turnover' in row:
                                factor.turnover = row['turnover']

                            factor.save()
                            total_submittable += 1
                        except AlphaFactorQueueModel.DoesNotExist:
                            self.logger.warning(f"未找到alpha_id为 {alpha_id} 的因子记录")
            except Exception as e:
                self.logger.error(f"读取 submittable_alpha.csv 失败: {e}")

        return total_submittable

    def _extract_dataset_from_filename(self, file_path: str) -> str:
        """从文件名提取数据集名称"""
        filename = os.path.basename(file_path)
        if 'analyst4' in filename:
            return 'analyst4'
        elif 'news18' in filename:
            return 'news18'
        else:
            return 'unknown'

    async def _validate_migration(self) -> Dict:
        """验证迁移结果"""
        validation_result = {
            'total_factors': AlphaFactorQueueModel.select().count(),
            'by_status': {},
            'by_stage': {},
            'by_dataset': {},
            'integrity_check': True
        }

        # 统计各状态的因子数量
        status_stats = (AlphaFactorQueueModel
                       .select(AlphaFactorQueueModel.status, fn.COUNT().alias('count'))
                       .group_by(AlphaFactorQueueModel.status))

        validation_result['by_status'] = {stat.status: stat.count for stat in status_stats}

        # 检查数据完整性
        orphaned_logs = AlphaProcessingLogModel.select().join(
            AlphaFactorQueueModel, JOIN.LEFT_OUTER
        ).where(AlphaFactorQueueModel.id.is_null()).count()

        if orphaned_logs > 0:
            validation_result['integrity_check'] = False
            validation_result['orphaned_logs'] = orphaned_logs

        return validation_result
```

### 队列处理流程

#### 状态流转图

```
Stage1: [pending] → [processing] → [completed] ─┐
                ↓                                │
           [skipped/failed]                      │
                                                │
Stage2: [pending] → [processing] → [completed] ─┤
                ↓                                │
           [skipped/failed]                      │
                                                │
Stage3: [pending] → [processing] → [completed] ─┤
                ↓                                │
           [skipped/failed]                      │
                                                │
Check:  [pending] → [processing] → [checked] ───┤
                ↓                                │
           [failed]                              │
                                                │
Submit: [pending] → [processing] → [submitted] ─┘
                ↓
           [failed] → [pending] (重试，直到max_retries)
                          ↓
                     [failed] (最终失败)
```

#### 详细处理流程

```python
class QueueProcessor:
    """队列处理器 - 管理因子在各阶段间的流转"""

    def __init__(self):
        self.queue = AlphaQueue()
        self.workers = {
            'stage1': Step1Worker(),
            'stage2': Step2Worker(),
            'stage3': Step3Worker(),  # 新增第三阶段
            'check': CheckWorker(),
            'submit': SubmitWorker()
        }

    async def process_stage_pipeline(self, stage: str, batch_size: int = 50):
        """处理特定阶段的管道流程"""

        while True:
            # 获取待处理的因子
            pending_factors = self.queue.get_pending_factors(
                stage=stage,
                limit=batch_size
            )

            if not pending_factors:
                await asyncio.sleep(30)  # 无任务时等待
                continue

            # 并行处理因子
            worker = self.workers[stage]
            await worker.process_batch(pending_factors)

            # 自动晋级符合条件的因子
            await self._promote_qualified_factors(stage)

    async def _promote_qualified_factors(self, current_stage: str):
        """自动晋级符合条件的因子到下一阶段"""

                 stage_mapping = {
             'stage1': 'stage2',
             'stage2': 'stage3',
             'stage3': 'check',
             'check': 'submit'
         }

        next_stage = stage_mapping.get(current_stage)
        if not next_stage:
            return

        # 获取当前阶段已完成的因子
        completed_factors = (AlphaFactorQueueModel
                           .select()
                           .where(
                               AlphaFactorQueueModel.stage == current_stage,
                               AlphaFactorQueueModel.status == 'completed'
                           ))

        promoted_count = 0
        for factor in completed_factors:
            if self._meets_promotion_criteria(factor, current_stage):
                # 创建下一阶段的因子记录
                new_factor = AlphaFactorQueueModel.create(
                    factor_expression=factor.factor_expression,
                    source_dataset=factor.source_dataset,
                    stage=next_stage,
                    status='pending',
                    priority=self._calculate_promotion_priority(factor),
                    region=factor.region,
                    universe=factor.universe,
                    delay=factor.delay,
                    decay=factor.decay,
                    submitted_at=datetime.now(),
                    # 继承父因子的性能数据
                    sharpe=factor.sharpe,
                    fitness=factor.fitness,
                    turnover=factor.turnover
                )

                promoted_count += 1

        if promoted_count > 0:
            self.logger.info(f"自动晋级了 {promoted_count} 个因子从 {current_stage} 到 {next_stage}")

    def _meets_promotion_criteria(self, factor: AlphaFactorQueueModel, stage: str) -> bool:
        """判断因子是否符合晋级条件"""

                 criteria_map = {
             'stage1': lambda f: f.sharpe and f.sharpe > 1.5 and f.turnover and f.turnover < 0.3,
             'stage2': lambda f: f.sharpe and f.sharpe > 1.0 and f.fitness and f.fitness > 0.75,
             'stage3': lambda f: f.sharpe and f.sharpe > 2.0 and f.fitness and f.fitness > 0.05,
             'check': lambda f: True  # 检测通过的都可以提交
         }

        criteria_func = criteria_map.get(stage)
        return criteria_func(factor) if criteria_func else False

    def _calculate_promotion_priority(self, factor: AlphaFactorQueueModel) -> int:
        """计算晋级因子的优先级"""

        base_priority = 50

        # 根据性能指标调整优先级
        if factor.sharpe:
            base_priority += min(int(factor.sharpe * 10), 30)

        if factor.fitness:
            base_priority += min(int(factor.fitness * 100), 20)

                 # 确保优先级在有效范围内
         return min(max(base_priority, 0), 100)

### 阶段处理流程详述

#### 1. 因子生成阶段 (Stage1)

- 从数据集字段生成一阶因子表达式
- 创建 stage1 的 pending 因子
- Worker 获取 pending 因子进行模拟
- 成功后状态变为 completed，失败后进入重试或 failed
- 符合条件的因子自动晋级到 stage2

#### 2. 因子组合阶段 (Stage2)

- 从 stage1 completed 的因子中选择优质因子
- 创建 stage2 的 pending 因子
- 基于 turnover 智能调整 decay 参数
- 重复模拟和状态更新流程
- 符合条件的因子自动晋级到 stage3

#### 3. 交易条件因子阶段 (Stage3)

- 从 stage2 completed 的因子中选择高质量因子
- 创建 stage3 的 pending 因子
- 使用 trade_when 生成多种交易条件因子
- 测试每种交易条件，选择最佳性能
- 符合条件的因子自动晋级到检查阶段

#### 4. 质量检测阶段 (Check)

- 从 stage3 completed 的因子中选择
- 创建 check 阶段的 pending 因子
- 进行自相关性和产品相关性检测
- 通过检测的因子状态变为 submittable
- 符合条件的因子自动晋级到提交阶段

#### 5. 智能提交阶段 (Submit)

- 从 check submittable 的因子中选择
- 创建 submit 阶段的 pending 因子
- 按自相关性排序优化提交顺序
- 使用30分钟超时的智能提交机制
- 成功提交后记录最终状态并发送通知
```

## 🤖 机器学习集成到三阶段处理的完整实施方案

### ML 集成架构设计

基于前述的新架构设计，我们将机器学习功能深度集成到现有的一二三阶段因子处理流程中，实现从"传统批量测试"到"AI 驱动智能挖掘"的全面升级。

#### 集成设计原则

1. **渐进增强**：保持现有流程稳定，ML 功能作为增强层逐步引入
2. **智能兼容**：ML 与传统方法混合运行，基于性能自适应选择
3. **跨阶段优化**：前一阶段的 ML 学习成果指导后续阶段处理
4. **完全向下兼容**：确保与现有 digging_1step.py、digging_2step.py、digging_3step.py 等脚本 100%兼容

#### ML 增强的三阶段处理架构

```mermaid
graph TD
    A[原始数据集] --> B[ML增强Stage1<br/>智能因子生成+预筛选]
    B --> C[ML增强Stage2<br/>智能组合+参数优化]
    C --> D[ML增强Stage3<br/>智能trade_when生成]
    D --> E[传统Check阶段]
    E --> F[智能Submit阶段]

    B -.-> B1[传统Stage1备选]
    C -.-> C1[传统Stage2备选]
    D -.-> D1[传统Stage3备选]

    G[ML系统监控] --> H[性能对比分析]
    H --> I[自适应模式切换]
    I --> B
    I --> C
    I --> D
```

### 🔬 第一阶段：ML 增强的因子生成和筛选

#### 核心 ML 功能集成

基于原有`digging_1step.py`的逻辑，在`MLEnhancedStep1Worker`中集成以下 ML 功能：

##### 1.1 智能因子生成

```python
class MLEnhancedStep1Worker(BaseWorker):
    """ML增强的第一阶段因子处理器"""

    async def _intelligent_factor_generation(self):
        """智能因子生成 - 替代传统穷举方法"""

        # 基于遗传算法的因子进化
        evolved_factors = await self.genetic_evolver.evolve_factors(
            base_dataset=dataset,
            target_metrics={'sharpe': 1.5, 'fitness': 0.3},
            population_size=50,
            generations=20
        )

        # ML质量预评估
        quality_scores = await self.quality_predictor.predict_batch(evolved_factors)

        # 选择高质量因子（相比传统方法减少90%候选）
        high_quality_factors = [
            factor for factor, score in zip(evolved_factors, quality_scores)
            if score >= 0.6
        ]
```

##### 1.2 ML 质量预筛选

取代传统的全量测试，使用训练好的 ML 模型提前筛选：

```python
async def _ml_quality_prefilter(self, factors):
    """ML预筛选 - 过滤低质量因子避免无效API调用"""

    # 批量质量预测
    quality_scores = await self.quality_predictor.predict_batch(expressions)

    # 筛选阈值可动态调整
    qualified_factors = []
    for factor, score in zip(factors, quality_scores):
        if score >= self.quality_threshold:  # 默认0.6
            qualified_factors.append(factor)
        else:
            # 直接标记为ML过滤，节省API调用
            self.queue.update_factor_status(factor.id, 'ml_filtered')

    # 预期效果：过滤70%低质量候选，减少无效计算
    return qualified_factors
```

##### 1.3 智能参数优化

集成贝叶斯优化替代网格搜索：

```python
async def _ml_parameter_optimization(self, factors):
    """ML参数优化 - 贝叶斯优化替代网格搜索"""

    for factor in factors:
        # 使用贝叶斯优化寻找最优参数（相比网格搜索减少80%测试次数）
        optimal_params = await self.ml_engine.bayesian_optimize_parameters(
            factor.factor_expression,
            optimization_budget=20,  # 仅需20次评估vs传统420次
            target_metrics=['sharpe', 'fitness', 'turnover']
        )

        # 更新因子参数
        factor.delay = optimal_params['delay']
        factor.decay = optimal_params['decay']
        factor.universe = optimal_params['universe']
```

#### 与原有逻辑的兼容性

```python
# 保持与原有execute_factor_processing接口完全兼容
async def execute_factor_processing(self, factor, account) -> Dict:
    """执行因子处理 - 兼容原有接口"""

    # 保持与原有BaseWorker接口兼容
    request_data = {
        'expression': factor.factor_expression,
        'universe': factor.universe or 'TOP3000',
        'region': factor.region or 'CN',
        'delay': factor.delay or 1,
        'decay': factor.decay or 9
    }

    # 调用原有API逻辑
    response = await self.api_client.robust_request('/alpha/simulation', request_data, account)
    result = self.parse_simulation_result(response)

    # 保持原有晋级逻辑
    if self.meets_stage2_criteria(result):
        await self.promote_to_stage2(factor, result)

    return result
```

### 🔄 第二阶段：ML 增强的因子组合和优化

#### 核心 ML 功能集成

基于原有`digging_2step.py`的逻辑，在`MLEnhancedStep2Worker`中集成：

##### 2.1 智能因子组合

```python
class MLEnhancedStep2Worker(BaseWorker):
    """ML增强的第二阶段因子处理器"""

    async def _multi_strategy_combination(self, factor_groups):
        """多策略因子组合生成 - 替代简单的二阶组合"""

        combined_factors = []

        for strategy in ['linear', 'nonlinear', 'conditional']:
            # 线性组合：智能权重预测
            if strategy == 'linear':
                optimal_weights = await self.factor_combiner.predict_optimal_weights(
                    factor1.factor_expression, factor2.factor_expression
                )
                combined_expr = f"({w1} * ({expr1}) + {w2} * ({expr2}))"

            # 非线性组合：乘积、比率
            elif strategy == 'nonlinear':
                combinations = [
                    f"rank(({expr1}) * ({expr2}))",  # 乘积
                    f"rank(({expr1}) / (abs({expr2}) + 0.001))"  # 比率
                ]

            # 条件组合：基于市场状态的因子切换
            elif strategy == 'conditional':
                conditions = [
                    "ts_mean(volume, 5) > ts_mean(volume, 20)",  # 高成交量
                    "ts_std(close, 10) > ts_mean(ts_std(close, 10), 20)"  # 高波动
                ]
                combined_expr = f"if({condition}, {expr1}, {expr2})"
```

##### 2.2 多目标参数优化

集成多目标优化替代传统的单一 decay 调整：

```python
async def _ml_parameter_optimization(self, factors):
    """ML多目标参数优化 - 平衡收益、风险、换手率"""

    for factor in factors:
        # 保持原有的turnover-based decay逻辑作为基础
        predicted_turnover = await self._predict_factor_turnover(factor)
        base_decay = self._calculate_optimal_decay(predicted_turnover)  # 原有逻辑

        # ML多目标优化增强
        optimal_params = await self.parameter_optimizer.optimize_factor_parameters(
            factor.factor_expression,
            base_params={'decay': base_decay},
            constraints={
                'max_turnover': 0.3,
                'min_sharpe': 1.0,
                'max_drawdown': 0.15
            }
        )

        # 更新参数
        factor.decay = optimal_params['decay']
        factor.delay = optimal_params['delay']
        factor.universe = optimal_params['universe']
```

#### 保持原有 decay 计算逻辑

```python
def _calculate_optimal_decay(self, turnover: float) -> int:
    """根据turnover计算最优decay - 保持原有逻辑"""
    # 完全保持digging_2step.py中的逻辑
    if turnover < 0.1:
        return 15
    elif turnover < 0.2:
        return 12
    elif turnover < 0.3:
        return 9
    else:
        return 6
```

### ⚡ 第三阶段：ML 增强的交易条件因子生成

#### 核心 ML 功能集成

基于原有`digging_3step.py`的逻辑，在`MLEnhancedStep3Worker`中集成：

##### 3.1 市场环境感知的 trade_when 生成

```python
class MLEnhancedStep3Worker(BaseWorker):
    """ML增强的第三阶段因子处理器"""

    async def _intelligent_trade_when_generation(self, factors, market_regime):
        """智能生成trade_when因子变体 - 基于市场regime选择策略"""

        for base_factor in factors:
            # 基于市场regime选择合适的trade_when策略
            strategies = await self._select_regime_appropriate_strategies(
                base_factor, market_regime
            )

            # 为每个策略生成trade_when变体
            factor_variants = []
            for strategy in strategies:
                if strategy == 'momentum_continuation':
                    variants = self._generate_momentum_variants(base_factor)
                elif strategy == 'mean_reversion':
                    variants = self._generate_reversal_variants(base_factor)
                elif strategy == 'volatility_breakout':
                    variants = self._generate_volatility_variants(base_factor)

                factor_variants.extend(variants)

            # 限制变体数量并选择最优的
            if len(factor_variants) > 8:
                factor_variants = await self._select_best_variants(factor_variants, 8)
```

##### 3.2 智能 trade_when 条件生成

集成多种智能策略替代原有固定模板：

```python
def _generate_momentum_variants(self, base_factor, base_expr):
    """生成动量策略变体 - 扩展原有trade_when_factory逻辑"""

    # 保持原有的基础条件
    momentum_conditions = [
        f"{base_expr} > delay({base_expr}, 1)",  # 原有逻辑
        f"{base_expr} > delay({base_expr}, 2)",  # 原有逻辑
        # ML增强的条件
        f"{base_expr} > ts_mean({base_expr}, 5)",
        f"delta({base_expr}, 1) > 0",
        f"ts_mean({base_expr}, 3) > ts_mean({base_expr}, 10)"
    ]

    variants = []
    for condition in momentum_conditions:
        trade_when_expr = f"trade_when({base_expr}, {condition})"
        # 创建AlphaFactorQueueModel记录
        variants.append(self._create_trade_when_factor(trade_when_expr, base_factor))

    return variants
```

### 🎛️ 统一调度器 ML 集成设计

#### 增强原有 FactorDiggingScheduler 支持 ML 模式

将 ML 功能直接集成到原有的`FactorDiggingScheduler`中，避免重复设计：

```python
class FactorDiggingScheduler:
    """统一的因子挖掘调度器 - 支持传统和ML两种模式"""

    def __init__(self, quiet: bool = False, ml_mode: str = 'traditional'):
        self.task_queue = AlphaQueue()
        self.account_scheduler = MultiAccountScheduler()
        self.monitor = FactorDiggingMonitor()
        self.logger = SimpleLogger("Scheduler", quiet)

        # ML模式配置
        self.ml_mode = ml_mode  # 'traditional', 'hybrid', 'full_ml'

        # 根据模式选择Worker
        if ml_mode in ['hybrid', 'full_ml']:
            # ML增强的Worker组件
            self.workers = {
                'step1': MLEnhancedStep1Worker("ML-Step1", quiet),
                'step2': MLEnhancedStep2Worker("ML-Step2", quiet),
                'step3': MLEnhancedStep3Worker("ML-Step3", quiet),
                'check': CheckWorker("Check", quiet),
                'submit': SubmitWorker("Submit", quiet)
            }

            # 传统Worker作为备选
            self.fallback_workers = {
                'step1': Step1Worker("Fallback-Step1", quiet),
                'step2': Step2Worker("Fallback-Step2", quiet),
                'step3': Step3Worker("Fallback-Step3", quiet)
            }
        else:
            # 纯传统模式
            self.workers = {
                'step1': Step1Worker("Step1", quiet),
                'step2': Step2Worker("Step2", quiet),
                'step3': Step3Worker("Step3", quiet),
                'check': CheckWorker("Check", quiet),
                'submit': SubmitWorker("Submit", quiet)
            }

    async def run_pipeline(self):
        """运行完整的挖掘流水线 - 自动选择最优模式"""

        self.logger.info(f"🚀 WQ因子挖掘系统启动 (模式: {self.ml_mode})")

        while True:
            try:
                # 智能模式决策（仅在hybrid模式下）
                if self.ml_mode == 'hybrid':
                    await self._adaptive_mode_selection()

                # 执行各阶段处理
                await self.schedule_step1_tasks()
                await self.schedule_step2_tasks()
                await self.schedule_step3_tasks()
                await self.schedule_check_tasks()
                await self.schedule_submit_tasks()

                await self.monitor.update_stats()
                await asyncio.sleep(60)  # 调度间隔

            except Exception as e:
                self.logger.error(0, f"调度器异常: {e}", 0)
                await self.handle_scheduler_error(e)
                await asyncio.sleep(30)

    async def _adaptive_mode_selection(self):
        """智能自适应模式选择（仅hybrid模式）"""

        # 评估ML vs 传统方法的性能
        ml_performance = await self._evaluate_ml_performance()

        # 动态切换Worker
        for stage in ['step1', 'step2', 'step3']:
            stage_ml_success_rate = ml_performance.get(f'{stage}_success_rate', 0.5)

            if stage_ml_success_rate >= 0.75:
                # 使用ML增强Worker
                if f'ml_{stage}' in self.workers:
                    continue  # 已经是ML Worker
            else:
                # 切换到传统Worker
                if hasattr(self, 'fallback_workers'):
                    self.workers[stage] = self.fallback_workers[stage]
```

### 🚀 app.py 统一入口集成 ML 功能

#### 增强 app.py 支持 ML 模式切换

```python
# src/app.py - 统一入口程序
import argparse
import asyncio
from lib.schedulers import FactorDiggingScheduler

async def main():
    """主入口函数"""
    parser = argparse.ArgumentParser(description="WQ因子挖掘系统")

    # 基础参数
    parser.add_argument("--quiet", action='store_true', help="静默模式")
    parser.add_argument("--status", action='store_true', help="显示状态")

    # ML模式参数
    parser.add_argument("--ml-mode", choices=['traditional', 'hybrid', 'full_ml'],
                       default='traditional', help="ML运行模式")
    parser.add_argument("--ml-ratio", type=float, default=0.5,
                       help="混合模式下的ML使用比例 (0.0-1.0)")

    # 调试参数
    parser.add_argument("--log-level", choices=['DEBUG', 'INFO', 'WARNING', 'ERROR'],
                       default='INFO', help="日志级别")
    parser.add_argument("--mode", choices=['auto', 'monitor', 'tail-logs'],
                       default='auto', help="运行模式")

    args = parser.parse_args()

    # 创建统一调度器
    scheduler = FactorDiggingScheduler(
        quiet=args.quiet,
        ml_mode=args.ml_mode
    )

    if args.status:
        # 显示状态
        await scheduler.show_status()
    elif args.mode == 'monitor':
        # 监控模式
        await scheduler.run_monitor_mode()
    else:
        # 自动运行模式
        await scheduler.run_pipeline()

if __name__ == "__main__":
    asyncio.run(main())
```

#### 统一使用方法

```bash
# 传统模式（默认）
python app.py

# 混合模式（推荐）
python app.py --ml-mode=hybrid

# 完全ML模式
python app.py --ml-mode=full_ml

# 查看ML vs 传统方法效果对比
python app.py --status --ml-mode=hybrid

# 静默ML模式运行
python app.py --ml-mode=hybrid --quiet

# 调试模式
python app.py --ml-mode=hybrid --log-level=DEBUG
```

### 📊 集成效果预期

#### 量化改进目标

| 指标         | 传统方法       | ML 增强方法     | 改进幅度         |
| ------------ | -------------- | --------------- | ---------------- |
| 因子生成效率 | 10 万候选      | 1 千高质量候选  | 减少 90%无效候选 |
| 参数优化速度 | 420 次网格搜索 | 50 次贝叶斯优化 | 提升 8 倍速度    |
| 质量预筛选   | 全量测试       | 70%预筛选过滤   | 减少 70%API 调用 |
| 整体成功率   | 20%            | 35%+            | 提升 75%         |
| 处理时间     | 基准           | 减少 50%        | 提升 100%效率    |

#### 系统兼容性保证

```python
# 完全向下兼容设计
✅ 原有API接口保持不变
✅ 原有Worker可作为备选方案
✅ 数据库模型向下兼容
✅ 配置文件格式兼容
✅ 命令行参数兼容
✅ 可随时切换回传统模式
```

## 📅 实施计划

### 第一阶段：核心架构搭建 (Week 1-2)

#### 1.1 扩展数据模型和日志系统 (Day 1-3)

- [ ] **设计数据模型架构**

  - [ ] 定义 AlphaFactorQueueModel 字段和关系
  - [ ] 设计 UserAccountModel 多账户支持
  - [ ] 规划 AccountExecutionLogModel 执行记录结构
  - [ ] 创建 ParameterOptimizationModel 参数优化模型

- [ ] **实现简化日志系统**

  - [ ] 创建 SimpleLogger 类 (src/lib/simple_logger.py)
  - [ ] 集成 rich 组件基础控制台输出
  - [ ] 设计简单的文件日志记录

- [ ] **实现数据库迁移**

  - [ ] 编写数据库迁移脚本 (src/migrations/001_initial_schema.py)
  - [ ] 创建索引优化查询性能
  - [ ] 设置外键约束和数据完整性检查
  - [ ] 测试迁移脚本的正确性

- [ ] **队列管理器基础功能**
  - [ ] 实现 AlphaQueue.add_factors() 批量添加
  - [ ] 实现 AlphaQueue.get_pending_factors() 智能查询
  - [ ] 实现 AlphaQueue.update_factor_status() 状态更新
  - [ ] 添加队列统计和清理功能

#### 1.2 迁移核心算法 (Day 4-7)

- [ ] **分析现有 xin 目录代码**

  - [ ] 解析 machine_lib.py 中的核心算法
  - [ ] 提取 digging_1step.py 的因子生成逻辑
  - [ ] 分析 digging_2step.py 的参数调优机制
  - [ ] 理解 digging_3step.py 的 trade_when 因子生成逻辑
  - [ ] 分析 check.py 的质量检测算法
  - [ ] 研究 s7.py 的智能提交和监控机制

- [ ] **重构核心组件**

  - [ ] 创建 src/lib/factor_generator.py 一阶因子生成器
  - [ ] 实现 src/lib/factor_combiner.py 二阶因子组合器
  - [ ] 移植 src/lib/trade_when_generator.py 第三阶段交易条件因子生成器
  - [ ] 实现 src/lib/quality_checker.py 质量检测器
  - [ ] 移植 src/lib/parameter_optimizer.py 参数优化器
  - [ ] 封装 src/lib/api_client.py 异步 API 客户端
  - [ ] 创建 src/lib/smart_submitter.py 智能提交器（基于 s7.py）

- [ ] **创建 ML 组件基础接口（P0 阻塞级优先）**

  - [ ] 创建 src/lib/ml/ 目录和 **init**.py
  - [ ] 实现 FactorIntelligenceEngine 基础接口
  - [ ] 实现 GeneticFactorEvolution 基础接口
  - [ ] 实现 FactorQualityPredictor 基础接口
  - [ ] 实现 IntelligentFactorCombiner 基础接口
  - [ ] 实现 MultiObjectiveOptimizer 基础接口
  - [ ] 实现 PerformancePredictor 基础接口
  - [ ] 实现 IntelligentTradeWhenGenerator 基础接口
  - [ ] 实现 MarketRegimeDetector 基础接口
  - [ ] 实现 FactorTimingOptimizer 基础接口
  - [ ] 确保 ML Worker 可以正常导入和启动

- [ ] **增强容错机制**
  - [ ] 实现 RobustAPIClient 带重试的 API 客户端
  - [ ] 添加 CircuitBreaker 熔断器模式
  - [ ] 集成 SessionManager 增强会话管理
  - [ ] 创建异常分类和处理策略

#### 1.3 基础工作进程框架 (Day 8-10)

- [ ] **实现 BaseWorker 抽象基类**

  - [ ] 定义通用的处理流程接口
  - [ ] 实现并发控制和信号量机制
  - [ ] 添加错误处理和重试逻辑
  - [ ] 集成监控指标收集
  - [ ] **集成简化日志功能**
  - [ ] 在 BaseWorker 中集成 SimpleLogger
  - [ ] 记录批次处理开始和结束

- [ ] **创建具体 Worker 实现**

  - [ ] 实现 Step1Worker 第一阶段处理器
  - [ ] 实现 Step2Worker 第二阶段处理器
  - [ ] 实现 Step3Worker 第三阶段 trade_when 处理器
  - [ ] 基础的因子表达式处理和 API 调用
  - [ ] 性能数据解析和状态更新
  - [ ] 自动晋级到下一阶段的逻辑
  - [ ] 集成智能提交 SubmitWorker（基于 s7.py 逻辑）

  - [ ] **在各 Worker 中集成基础日志**

  - [ ] 记录因子处理成功/失败状态
  - [ ] 简化错误信息记录

- [ ] **集成测试**
  - [ ] 编写 Worker 单元测试
  - [ ] 测试 API 客户端容错机制
  - [ ] 验证数据库操作正确性
  - [ ] 集成测试端到端流程

### 第二阶段：多阶段处理器 (Week 3-4)

#### 2.1 完整工作流实现 (Day 11-16)

- [ ] **实现 Step2Worker 第二阶段处理器**

  - [ ] 基于第一阶段结果的智能 decay 调整
  - [ ] turnover 分析和参数优化算法
  - [ ] 复合因子生成和组合策略
  - [ ] 性能提升验证和阶段晋级判断

- [ ] **实现 CheckWorker 质量检测器**

  - [ ] 自相关性检测 API 调用和结果解析
  - [ ] 产品相关性检测和阈值判断
  - [ ] 多维度质量评估综合算法
  - [ ] 检测结果缓存和优化策略

- [ ] **实现 SubmitWorker 因子提交器**
  - [ ] 因子提交 API 封装和错误处理
  - [ ] 提交状态跟踪和结果记录
  - [ ] 成功提交事件通知机制
  - [ ] 提交失败重试和降级策略

#### 2.2 队列系统优化 (Day 17-20)

- [ ] **实现优先级队列机制**

  - [ ] 基于历史成功率的优先级算法
  - [ ] 因子复杂度和处理成本评估
  - [ ] 动态优先级调整策略
  - [ ] 优先级队列的数据库索引优化

- [ ] **批量处理功能增强**

  - [ ] 智能批量大小自适应调整
  - [ ] 并发批次处理和资源管理
  - [ ] 批量失败处理和部分回滚
  - [ ] 批量处理性能监控和调优

- [ ] **数据库查询性能优化**
  - [ ] 复合索引设计和查询计划优化
  - [ ] 分页查询和游标机制
  - [ ] 定期数据清理和归档策略
  - [ ] 查询缓存和 Redis 集成

#### 2.3 异常处理增强 (Day 21-23)

- [ ] **智能重试机制**

  - [ ] 指数退避重试策略实现
  - [ ] 基于错误类型的重试决策
  - [ ] 重试次数限制和熔断机制
  - [ ] 重试状态持久化和恢复

- [ ] **错误分类和处理策略**

  - [ ] 网络错误、API 限制、逻辑错误分类
  - [ ] 针对性的错误处理和恢复策略
  - [ ] 错误统计和趋势分析
  - [ ] 错误模式识别和预防机制

- [ ] **断点续传功能**
  - [ ] 处理状态快照和持久化
  - [ ] 中断恢复时的状态一致性检查
  - [ ] 部分完成任务的续传机制
  - [ ] 恢复过程的进度监控

#### 2.4 多账户管理系统 (Day 24-28)

- [ ] **账户模型和管理功能**

  - [ ] UserAccountModel 扩展字段设计
  - [ ] 账户加密存储和安全机制
  - [ ] 账户状态生命周期管理
  - [ ] 账户配置和偏好设置

- [ ] **多账户调度器框架**

  - [ ] MultiAccountScheduler 核心算法
  - [ ] 账户能力评估和匹配算法
  - [ ] 任务分配公平性算法
  - [ ] 调度决策的可解释性

- [ ] **账户监控和健康检查**

  - [ ] 实时账户状态监控
  - [ ] 账户性能指标收集和分析
  - [ ] 异常账户检测和隔离
  - [ ] 账户健康评分算法

- [ ] **负载均衡和轮换机制**
  - [ ] 动态负载均衡算法实现
  - [ ] 账户轮换策略和时机控制
  - [ ] 故障转移和容灾机制
  - [ ] 负载均衡效果监控和调优

### 第三阶段：监控和通知 (Week 5-6)

#### 3.1 统计监控系统 (Day 29-34)

- [ ] **实时统计收集引擎**

  - [ ] FactorDiggingMonitor 核心监控器实现
  - [ ] MetricsCollector 指标收集器
  - [ ] 实时数据聚合和计算算法
  - [ ] 监控数据的时序存储和查询优化

- [ ] **性能指标面板**

  - [ ] 各阶段处理速度和成功率监控
  - [ ] 账户性能对比和排名展示
  - [ ] 系统资源使用情况监控
  - [ ] 队列状态和积压情况可视化

- [ ] **资源使用监控**
  - [ ] CPU、内存、磁盘使用率监控
  - [ ] 数据库连接池和查询性能监控
  - [ ] 网络请求频率和响应时间监控
  - [ ] 并发处理能力和瓶颈分析

#### 3.2 事件通知系统 (Day 35-38)

- [ ] **多渠道通知框架**

  - [ ] NotificationManager 通知管理器实现
  - [ ] 微信、邮件、Webhook 等多渠道支持
  - [ ] 通知模板和格式化引擎
  - [ ] 通知发送状态跟踪和重试机制

- [ ] **智能通知规则引擎**

  - [ ] 基于条件的自动通知触发
  - [ ] 通知频率控制和去重机制
  - [ ] 通知优先级和升级策略
  - [ ] 自定义通知规则配置界面

- [ ] **告警管理系统**
  - [ ] AlertManager 告警管理器实现
  - [ ] 告警阈值配置和动态调整
  - [ ] 告警聚合和噪音过滤
  - [ ] 告警处理流程和闭环管理

#### 3.3 报告和可视化 (Day 39-42)

- [ ] **自动报告生成**

  - [ ] 日报/周报/月报模板设计
  - [ ] 报告数据自动收集和分析
  - [ ] 图表生成和数据可视化
  - [ ] 报告分发和订阅管理

- [ ] **监控面板开发**

  - [ ] Web 监控面板前端界面
  - [ ] 实时数据展示和刷新机制
  - [ ] 交互式图表和钻取功能
  - [ ] 移动端适配和响应式设计

- [ ] **历史数据分析**
  - [ ] 历史趋势分析和预测
  - [ ] 因子质量分布和演变分析
  - [ ] 账户性能历史对比
  - [ ] 异常模式识别和根因分析

#### 3.4 参数优化和执行分析 (Day 43-49)

- [ ] **执行记录收集系统**

  - [ ] AccountExecutionLogModel 数据收集
  - [ ] 执行过程的详细指标记录
  - [ ] 参数配置和结果关联分析
  - [ ] 执行日志的结构化存储和索引

- [ ] **参数优化算法引擎**

  - [ ] ParameterOptimizer 优化器实现
  - [ ] 基于历史数据的参数效果分析
  - [ ] 多目标优化算法（成功率+效率）
  - [ ] 参数组合的智能推荐算法

- [ ] **执行模式分析**

  - [ ] ExecutionAnalyzer 分析器实现
  - [ ] 成功模式识别和规律挖掘
  - [ ] 失败原因分析和改进建议
  - [ ] 最优执行时间窗口分析

- [ ] **A/B 测试框架**

  - [ ] 参数配置的 A/B 测试设计
  - [ ] 实验组和对照组的自动分配
  - [ ] 测试结果的统计显著性分析
  - [ ] A/B 测试报告和决策支持

- [ ] **性能对比和排名系统**
  - [ ] 账户综合性能评分算法
  - [ ] 多维度性能对比分析
  - [ ] 排名变化趋势和原因分析
  - [ ] 最佳实践提取和推广机制

### 第四阶段：统一入口和优化 (Week 7-8)

#### 4.1 统一程序入口 (Day 50-53)

- [ ] **重构 app.py 为 ML 增强的主调度器**

  - [ ] FactorDiggingScheduler 集成 ML 和传统两种模式
  - [ ] 添加 --ml-mode 命令行参数支持
  - [ ] 统一的启动和停止流程控制
  - [ ] 优雅关闭和资源清理机制
  - [ ] 配置文件加载和验证

  - [ ] **集成 ML 模式切换到主调度器**

  - [ ] 添加智能模式选择逻辑
  - [ ] 实现运行时 ML/传统模式动态切换
  - [ ] 添加 ML 性能监控和对比统计

  - [ ] **集成基础日志到主调度器**

  - [ ] 添加系统启动和运行状态日志
  - [ ] 实现 show_status() 方法显示队列状态
  - [ ] 添加 ML 模式状态显示

- [ ] **命令行界面开发**

  - [ ] 使用 argparse 实现丰富的命令行参数
  - [ ] 支持不同运行模式（auto, monitor, optimize 等）
  - [ ] 添加 ML 模式切换参数（--ml-mode, --ml-ratio）
  - [ ] 实时状态查询和控制命令
  - [ ] 命令行参数验证和帮助文档

- [ ] **添加日志控制参数**

  - [ ] --log-level (DEBUG/INFO/WARNING/ERROR)
  - [ ] --console-output / --no-console
  - [ ] --queue-status-interval, --metrics-interval
  - [ ] --mode=tail-logs, --mode=log-analysis, --mode=report
  - [ ] --ml-mode (traditional/hybrid/full_ml)
  - [ ] --ml-performance-compare 显示 ML vs 传统方法对比

- [ ] **交互式操作界面**

  - [ ] 简单的文字 UI 界面（rich 组件）
  - [ ] 实时状态显示和进度条
  - [ ] 交互式配置和控制操作
  - [ ] 快捷键和操作提示

- [ ] **集成实时日志查看功能**
  - [ ] 实时队列状态表格显示
  - [ ] 账户性能监控面板
  - [ ] 错误日志高亮显示
  - [ ] 日志过滤和搜索功能

#### 4.2 性能优化 (Day 54-56)

- [ ] **并发处理性能优化**

  - [ ] 异步 IO 和协程优化
  - [ ] 连接池和会话复用优化
  - [ ] 并发数动态调整算法
  - [ ] CPU 密集型任务的进程池优化

- [ ] **内存使用效率改进**

  - [ ] 大批量数据的流式处理
  - [ ] 内存缓存和 LRU 淘汰策略
  - [ ] 内存泄漏检测和修复
  - [ ] 垃圾回收优化和监控

- [ ] **数据库查询优化**
  - [ ] 查询语句性能分析和调优
  - [ ] 索引策略优化和维护
  - [ ] 连接池配置和查询缓存
  - [ ] 读写分离和数据分片策略

#### 4.3 测试和文档 (Day 57-63)

- [ ] **单元测试完善**

  - [ ] 核心组件的单元测试覆盖
  - [ ] Mock 和 Stub 的合理使用
  - [ ] 测试数据生成和管理
  - [ ] 测试自动化和 CI 集成

- [ ] **集成测试和压力测试**

  - [ ] 端到端流程测试
  - [ ] 多账户并发测试
  - [ ] 大批量数据处理测试
  - [ ] 系统稳定性和性能基准测试

- [ ] **文档和部署指南**
  - [ ] 完整的用户使用文档
  - [ ] 开发者技术文档
  - [ ] 部署和运维指南
  - [ ] 故障排查和问题解决手册

## 🔄 迁移策略和风险控制

### 渐进式迁移策略

#### 第一阶段：数据迁移和基础设施准备

##### 1.1 历史数据迁移

```python
class DataMigrator:
    """数据迁移器 - 将旧系统数据迁移到新数据库"""

    def __init__(self):
        self.db = get_database()
        self.file_parser = FileParser()

    async def migrate_historical_factors(self):
        """迁移历史因子数据"""
        # 扫描 xin 目录中的数据文件
        file_patterns = [
            'xin/data/step1_*.txt',
            'xin/data/step2_*.txt',
            'xin/data/check_*.txt',
            'xin/data/submit_*.txt'
        ]

        for pattern in file_patterns:
            files = glob.glob(pattern)
            for file_path in files:
                await self.process_file(file_path)

    async def process_file(self, file_path: str):
        """处理单个数据文件"""
        stage = self.extract_stage_from_filename(file_path)
        factors = self.file_parser.parse_factor_file(file_path)

        # 批量插入到新数据库
        with self.db.atomic():
            for factor_data in factors:
                AlphaFactorQueueModel.create(
                    factor_expression=factor_data['expression'],
                    stage=stage,
                    status=self.map_old_status(factor_data['status']),
                    tags=factor_data.get('tags'),
                    created_at=factor_data.get('created_at', datetime.now()),
                    performance_data=json.dumps(factor_data.get('performance', {}))
                )

    def validate_migration(self) -> Dict:
        """验证迁移结果"""
        validation_result = {
            'old_files_count': self.count_old_files(),
            'new_records_count': self.count_new_records(),
            'data_integrity_check': self.check_data_integrity(),
            'sample_verification': self.verify_sample_data()
        }
        return validation_result
```

##### 1.2 配置迁移

```python
class ConfigMigrator:
    """配置迁移器"""

    async def migrate_configurations(self):
        """迁移配置信息"""
        # 读取旧系统配置
        old_configs = self.read_old_configs()

        # 转换为新的配置格式
        new_configs = self.convert_config_format(old_configs)

        # 保存到数据库
        await self.save_configs_to_db(new_configs)

    def read_old_configs(self) -> Dict:
        """读取旧系统配置"""
        configs = {}
        config_files = [
            'xin/config.py',
            'xin/user_info.txt',
            'xin/fields.py'
        ]

        for file_path in config_files:
            if os.path.exists(file_path):
                configs.update(self.parse_config_file(file_path))

        return configs
```

#### 第二阶段：并行运行验证

##### 2.1 影子模式运行

```python
class ShadowModeRunner:
    """影子模式运行器 - 新旧系统并行运行对比"""

    def __init__(self):
        self.old_system = OldSystemInterface()
        self.new_system = NewSystemInterface()
        self.comparator = ResultComparator()

    async def run_parallel_processing(self, factors: List[str]):
        """并行处理对比"""
        # 同时在新旧系统中处理相同因子
        old_results = await self.old_system.process_factors(factors)
        new_results = await self.new_system.process_factors(factors)

        # 对比结果
        comparison = self.comparator.compare_results(old_results, new_results)

        # 记录差异
        await self.log_differences(comparison)

        return comparison

    async def validate_consistency(self) -> Dict:
        """验证一致性"""
        test_factors = self.generate_test_factors(100)
        comparison = await self.run_parallel_processing(test_factors)

        consistency_report = {
            'total_factors': len(test_factors),
            'identical_results': comparison['identical_count'],
            'acceptable_differences': comparison['acceptable_diff_count'],
            'significant_differences': comparison['significant_diff_count'],
            'consistency_rate': comparison['identical_count'] / len(test_factors)
        }

        return consistency_report
```

##### 2.2 灰度发布

```python
class GradualRollout:
    """灰度发布管理器"""

    def __init__(self):
        self.traffic_splitter = TrafficSplitter()
        self.monitor = SystemMonitor()

    async def start_rollout(self):
        """开始灰度发布"""
        rollout_phases = [
            {'name': 'phase1', 'new_system_ratio': 0.1, 'duration_hours': 24},
            {'name': 'phase2', 'new_system_ratio': 0.3, 'duration_hours': 48},
            {'name': 'phase3', 'new_system_ratio': 0.5, 'duration_hours': 72},
            {'name': 'phase4', 'new_system_ratio': 0.8, 'duration_hours': 48},
            {'name': 'phase5', 'new_system_ratio': 1.0, 'duration_hours': -1}
        ]

        for phase in rollout_phases:
            await self.execute_phase(phase)

            # 监控关键指标
            metrics = await self.monitor.get_phase_metrics()

            # 决定是否继续
            if not self.should_continue_rollout(metrics):
                await self.rollback_to_previous_phase()
                break

    def should_continue_rollout(self, metrics: Dict) -> bool:
        """判断是否继续灰度发布"""
        criteria = {
            'error_rate': metrics['error_rate'] < 0.05,
            'performance_regression': metrics['avg_response_time'] < metrics['baseline'] * 1.2,
            'success_rate': metrics['success_rate'] > 0.95
        }

        return all(criteria.values())
```

#### 第三阶段：完全切换

##### 3.1 最终切换

```python
class FinalCutover:
    """最终切换管理器"""

    async def perform_cutover(self):
        """执行最终切换"""
        # 1. 停止旧系统新任务接收
        await self.old_system.stop_accepting_new_tasks()

        # 2. 等待旧系统处理完毕
        await self.wait_for_old_system_completion()

        # 3. 迁移剩余数据
        await self.migrate_remaining_data()

        # 4. 启用新系统全流量
        await self.enable_full_traffic_to_new_system()

        # 5. 验证切换结果
        validation_result = await self.validate_cutover()

        if validation_result['success']:
            await self.complete_cutover()
        else:
            await self.emergency_rollback()
```

### 风险控制机制

#### 1. 数据备份策略

```python
class BackupManager:
    """备份管理器"""

    async def create_full_backup(self) -> str:
        """创建完整备份"""
        backup_id = f"backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"

        # 备份数据库
        db_backup_path = await self.backup_database(backup_id)

        # 备份配置文件
        config_backup_path = await self.backup_configurations(backup_id)

        # 备份关键代码文件
        code_backup_path = await self.backup_critical_files(backup_id)

        # 创建备份清单
        manifest = {
            'backup_id': backup_id,
            'timestamp': datetime.now().isoformat(),
            'database_backup': db_backup_path,
            'config_backup': config_backup_path,
            'code_backup': code_backup_path,
            'validation_hash': self.calculate_backup_hash()
        }

        await self.save_backup_manifest(backup_id, manifest)
        return backup_id

    async def restore_from_backup(self, backup_id: str):
        """从备份恢复"""
        manifest = await self.load_backup_manifest(backup_id)

        # 验证备份完整性
        if not await self.validate_backup_integrity(manifest):
            raise BackupCorruptionError(f"备份 {backup_id} 已损坏")

        # 恢复数据库
        await self.restore_database(manifest['database_backup'])

        # 恢复配置
        await self.restore_configurations(manifest['config_backup'])

        # 恢复代码文件
        await self.restore_critical_files(manifest['code_backup'])
```

#### 2. 快速回滚机制

```python
class RollbackManager:
    """回滚管理器"""

    def __init__(self):
        self.backup_manager = BackupManager()
        self.traffic_manager = TrafficManager()

    async def emergency_rollback(self, reason: str):
        """紧急回滚"""
        logger.critical(f"执行紧急回滚: {reason}")

        # 1. 立即切换流量回旧系统
        await self.traffic_manager.route_all_traffic_to_old_system()

        # 2. 停止新系统处理
        await self.stop_new_system_processing()

        # 3. 恢复最近的稳定备份
        latest_backup = await self.backup_manager.get_latest_stable_backup()
        await self.backup_manager.restore_from_backup(latest_backup)

        # 4. 验证回滚结果
        validation_result = await self.validate_rollback()

        # 5. 发送紧急通知
        await self.send_emergency_notification(reason, validation_result)

    async def gradual_rollback(self, target_version: str):
        """渐进式回滚"""
        rollback_phases = [
            {'new_system_ratio': 0.5, 'duration_minutes': 30},
            {'new_system_ratio': 0.2, 'duration_minutes': 30},
            {'new_system_ratio': 0.0, 'duration_minutes': -1}
        ]

        for phase in rollback_phases:
            await self.execute_rollback_phase(phase)

            # 监控系统稳定性
            if await self.is_system_stable():
                logger.info(f"回滚阶段 {phase} 执行成功")
            else:
                await self.emergency_rollback("渐进式回滚失败")
                break
```

#### 3. 健康检查和自动恢复

```python
class HealthChecker:
    """健康检查器"""

    def __init__(self):
        self.check_interval = 60  # 秒
        self.failure_threshold = 3
        self.recovery_manager = RecoveryManager()

    async def continuous_health_check(self):
        """持续健康检查"""
        consecutive_failures = 0

        while True:
            try:
                health_status = await self.perform_health_check()

                if health_status['overall_healthy']:
                    consecutive_failures = 0
                    await self.record_healthy_status(health_status)
                else:
                    consecutive_failures += 1
                    await self.record_unhealthy_status(health_status)

                    if consecutive_failures >= self.failure_threshold:
                        await self.trigger_auto_recovery(health_status)

            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                consecutive_failures += 1

            await asyncio.sleep(self.check_interval)

    async def perform_health_check(self) -> Dict:
        """执行健康检查"""
        checks = {
            'database_connectivity': await self.check_database(),
            'api_responsiveness': await self.check_api_endpoints(),
            'queue_processing': await self.check_queue_processing(),
            'account_availability': await self.check_account_status(),
            'system_resources': await self.check_system_resources()
        }

        overall_healthy = all(checks.values())

        return {
            'timestamp': datetime.now().isoformat(),
            'overall_healthy': overall_healthy,
            'detailed_checks': checks,
            'health_score': sum(checks.values()) / len(checks)
        }

    async def trigger_auto_recovery(self, health_status: Dict):
        """触发自动恢复"""
        failed_components = [
            component for component, status in health_status['detailed_checks'].items()
            if not status
        ]

        for component in failed_components:
            await self.recovery_manager.recover_component(component)
```

#### 4. 监控和告警升级

```python
class EscalationManager:
    """告警升级管理器"""

    def __init__(self):
        self.escalation_rules = {
            'critical': {'immediate': True, 'channels': ['phone', 'wechat', 'email']},
            'high': {'delay_minutes': 5, 'channels': ['wechat', 'email']},
            'medium': {'delay_minutes': 15, 'channels': ['email']},
            'low': {'delay_minutes': 60, 'channels': ['email']}
        }

    async def process_alert(self, alert: Dict):
        """处理告警"""
        severity = alert['severity']
        rule = self.escalation_rules[severity]

        if rule.get('immediate', False):
            await self.send_immediate_alert(alert, rule['channels'])
        else:
            await self.schedule_delayed_alert(alert, rule)

    async def send_immediate_alert(self, alert: Dict, channels: List[str]):
        """发送立即告警"""
        for channel in channels:
            await self.notification_manager.send_alert(
                alert['message'],
                channel=channel,
                priority='urgent'
            )
```

### 成功验收标准

#### 技术验收标准

1. **功能完整性**: 新系统实现旧系统所有功能，无功能缺失
2. **性能指标**: 处理效率提升 ≥ 30%，响应时间改善 ≥ 20%
3. **稳定性**: 系统可用性 ≥ 99.5%，MTTR ≤ 5 分钟
4. **数据一致性**: 新旧系统处理结果一致性 ≥ 99%

#### 业务验收标准

1. **操作简化**: 从多脚本操作简化为单程序操作
2. **监控可视**: 实时监控面板，关键指标可视化
3. **告警及时**: 异常告警响应时间 ≤ 1 分钟
4. **容错能力**: 异常自动恢复成功率 ≥ 90%

## 🔧 关键改进点

### 1. 统一的程序入口（支持 ML 增强）

```bash
# 传统模式（默认）
python app.py

# ML混合模式（推荐）
python app.py --ml-mode=hybrid

# 完全ML模式
python app.py --ml-mode=full_ml

# 静默模式（仅文件日志）
python app.py --quiet --ml-mode=hybrid

# 查看实时状态（含ML对比）
python app.py --status --ml-mode=hybrid

# 显示帮助
python app.py --help
```

### 2. 智能队列管理

- **状态驱动**：因子在数据库中按状态流转
- **标签分类**：不同数据集和策略的因子分别管理
- **优先级队列**：高质量因子优先处理
- **自动清理**：完成的因子定期归档

### 3. 容错和恢复

- **异常分类**：网络、API、逻辑错误分别处理
- **智能重试**：根据错误类型决定重试策略
- **状态恢复**：程序重启后自动恢复中断的任务
- **错误统计**：监控错误率，超阈值时告警

### 4. 统计和监控

- **实时统计**：每小时处理的因子数量和成功率
- **性能监控**：响应时间、吞吐量、资源使用
- **质量跟踪**：因子质量分布和趋势分析
- **告警机制**：异常情况及时通知

### 5. 事件通知

- **成功通知**：高质量因子提交成功时通知
- **批次报告**：每个处理批次完成后的统计
- **错误告警**：严重错误或错误率过高时告警
- **日报推送**：每日处理情况汇总

### 6. 多账户管理

- **账户池管理**：维护多个活跃账户的状态和可用性
- **智能调度**：根据账户性能和负载情况分配任务
- **故障转移**：账户异常时自动切换到备用账户
- **负载均衡**：合理分配任务避免单账户过载
- **账户轮换**：定期轮换账户减少被检测风险

### 7. 参数调优和执行优化

- **性能记录**：详细记录各账户在不同数据集上的表现
- **参数分析**：基于历史数据分析最优参数组合
- **自适应调整**：根据成功率自动调整执行策略
- **A/B 测试**：对比不同参数配置的效果
- **智能推荐**：为每个账户推荐最优的执行参数

## 📊 预期效果

### 运维效率提升

- **无人值守**：自动运行，无需手工干预
- **快速恢复**：异常自动处理，减少人工介入
- **状态透明**：随时了解处理进度和系统状态
- **实时监控**：控制台和文件双重日志，处理过程全程可见
- **问题定位**：详细的错误日志和上下文信息，故障排查时间减少 80%
- **进度跟踪**：实时进度条和批次统计，处理效率一目了然

### 资源优化配置

- **智能调度**：根据系统负载调整并发数
- **错误处理**：减少因异常导致的资源浪费
- **优先处理**：高质量因子优先获得资源

### 质量提升

- **完整流程**：确保所有因子经过完整的检测流程
- **质量跟踪**：持续监控因子质量趋势
- **及时反馈**：重要结果及时通知相关人员

### 可维护性增强

- **模块化**：清晰的模块分离，便于维护
- **可扩展**：易于添加新的处理阶段和功能
- **可测试**：完整的测试覆盖，确保代码质量

## 🎯 成功指标

1. **统一性**：一个程序处理所有任务，无需手工切换脚本
2. **容错性**：异常情况下自动恢复，减少人工干预 > 90%
3. **可观测性**：实时了解系统状态，错误定位时间 < 5 分钟
4. **效率提升**：因子处理效率提升 > 30%
5. **质量保证**：零遗漏，所有因子完成完整流程
6. **多账户协同**：支持 3+ 账户并行运行，总体吞吐量提升 > 50%
7. **智能优化**：基于历史数据的参数调优，账户成功率提升 > 20%
8. **负载均衡**：账户间任务分配均匀度 > 85%，无单点过载
9. **自适应性**：系统能根据账户状态自动调整策略，人工干预 < 5%
10. **日志完整性**：处理过程 100%记录，支持控制台和文件双重输出
11. **故障排查效率**：通过详细日志，问题定位时间 < 5 分钟
12. **实时可观测性**：系统状态、队列情况、账户性能实时可见

## 📝 后续扩展

1. **分布式处理**：支持多机器并行处理
2. **更多数据源**：集成更多数据集和字段
3. **机器学习**：智能因子生成和质量预测
4. **可视化界面**：Web 界面监控和管理
5. **API 接口**：提供 REST API 供外部调用

## 📋 总结

本设计文档提供了 WQ 项目从现有分散式脚本架构向统一、自动化、智能化系统重构的完整方案。通过深入分析现有的 6 个核心脚本（digging_1step.py、digging_2step.py、digging_3step.py、check.py、s7.py、submit_alpha.py），我们设计了一个基于数据库队列、多账户支持、智能监控的新一代因子挖掘系统。

### 核心改进成果

#### 1. 架构层面革新

- **统一入口程序**：从 6 个独立脚本 → 单一 `app.py` 统一调度
- **数据驱动架构**：从文件存储 → SQLite 数据库 + 完整 ORM 模型
- **状态机管理**：因子生命周期从 pending → processing → completed → submitted
- **微服务化设计**：模块化 Worker 系统，易于扩展和维护

#### 2. 容错性大幅提升

- **多层重试机制**：指数退避 + 熔断器 + 智能重试策略
- **会话管理增强**：自动刷新 + 健康检查 + 故障恢复
- **错误分类处理**：网络/API/逻辑错误的针对性处理
- **断点续传优化**：数据库状态保证中断后无缝恢复

#### 3. 智能化处理能力

- **机器学习参数优化**：基于历史数据的 RandomForest 模型优化
- **启发式规则引擎**：成功案例分析 + 模式识别
- **智能账户调度**：健康评分 + 负载均衡 + 自动轮换
- **30 分钟智能提交**：超时控制 + 状态监控 + 自动重连

#### 4. 多账户协同系统

- **负载均衡算法**：轮询 + 最少连接 + 加权轮询
- **健康检查机制**：4 维度评分（登录/错误率/响应时间/限流）
- **故障转移策略**：实时监控 + 自动切换 + 任务重分配
- **账户轮换机制**：定时轮换 + 性能驱动休息策略

#### 5. 全方位监控体系

- **实时监控面板**：5 阶段处理状态 + 账户性能 + 系统指标
- **性能趋势分析**：同比分析 + 趋势预测 + 异常检测
- **事件驱动通知**：8 种事件类型 + 4 种通知渠道 + 智能过滤
- **数据质量评估**：一致性检查 + 异常检测 + 自动修复

### 技术架构亮点

#### 数据模型设计

```python
# 5个核心模型支撑完整业务流程
- AlphaFactorQueueModel    # 因子队列和状态管理
- AlphaProcessingLogModel  # 详细处理日志记录
- AlphaBatchModel         # 批次管理和统计
- SystemStatsModel        # 系统性能监控
- UserAccountModel        # 多账户管理
```

#### 核心技术组件

```python
# 8个关键组件构建完整生态系统
- RobustAPIClient         # 容错API客户端 + 缓存 + 重试
- MultiAccountScheduler   # 多账户智能调度系统
- ParameterOptimizer      # ML驱动的参数优化引擎
- EventNotificationSystem # 事件驱动通知架构
- FactorDiggingMonitor   # 全面监控统计系统
- SessionManager         # 增强会话管理器
- DataMigrator           # 无缝数据迁移工具
- QueueProcessor         # 智能队列处理器
```

#### 算法和策略创新

- **加权轮询负载均衡**：基于账户容量和当前负载的智能分配
- **指数移动平均性能追踪**：实时更新账户成功率和响应时间
- **随机森林参数优化**：多特征参数组合的智能推荐
- **分层重试策略**：根据错误类型采用不同的重试间隔和次数

### 解决的核心痛点

#### 🏗️ 从架构层面根治问题

| 旧问题           | 新方案                 | 改进效果            |
| ---------------- | ---------------------- | ------------------- |
| 6 个脚本手动协调 | 统一调度器自动流程管理 | 人工干预减少 95%    |
| 文件存储状态混乱 | 数据库事务保证一致性   | 数据丢失风险降至 0  |
| 单账户性能瓶颈   | 多账户负载均衡         | 处理能力提升 3-5 倍 |
| 错误处理简陋     | 智能重试+故障转移      | 成功率提升 20-30%   |

#### 🛡️ 从容错性大幅增强系统稳定性

- **故障恢复时间**：从手工分析数小时 → 自动恢复数分钟
- **会话管理强度**：3 小时自动刷新 + 实时健康检查
- **数据一致性**：事务保证 + 完整性约束 + 自动修复
- **系统可用性**：目标 99.5%可用性，MTTR < 5 分钟

#### 📊 从监控统计实现精细化运营

- **实时可观测性**：59 个关键指标实时监控
- **趋势分析能力**：4 个维度趋势对比分析
- **智能告警体系**：事件驱动 + 自定义阈值 + 多渠道通知
- **数据驱动决策**：基于历史数据的参数优化和策略调整

#### 🚀 从性能优化实现质的飞跃

- **处理效率提升**：预期 30%+ 处理速度提升
- **资源利用率**：多账户并行 + 智能调度，利用率提升 50%+
- **参数优化效果**：机器学习驱动，成功率提升 20%+
- **系统响应速度**：缓存优化 + 异步处理，响应时间改善 20%+

### 增强处理日志系统特色功能

#### 🎯 核心日志功能

**多层次日志记录**：

- **ProcessingLogger**: 主要日志记录器，支持控制台美化输出和文件记录
- **ProgressTracker**: 实时进度跟踪，展示各阶段处理进度
- **DetailedProcessLogger**: 详细过程记录器，记录每个因子的完整处理过程

**智能日志输出**：

- **阶段开始/结束**: 记录每个处理阶段的开始时间、因子数量、预计完成时间
- **因子级别跟踪**: 每个因子的表达式、API 调用参数、响应时间、性能指标
- **批次统计**: 成功率、失败原因分类、平均处理时间、错误汇总
- **系统指标**: CPU 使用率、内存占用、队列深度、处理速度
- **账户性能**: 各账户成功率、响应时间、当前负载、健康评分

#### 🖥️ 控制台输出示例

```
┌─── 🚀 系统启动 ──────────────────────────────────────────┐
│ WQ因子挖掘系统启动 - 时间: 2024-01-15 09:30:00          │
│                                                          │
│ 活跃Worker: step1, step2, step3, check, submit         │
│ 日志级别: INFO                                          │
│ 控制台输出: 启用                                        │
│ 状态更新间隔: 300秒                                     │
└──────────────────────────────────────────────────────────┘

🚀 开始 stage1 阶段处理，共 150 个因子

  └─ 📊 处理因子 #1001: 开始处理 - 账户: user1
     表达式: rank(close/delay(close,1))...
  └─ 📊 处理因子 #1001: 调用API /stage1/simulation
     参数: {'universe': 'TOP3000', 'region': 'CN', 'delay': 1, 'decay': 9}
  └─ ✅ 成功 因子 #1001 (2.34s) Sharpe=1.85 Fitness=0.45 ID=alpha_12345
  └─ 📊 处理因子 #1001: 符合Stage2晋级条件
     Sharpe=1.85, Turnover=0.25
  └─ 📊 处理因子 #1001: 已成功晋级到Stage2

┌─── 批次处理摘要 - Step1Worker ──────────────────────────┐
│ 指标          │ 数值                                    │
│───────────────┼─────────────────────────────────────────│
│ 总因子数      │ 150                                     │
│ 成功数量      │ 142                                     │
│ 失败数量      │ 8                                       │
│ 成功率        │ 94.67%                                  │
│ 总耗时        │ 380.45s                                 │
│ 平均耗时      │ 2.54s/factor                           │
└─────────────────────────────────────────────────────────┘

📋 队列状态
┌─────────┬──────────┬──────────┬──────────┬────────┐
│ 阶段    │ 待处理   │ 处理中   │ 已完成   │ 失败   │
├─────────┼──────────┼──────────┼──────────┼────────┤
│ STAGE1  │ 1250     │ 15       │ 342      │ 8      │
│ STAGE2  │ 85       │ 8        │ 156      │ 3      │
│ STAGE3  │ 12       │ 2        │ 45       │ 1      │
│ CHECK   │ 23       │ 3        │ 89       │ 2      │
│ SUBMIT  │ 5        │ 1        │ 67       │ 0      │
└─────────┴──────────┴──────────┴──────────┴────────┘
```

#### 📁 文件日志示例

```
2024-01-15 09:30:15,123 - factor_processing_Step1Worker - INFO - [step1_worker.py:145] - Stage stage1 started with 150 factors
2024-01-15 09:30:15,200 - factor_processing_Step1Worker - INFO - [step1_worker.py:67] - Processing factor 1001: 开始处理 - rank(close/delay(close,1))
2024-01-15 09:30:17,543 - factor_processing_Step1Worker - INFO - [step1_worker.py:89] - Factor 1001 成功 in 2.34s - Result: {'sharpe': 1.85, 'fitness': 0.45, 'alpha_id': 'alpha_12345'}
2024-01-15 09:30:18,123 - factor_processing_Step1Worker - INFO - [step1_worker.py:156] - Batch completed - 142/150 factors succeeded (94.67%) in 380.45s

2024-01-15 09:32:15,456 - factor_processing_MainScheduler - INFO - [scheduler.py:234] - Queue status: {'stage1': {'pending': 1250, 'processing': 15, 'completed': 342, 'failed': 8}}
```

#### 🛠️ 命令行控制功能

```bash
# 详细日志模式 - 查看所有处理细节
python app.py --mode=auto --log-level=DEBUG --console-output

# 静默模式 - 仅文件日志，最小化控制台输出
python app.py --mode=auto --no-console --log-level=WARNING

# 实时监控模式 - 专门的监控界面
python app.py --mode=monitor --refresh-interval=10

# 实时日志查看 - 类似 tail -f
python app.py --mode=tail-logs --worker=Step1Worker --lines=50

# 日志分析和报告生成
python app.py --mode=log-analysis --date=2024-01-15 --export-csv
python app.py --mode=report --start-date=2024-01-01 --end-date=2024-01-31 --format=html
```

#### 📊 日志统计和分析

**错误分析**：

- 自动分类错误类型（网络、API、逻辑错误）
- 记录错误上下文（账户、阶段、参数）
- 生成错误趋势报告和改进建议

**性能分析**：

- 各阶段处理效率对比
- 账户性能排名和优化建议
- 系统瓶颈识别和资源优化建议

**质量跟踪**：

- 因子质量指标统计（Sharpe、Fitness 分布）
- 晋级率分析（各阶段通过率）
- 最终提交成功率趋势

### 数据迁移和部署策略

#### 渐进式迁移方案

1. **阶段一**：数据模型创建 + 历史数据迁移
2. **阶段二**：影子模式并行运行验证
3. **阶段三**：灰度发布逐步切流量
4. **阶段四**：全量切换 + 旧系统下线

#### 风险控制机制

- **完整备份策略**：数据库 + 配置 + 代码的多层备份
- **快速回滚能力**：紧急回滚 < 5 分钟，渐进回滚 < 30 分钟
- **健康检查体系**：持续监控 + 自动故障检测
- **告警升级机制**：分级告警 + 自动升级 + 多渠道通知

## 🚀 立即行动计划

### Week 1: 环境准备和数据模型

```bash
# 1. 创建数据库迁移脚本
cd src && python3 -m venv .venv && source .venv/bin/activate
pip3 install -r requirements.txt

# 2. 初始化数据库模型
python3 -c "from lib.models import create_tables; create_tables()"

# 3. 执行数据迁移
python3 migrations/migrate_from_xin.py --validate
```

### Week 2-3: 核心功能开发

```bash
# 4. 开发核心组件
touch src/lib/alpha_queue.py
touch src/lib/multi_account_scheduler.py
touch src/lib/robust_api_client.py

# 5. 实现 Worker 系统
mkdir src/workers
touch src/workers/base_worker.py
touch src/workers/step1_worker.py
touch src/workers/step2_worker.py
touch src/workers/step3_worker.py
```

### Week 4: 测试和优化

```bash
# 6. 创建测试套件
mkdir test/integration
python3 -m pytest test/ -v --cov

# 7. 性能调优
python3 scripts/benchmark_queue_performance.py
```

这份设计文档为 WQ 项目的现代化重构提供了详尽的技术路线图，确保在提升效率的同时保持系统稳定性和可维护性。

## 🎯 ML 融入式设计的核心优势

### ML 完全融入现有流程，非独立执行

上述 ML 增强设计的核心特点是**无缝融入**现有的一二三阶段处理流程，而不是作为独立系统运行：

#### ✅ 融入式设计优势

1. **代码复用**：保持原有`digging_1step.py`、`digging_2step.py`、`digging_3step.py`的核心逻辑
2. **接口兼容**：ML 功能作为增强层，完全兼容现有 API 和数据结构
3. **无缝切换**：可在运行时动态选择 ML 模式或传统模式
4. **渐进升级**：从传统 → 混合 → 完全 ML 的平滑过渡
5. **风险可控**：任何时候都可以回退到传统方法

#### ✅ 具体融入方式

```python
# 第一阶段：在原有逻辑基础上增强
class MLEnhancedStep1Worker(BaseWorker):  # 继承原有BaseWorker

    async def process_batch(self, factors, accounts):
        if self.ml_enabled:
            # ML预筛选 → 减少70%无效测试
            qualified_factors = await self._ml_quality_prefilter(factors)
            # ML参数优化 → 替代网格搜索
            optimized_factors = await self._ml_parameter_optimization(qualified_factors)
        else:
            # 完全使用原有逻辑
            optimized_factors = factors

        # 执行原有的factor_processing逻辑（保持不变）
        for factor in optimized_factors:
            result = await self.execute_factor_processing(factor, account)
            # 原有的晋级逻辑保持不变
            if self.meets_stage2_criteria(result):
                await self.promote_to_stage2(factor, result)
```

#### ✅ 非独立执行的设计

ML 功能**不是**独立的系统，而是**融入**到现有流程中：

```python
# ❌ 错误理解：ML作为独立系统
ml_system.run()  # 独立运行
traditional_system.run()  # 独立运行

# ✅ 正确设计：ML融入现有流程
worker = MLEnhancedStep1Worker()  # 增强版Worker
worker.process_batch()  # 在现有流程中使用ML增强功能
# 内部会调用原有的execute_factor_processing等方法
```

#### ✅ 实际运行体验

```bash
# 用户只需运行一个命令，ML自动融入三阶段处理
python3 src/schedulers/ml_enhanced_scheduler.py --mode=hybrid

# 系统内部智能决策：
# Stage1: 20%因子用ML生成 + 70%用ML预筛选 + 传统processing
# Stage2: ML智能组合 + 原有decay逻辑 + 传统testing
# Stage3: ML智能trade_when + 原有晋级逻辑
# Check/Submit: 保持完全原有逻辑
```

### 📊 融入效果对比

| 对比维度       | 独立 ML 系统       | 融入式 ML 设计   |
| -------------- | ------------------ | ---------------- |
| **系统复杂度** | 需维护两套系统     | 单一增强系统     |
| **兼容性风险** | 高（接口不一致）   | 零（完全兼容）   |
| **部署复杂度** | 复杂（两套部署）   | 简单（一键部署） |
| **回退能力**   | 困难               | 即时切换         |
| **维护成本**   | 高（双重维护）     | 低（统一维护）   |
| **用户体验**   | 复杂（需选择系统） | 透明（自动选择） |

这种融入式设计确保了 ML 技术的引入不会打破现有系统的稳定性，同时让用户享受到 AI 带来的效率提升。

#### ✅ 设计问题修复总结

通过以上修复，解决了原设计中的关键问题：

1. **消除设计冗余**：统一使用`FactorDiggingScheduler`，删除重复的`MLEnhancedFactorDiggingScheduler`
2. **app.py 统一入口**：通过`--ml-mode`参数实现 ML 和传统模式的统一管理
3. **完整功能实现**：用户可以通过单一入口程序体验所有功能
4. **实施计划完善**：更新实施计划以反映统一的设计架构

#### ⚠️ 关键发现：ML 组件实现缺失

**严重问题**：ML Worker 中引用的组件类需要实际实现，否则系统无法启动

```python
# 当前缺失的ML组件文件：
src/lib/ml/                           # ❌ 目录不存在
├── factor_intelligence.py           # ❌ FactorIntelligenceEngine
├── genetic_algorithm.py             # ❌ GeneticFactorEvolution
├── quality_predictor.py             # ❌ FactorQualityPredictor
├── factor_combiner.py               # ❌ IntelligentFactorCombiner
├── parameter_optimizer.py           # ❌ MultiObjectiveOptimizer
├── performance_predictor.py         # ❌ PerformancePredictor
├── trade_when_generator.py          # ❌ IntelligentTradeWhenGenerator
├── market_regime_detector.py        # ❌ MarketRegimeDetector
└── factor_timing_optimizer.py       # ❌ FactorTimingOptimizer
```

**影响**：系统无法启动，ML 功能完全不可用

**必需的补充工作**：

1. **P0（阻塞）**：创建基础 ML 组件接口，确保系统可启动
2. **P1（核心）**：实现质量预测和参数优化的基础功能
3. **P2（完整）**：实现完整的 ML 算法逻辑

**快速启动方案**：

```python
# src/lib/ml/__init__.py - 最小可用实现
class FactorIntelligenceEngine:
    def __init__(self): pass
    async def bayesian_optimize_parameters(self, expr, **kwargs):
        return {'delay': 1, 'decay': 9, 'universe': 'TOP3000'}

class GeneticFactorEvolution:
    def __init__(self): pass
    async def evolve_factors(self, **kwargs):
        return []  # 暂时返回空，不影响传统流程

class FactorQualityPredictor:
    def __init__(self): pass
    async def predict_batch(self, expressions):
        return [0.8] * len(expressions)  # 默认高分，不过滤因子
```

```

```
