# WQ 因子挖掘系统 - 完整架构概览

## 🎯 系统概述

WQ 因子挖掘系统是一个现代化的、全功能的量化因子发现和管理平台。系统采用模块化架构设计，集成了传统因子挖掘算法和先进的机器学习技术，提供从因子生成、质量检测到智能提交的完整工作流。

## 🏗️ 系统架构

### 核心组件架构图

```
┌─────────────────────────────────────────────────────────────┐
│                    WQ因子挖掘系统                              │
├─────────────────────────────────────────────────────────────┤
│  🎛️ 数据库配置管理 (DbConfigReader)                            │
├─────────────────┬─────────────────┬─────────────────────────┤
│  📋 队列管理      │  👥 账户调度      │  📨 通知系统             │
│  (AlphaQueue)   │  (AccountSched) │  (NotificationSys)    │
├─────────────────┴─────────────────┴─────────────────────────┤
│           ⚙️ BaseWorker框架 - 统一任务处理                      │
├─────────────────┬─────────────────┬─────────────────────────┤
│  🧬 因子生成      │  🔍 质量检测      │  📡 智能提交             │
│  - FactorGen    │  - QualityCheck │  - SmartSubmitter     │
│  - FactorComb   │  - 4维度评分     │  - 批量提交策略          │
│  - TradeWhen    │  - 风险评估      │  - 超时管理             │
├─────────────────┴─────────────────┴─────────────────────────┤
│            🤖 ML增强引擎 - 智能决策支持                         │
│  - 贝叶斯优化  - 性能预测  - 风险评估  - 特征工程            │
├─────────────────────────────────────────────────────────────┤
│  💾 数据层: SQLite + Peewee ORM + 缓存                        │
└─────────────────────────────────────────────────────────────┘
```

## 📦 已实现的核心组件

### 1. 🎛️ 数据库配置管理系统 (`src/lib/db_config_reader.py`)

**功能特性：**

- 统一的数据库配置管理
- URL 配置和地区配置
- 用户账户信息管理
- 数据集配置管理
- 缓存机制

**主要 API：**

```python
config_reader = get_config_reader()
api_base = config_reader.get_url('api_base')
auth_url = config_reader.get_url('auth')
regions = config_reader.get_all_regions()
account = config_reader.get_user_account('<EMAIL>')
```

### 2. ⚙️ BaseWorker 框架 (`src/lib/base_worker.py`)

**功能特性：**

- 统一的任务处理框架
- 异步并发处理
- 状态机管理
- 重试和超时机制
- 事件驱动的监控

**主要组件：**

- `BaseWorker`: 抽象工作器基类
- `Task`: 任务定义和状态管理
- `WorkerStats`: 性能统计和健康监控

### 3. 👥 多账户调度器 (`src/lib/account_scheduler.py`)

**功能特性：**

- 4 维度健康评分（登录/错误率/响应时间/限流）
- 多种负载均衡策略
- 自动故障恢复
- 账户轮换机制
- 实时健康监控

**评分维度：**

1. **登录成功率** - 账户认证稳定性
2. **错误率评分** - API 调用成功率
3. **响应时间评分** - 性能指标
4. **限流状态评分** - 频控状态

### 4. 🧬 因子生成组件

#### FactorGenerator (`src/lib/factor_generator.py`)

- 一阶因子生成：基础操作符组合
- 二阶因子生成：复合操作构造
- 表达式验证和复杂度评估
- 智能参数建议

#### FactorCombiner (`src/lib/factor_combiner.py`)

- 因子质量筛选
- 二阶组合生成
- 参数优化
- 批次管理

#### TradeWhenGenerator (`src/lib/trade_when_generator.py`)

- 交易条件因子生成
- 多地区事件库
- 智能变体生成
- 风险评估

### 5. 🔍 质量检测器 (`src/lib/quality_checker.py`)

**检测维度：**

- 自相关性检测
- 生产相关性检测
- 性能指标验证
- 合规性检查
- 风险评估

**并发检测：**

- 多线程并行处理
- 批量检测优化
- 详细报告生成

### 6. 📡 智能提交器 (`src/lib/smart_submitter.py`)

**功能特性：**

- 批量提交策略
- 超时管理（30 分钟）
- 重试机制
- 状态监控
- 进度回调

**提交策略：**

- `quality_first`: 质量优先
- `balanced`: 平衡策略
- `fast`: 快速策略

### 7. 📨 事件驱动通知系统 (`src/lib/notification_system.py`)

**通知渠道：**

- 📧 邮件通知
- 🔗 Webhook 推送
- 📄 文件日志
- 🖥️ 控制台输出

**特性：**

- 事件过滤和分级
- 批量通知聚合
- 消息模板引擎
- 多渠道并发发送

### 8. 🤖 ML 增强组件 (`src/lib/ml/enhanced_factor_intelligence.py`)

**核心算法：**

- **贝叶斯优化** - 参数自动调优
- **随机森林** - 性能预测模型
- **特征工程** - 22 维因子特征提取
- **风险建模** - 多维度风险评估

**智能功能：**

- 因子性能预测（0-1 评分）
- 风险评估和建议
- 参数优化建议
- 相似因子发现

## 🚀 快速开始

### 1. 环境准备

```bash
# 创建虚拟环境
python3 -m venv .venv
source .venv/bin/activate  # macOS/Linux
# .venv\Scripts\activate   # Windows

# 安装依赖
pip3 install -r requirements.txt

# 可选：安装ML增强依赖
pip3 install scikit-learn optuna
```

### 2. 数据库配置设置

系统使用 SQLite 数据库存储所有配置信息，默认数据库位置：`data/wq.db`

初始化数据库配置：

```bash
# 设置基础配置（地区、URL、数据集）
cd src
python3 process/config_setup.py

# 设置用户配置（账户信息）
python3 process/user_setup.py

# 或者使用一键设置
python3 setup.py --quick
```

配置内容包括：

- **API URLs**: api_base, auth, alpha_user 等
- **地区配置**: usa, chn 等市场信息
- **用户账户**: 认证邮箱和密码
- **数据集**: 可用数据集列表

### 3. 运行集成测试

```bash
cd src
python3 integration_test.py
```

### 4. 基础使用示例

```python
import asyncio
from lib.factor_generator import FactorGenerator
from lib.quality_checker import QualityChecker
from lib.smart_submitter import SmartSubmitter
from lib.db_config_reader import get_config_reader

async def main():
    # 检查配置
    config_reader = get_config_reader()
    api_base = config_reader.get_url('api_base')
    print(f"API基础URL: {api_base}")

    # 生成因子
    generator = FactorGenerator()
    factors = generator.first_order_factory(
        fields=["close", "volume"],
        ops=["rank", "ts_rank"]
    )

    # 质量检测
    checker = QualityChecker()
    results = await checker.run_comprehensive_check(factors[:5])

    # 智能提交
    submitter = SmartSubmitter()
    alpha_data = [{"id": f"alpha_{i}", "self_corr": 0.5} for i in range(3)]
    submit_results = await submitter.submit_alpha_batch(alpha_data)

    print(f"生成因子: {len(factors)}")
    print(f"检测通过: {results['summary']['passed_count']}")
    print(f"提交成功: {submit_results['success_count']}")

asyncio.run(main())
```

## 📊 性能特性

### 并发处理能力

- **因子生成**: 支持 1000+因子/批次
- **质量检测**: 5 线程并发检测
- **账户调度**: 10 账户负载均衡
- **智能提交**: 3 并发提交策略

### 高可用性设计

- **熔断器模式**: API 故障自动恢复
- **重试机制**: 指数退避重试
- **健康检查**: 实时状态监控
- **故障转移**: 账户自动切换

### 监控和可观测性

- **实时统计**: 详细性能指标
- **事件通知**: 多渠道状态推送
- **日志记录**: 结构化日志输出
- **健康检查**: 4 维度综合评分

## 🔧 扩展性

### 自定义 Worker

```python
from lib.base_worker import BaseWorker, Task

class CustomFactorWorker(BaseWorker):
    async def process_task(self, task: Task):
        # 自定义处理逻辑
        expression = task.input_data['expression']
        result = await self.custom_processing(expression)
        return result

    def get_supported_task_types(self):
        return ['custom_factor_generation']
```

### 自定义通知渠道

```python
from lib.notification_system import NotificationSystem

notification_system = NotificationSystem()

# 添加自定义过滤器
notification_system.add_channel_filter(
    'email_default',
    lambda event: event.level.value in ['error', 'critical']
)

# 测试通知渠道
result = await notification_system.test_channel('webhook_0')
```

### 数据库配置管理

```python
from lib.db_config_reader import get_config_reader
from lib.db import create_database_manager

# 获取配置
config_reader = get_config_reader()
api_urls = config_reader.get_all_url_configs()

# 管理数据库
db_manager = create_database_manager()
db_manager.insert_url_config('new_api', '/new/endpoint', '新API端点')
```

### ML 模型训练

```python
from lib.ml.enhanced_factor_intelligence import EnhancedFactorIntelligence

# 准备训练数据
training_data = [
    {
        'expression': 'rank(close)',
        'performance': 0.75,
        'risk_score': 0.3
    },
    # ... 更多训练样本
]

# 训练模型
ml_engine = EnhancedFactorIntelligence()
training_result = await ml_engine.train_comprehensive_model(training_data)

if training_result['success']:
    # 使用训练好的模型进行预测
    analysis = await ml_engine.analyze_factor_comprehensive('rank(ts_mean(close, 20))')
    print(f"预测性能: {analysis.predicted_performance:.3f}")
    print(f"风险评分: {analysis.risk_score:.3f}")
```

## 📈 系统优势

### 1. **现代化架构**

- 异步编程提升并发性能
- 模块化设计便于维护扩展
- 事件驱动架构支持松耦合

### 2. **数据库驱动配置**

- 集中式配置管理
- 运行时配置更新
- 多环境配置支持

### 3. **工程化实践**

- 完整的错误处理和恢复机制
- 详细的监控和日志记录
- 灵活的数据库配置系统

### 4. **高可靠性**

- 多账户负载均衡
- 故障自动恢复
- 实时健康检查

## 🔮 未来规划

### 短期目标

- [ ] 集成更多因子类型（基本面、另类数据）
- [ ] 优化 ML 模型性能
- [ ] 添加更多通知渠道
- [ ] 增强可视化界面

### 长期规划

- [ ] 分布式部署支持
- [ ] 实时流处理能力
- [ ] 深度学习模型集成
- [ ] 云原生架构迁移

## 📞 技术支持

系统采用模块化设计，每个组件都有详细的日志记录和错误处理。如遇问题：

1. 查看 `logs/` 目录下的日志文件
2. 运行集成测试检查系统状态
3. 检查数据库配置的正确性
4. 查看各组件的健康检查结果

**数据库位置**: `data/wq.db`  
**日志位置**: `logs/`  
**配置管理**: 使用 `src/process/config_setup.py` 和 `src/process/user_setup.py`

---

**🎉 系统已完成核心架构建设，采用数据库驱动的配置管理，具备了处理大规模因子挖掘的基础能力！**
