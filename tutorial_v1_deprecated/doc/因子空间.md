graph TD
    %% 主数据流程
    A["🔍 数据字段获取<br/>get_datafields()"] --> B["⚙️ 字段处理<br/>process_datafields()"]
    B --> C["📊 获取处理后字段<br/>pc_fields = matrix + vector"]
    C --> D["🏭 一阶因子生成<br/>first_order_factory()"]
    D --> E["📈 因子空间<br/>first_order"]
    
    %% 操作符模块
    F["🔧 基础操作符<br/>basic_ops"]
    G["⏰ 时序操作符<br/>ts_ops"]
    H["🚀 高级操作符<br/>arsenal"]
    I["👥 分组操作符<br/>group_ops"]
    
    %% 操作符连接到因子生成
    F --> D
    G --> D
    H --> D
    I --> D
    
    %% 操作符详细功能
    subgraph DETAILS["🛠️ 操作符功能详情"]
        
        subgraph BASIC_OPS["基础操作符详情"]
            F1["数学变换:<br/>log, sqrt, reverse, inverse<br/>rank, zscore, log_diff<br/><br/>数据处理:<br/>s_log_1p, fraction<br/>quantile, normalize, scale_down"]
        end
        
        subgraph TIME_OPS["时序操作符详情"]
            G1["统计函数:<br/>ts_rank, ts_zscore, ts_delta<br/>ts_sum, ts_product, ts_ir<br/>ts_std_dev, ts_mean<br/><br/>序列分析:<br/>ts_arg_min, ts_arg_max<br/>ts_min_diff, ts_max_diff<br/>ts_returns, ts_scale<br/>ts_skewness, ts_kurtosis<br/>ts_quantile"]
        end
        
        subgraph ADV_OPS["高级操作符详情"]
            H1["复杂函数:<br/>ts_moment, ts_entropy<br/>ts_min_max_cps, ts_min_max_diff<br/>inst_tvr, sigmoid<br/><br/>特殊操作:<br/>ts_decay_exp_window<br/>ts_percentage, vector_neut<br/>vector_proj, signed_power"]
        end
        
        subgraph GROUP_OPS["分组操作符详情"]
            I1["分组统计:<br/>group_neutralize, group_rank<br/>group_normalize, group_scale<br/>group_zscore"]
        end
        
    end
    
    %% 操作符到详情的连接
    F -.-> F1
    G -.-> G1
    H -.-> H1
    I -.-> I1
    
    %% 样式定义
    classDef mainFlow fill:#e3f2fd,stroke:#1976d2,stroke-width:3px,color:#000
    classDef operators fill:#f1f8e9,stroke:#388e3c,stroke-width:2px,color:#000
    classDef details fill:#fff8e1,stroke:#f57c00,stroke-width:1px,color:#333
    classDef endpoint fill:#fce4ec,stroke:#c2185b,stroke-width:2px,color:#000
    
    %% 应用样式
    class A,B,C,D mainFlow
    class E endpoint
    class F,G,H,I operators
    class F1,G1,H1,I1 details