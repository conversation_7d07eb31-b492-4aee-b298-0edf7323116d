# 统一日志配置系统

基于 Rich 组件的统一日志配置系统，为整个项目提供美观、一致的日志输出。

## 功能特性

- ✨ 基于 Rich 组件的美观日志输出
- 🎨 支持彩色主题和标记语言
- 📁 自动模块名检测
- 📝 支持文件日志输出
- ⚙️ 可配置的日志行为
- 🔄 运行时配置重载
- 🐛 增强的异常追踪

## 快速开始

### 基本使用

```python
from lib import get_logger

# 获取当前模块的日志记录器
logger = get_logger()

# 记录不同级别的日志
logger.info("信息日志")
logger.warning("警告日志")
logger.error("错误日志")
logger.debug("调试日志")
```

### 使用 Rich 标记语言

```python
logger.info("支持 [bold red]粗体红色[/bold red] 文字")
logger.info("支持 [green]绿色[/green] 和 [blue]蓝色[/blue] 文字")
logger.warning("[yellow]这是一条黄色警告[/yellow]")
```

### 异常日志记录

```python
try:
    result = 1 / 0
except Exception as e:
    logger.error("发生了异常", exc_info=True)
```

## 配置文件

日志配置位于 `src/config/logging.json`：

```json
{
    "logging": {
        "level": "INFO",
        "console": {
            "enabled": true,
            "show_time": true,
            "show_level": true,
            "show_path": true,
            "rich_tracebacks": true
        },
        "file": {
            "enabled": false,
            "path": "logs/app.log",
            "level": "INFO"
        },
        "theme": {
            "info": "cyan",
            "warning": "yellow",
            "error": "red bold",
            "critical": "red bold reverse",
            "debug": "dim"
        }
    }
}
```

## API 参考

### 主要函数

#### `get_logger(name: str = None) -> logging.Logger`

获取日志记录器，如果不指定名称，会自动使用调用模块的名称。

```python
# 自动获取模块名
logger = get_logger()

# 指定日志记录器名称
logger = get_logger("my_module")
```

#### `set_log_level(level: str)`

设置全局日志级别。

```python
from lib import set_log_level

set_log_level("DEBUG")  # 显示调试日志
set_log_level("ERROR")  # 只显示错误和严重错误
```

#### `add_file_logging(log_file: str, level: str = "INFO")`

添加文件日志输出。

```python
from lib import add_file_logging

add_file_logging("logs/app.log", "INFO")
```

#### `reload_logging_config()`

重新加载日志配置文件。

```python
from lib import reload_logging_config

reload_logging_config()
```

## 日志级别

| 级别     | 数值 | 用途                       |
| -------- | ---- | -------------------------- |
| CRITICAL | 50   | 严重错误，程序无法继续运行 |
| ERROR    | 40   | 错误信息                   |
| WARNING  | 30   | 警告信息                   |
| INFO     | 20   | 一般信息                   |
| DEBUG    | 10   | 调试信息                   |

## 配置选项说明

### 控制台配置 (console)

- `enabled`: 是否启用控制台输出
- `show_time`: 是否显示时间
- `show_level`: 是否显示日志级别
- `show_path`: 是否显示文件路径
- `rich_tracebacks`: 是否使用 Rich 格式的异常追踪

### 文件配置 (file)

- `enabled`: 是否启用文件输出
- `path`: 日志文件路径
- `level`: 文件日志级别

### 主题配置 (theme)

可以自定义不同日志级别的颜色：

- `info`: 信息日志颜色
- `warning`: 警告日志颜色
- `error`: 错误日志颜色
- `critical`: 严重错误日志颜色
- `debug`: 调试日志颜色

支持的颜色和样式：

- 颜色：`red`, `green`, `blue`, `yellow`, `cyan`, `magenta`, `white`, `black`
- 样式：`bold`, `dim`, `italic`, `underline`, `reverse`

## 最佳实践

1. **模块级别使用**：在每个模块开头获取日志记录器

   ```python
   from lib import get_logger
   logger = get_logger()
   ```

2. **结构化日志**：使用有意义的日志消息

   ```python
   logger.info("用户登录成功", extra={"user_id": 123})
   ```

3. **异常记录**：总是记录异常的完整信息

   ```python
   try:
       # some code
   except Exception as e:
       logger.error("操作失败", exc_info=True)
   ```

4. **合适的日志级别**：选择合适的日志级别
   - `DEBUG`: 详细的调试信息
   - `INFO`: 正常的程序流程信息
   - `WARNING`: 警告，但程序可以继续运行
   - `ERROR`: 错误，但程序可以继续运行
   - `CRITICAL`: 严重错误，程序可能无法继续

## 迁移指南

### 从标准 logging 迁移

**之前：**

```python
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)
```

**现在：**

```python
from lib import get_logger

logger = get_logger()
```

### 从自定义日志迁移

只需要将导入语句改为使用统一的日志系统，其他 API 保持不变。

## 故障排除

### 日志不显示

1. 检查日志级别设置
2. 确认控制台输出已启用
3. 检查配置文件格式是否正确

### 文件日志不工作

1. 确认文件日志已启用
2. 检查日志目录权限
3. 验证文件路径是否正确

### 配置不生效

1. 检查配置文件格式
2. 调用 `reload_logging_config()` 重新加载配置
3. 确认配置文件路径正确
