#!/usr/bin/env python3
"""
日志系统单元测试

测试 logger.py 中的 get_logger 方法和日志文件写入功能
"""

import unittest
import logging
import os
import time
import sys

# 添加src目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
src_path = os.path.join(project_root, 'src')
sys.path.insert(0, src_path)

# 导入日志模块
from lib.logger import get_logger, add_file_logging


class TestLoggerFunctions(unittest.TestCase):
    """测试 logger.py 中的核心函数"""

    def test_get_logger_basic(self):
        """测试 get_logger 基本功能"""
        logger = get_logger("test_basic")
        
        self.assertIsInstance(logger, logging.Logger)
        self.assertEqual(logger.name, "test_basic")
        
        # 测试日志记录（显示到控制台）
        logger.info("✅ get_logger 函数测试 - 基本功能正常")
        logger.warning("⚠️ 警告级别日志测试")
        logger.error("❌ 错误级别日志测试")

    def test_get_logger_auto_name(self):
        """测试 get_logger 自动获取模块名"""
        logger = get_logger()
        
        self.assertIsInstance(logger, logging.Logger)
        self.assertIsNotNone(logger.name)
        self.assertTrue(len(logger.name) > 0)
        
        logger.info(f"✅ 自动模块名获取测试 - 获取到模块名: {logger.name}")

    def test_get_logger_caching(self):
        """测试 get_logger 缓存机制"""
        logger1 = get_logger("cache_test")
        logger2 = get_logger("cache_test")
        
        # 应该返回同一个实例
        self.assertIs(logger1, logger2)
        
        logger1.info("✅ logger 缓存机制测试通过")

    def test_console_time_format(self):
        """测试控制台时间格式"""
        logger = get_logger("time_format_console")
        
        logger.info("⏰ 控制台时间格式测试 - 应显示 [HH:MM:SS] 格式")
        logger.warning("⏰ 时间应该只显示到秒，不显示毫秒")
        
        # 这个测试主要是观察控制台输出的时间格式
        self.assertIsInstance(logger, logging.Logger)


class TestFileLogging(unittest.TestCase):
    """测试文件日志功能"""

    def test_manual_file_logging(self):
        """手动验证文件日志功能"""
        print("\n🔍 手动测试文件日志功能...")
        
        # 确保logs目录存在
        logs_dir = os.path.join(project_root, 'logs')
        os.makedirs(logs_dir, exist_ok=True)
        
        log_file = "logs/manual_test.log"
        
        # 添加文件日志
        add_file_logging(log_file, "INFO")
        
        # 获取logger
        logger = get_logger("manual_file_test")
        
        # 记录测试消息
        test_messages = [
            "手动文件日志测试 - INFO级别",
            "手动文件日志测试 - WARNING级别", 
            "手动文件日志测试 - ERROR级别"
        ]
        
        for i, message in enumerate(test_messages, 1):
            if i == 1:
                logger.info(message)
            elif i == 2:
                logger.warning(message)
            else:
                logger.error(message)
        
        # 强制刷新
        for handler in logging.getLogger().handlers:
            if hasattr(handler, 'flush'):
                handler.flush()
        
        # 等待文件写入
        time.sleep(1.0)
        
        # 检查文件
        full_path = os.path.join(project_root, log_file)
        
        if os.path.exists(full_path):
            with open(full_path, 'r', encoding='utf-8') as f:
                content = f.read()
                
            print(f"📝 文件日志内容:")
            print("-" * 50)
            print(content)
            print("-" * 50)
            
            # 基本验证
            self.assertTrue(len(content) > 0, "文件内容不应为空")
            
            # 验证包含测试消息
            for message in test_messages:
                self.assertIn(message, content)
            
            # 验证时间格式 (YYYY-MM-DD HH:MM:SS)
            import re
            time_pattern = r'\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}'
            time_matches = re.findall(time_pattern, content)
            self.assertTrue(len(time_matches) >= 3, "应该找到至少3个时间戳")
            
            print(f"✅ 文件日志测试成功！时间格式正确：{time_matches[0]}")
            print(f"📁 日志文件路径: {full_path}")
            
        else:
            self.fail(f"日志文件未创建: {full_path}")


if __name__ == '__main__':
    print("=" * 80)
    print("🧪 测试 logger.py 中的 get_logger 方法和 logs 目录文件写入功能")
    print("=" * 80)
    
    # 运行测试
    unittest.main(verbosity=2)
