#!/usr/bin/env python3
"""
WQ因子挖掘系统 - 集成测试脚本
验证所有核心组件的正常工作
"""

from lib.queue_manager import AlphaQueue
from lib.base_worker import BaseWorker, Task, TaskStatus
from lib.notification_system import NotificationSystem, NotificationLevel
from lib.account_scheduler import AccountScheduler
from lib.smart_submitter import SmartSubmitter
from lib.quality_checker import QualityChecker
from lib.trade_when_generator import TradeWhenGenerator
from lib.factor_combiner import FactorCombiner
from lib.factor_generator import FactorGenerator
from lib.simple_logger import get_logger
from lib.db_config_reader import get_config_reader
import sys
import asyncio
import traceback
from pathlib import Path
from datetime import datetime

# 添加项目路径
sys.path.insert(0, str(Path(__file__).parent))


logger = get_logger(__name__)


class TestWorker(BaseWorker):
    """测试用的Worker实现"""

    async def process_task(self, task):
        """处理测试任务"""
        await asyncio.sleep(0.1)  # 模拟处理时间
        return f"处理完成: {task.input_data.get('factor_expression', 'test')}"

    def get_supported_task_types(self):
        return ['test_factor_generation', 'test_quality_check']


async def test_database_config():
    """测试数据库配置读取器"""
    logger.info("🔧 测试数据库配置读取器...")

    try:
        config_reader = get_config_reader()

        # 测试基本配置读取
        api_base = config_reader.get_url('api_base')
        auth_url = config_reader.get_url('auth')

        assert api_base is not None, "API基础URL配置缺失"
        assert auth_url is not None, "认证URL配置缺失"

        # 测试地区配置
        regions = config_reader.get_all_regions()
        assert len(regions) > 0, "地区配置缺失"

        # 测试URL配置
        url_configs = config_reader.get_all_url_configs()
        assert len(url_configs) > 0, "URL配置缺失"

        logger.info("✅ 数据库配置读取器测试通过")
        return True, {
            'api_base': api_base,
            'auth_url': auth_url,
            'regions_count': len(regions),
            'url_configs_count': len(url_configs)
        }

    except Exception as e:
        logger.error(f"❌ 数据库配置读取器测试失败: {e}")
        return False, str(e)


async def test_queue_manager():
    """测试队列管理器"""
    logger.info("📋 测试队列管理器...")

    try:
        queue = AlphaQueue()

        # 测试批量添加因子
        test_factors = [
            "rank(close)",
            "ts_rank(volume, 20)",
            "group_rank(returns, sector)"
        ]

        batch_id = await queue.add_factor_batch(test_factors, batch_size=2)
        assert batch_id is not None, "批量添加失败"

        # 测试获取待处理因子
        pending = await queue.get_pending_factors(limit=5)
        assert len(pending) > 0, "没有待处理因子"

        # 测试状态更新
        factor_id = pending[0]['id']
        success = await queue.update_factor_status(factor_id, 'processing')
        assert success, "状态更新失败"

        # 测试统计信息
        stats = await queue.get_queue_statistics()

        logger.info("✅ 队列管理器测试通过")
        return True, {
            'batch_id': batch_id,
            'pending_count': len(pending),
            'stats': stats
        }

    except Exception as e:
        logger.error(f"❌ 队列管理器测试失败: {e}")
        return False, str(e)


async def test_factor_components():
    """测试因子生成组件"""
    logger.info("🧬 测试因子生成组件...")

    try:
        # 测试因子生成器
        generator = FactorGenerator()
        test_fields = ["close", "volume", "returns"]
        test_ops = ["rank", "ts_rank", "zscore"]

        factors = generator.first_order_factory(test_fields, test_ops)
        assert len(factors) > 0, "因子生成失败"

        # 测试因子组合器
        combiner = FactorCombiner()
        fo_factors = [("rank(close)", 6), ("ts_rank(volume, 20)", 8)]

        so_factors = combiner.generate_second_order_factors(fo_factors, "USA")
        assert len(so_factors) > 0, "二阶因子生成失败"

        # 测试TradeWhen生成器
        tw_generator = TradeWhenGenerator()
        tw_factors = tw_generator.trade_when_factory(
            "trade_when", "rank(close)", "usa", 1)
        assert len(tw_factors) > 0, "TradeWhen因子生成失败"

        logger.info("✅ 因子生成组件测试通过")
        return True, {
            'first_order_count': len(factors),
            'second_order_count': len(so_factors['USA']) if 'USA' in so_factors else 0,
            'trade_when_count': len(tw_factors)
        }

    except Exception as e:
        logger.error(f"❌ 因子生成组件测试失败: {e}")
        return False, str(e)


async def test_quality_checker():
    """测试质量检测器"""
    logger.info("🔍 测试质量检测器...")

    try:
        checker = QualityChecker()

        # 测试综合质量检查
        test_alpha_ids = ['alpha_001', 'alpha_002']
        results = await checker.run_comprehensive_check(test_alpha_ids, parallel_workers=2)

        assert 'summary' in results, "检查结果格式错误"
        assert results['summary']['total_alphas'] == 2, "检查数量不符"

        logger.info("✅ 质量检测器测试通过")
        return True, {
            'checked_alphas': results['summary']['total_alphas'],
            'passed_count': results['summary']['passed_count'],
            'failed_count': results['summary']['failed_count']
        }

    except Exception as e:
        logger.error(f"❌ 质量检测器测试失败: {e}")
        return False, str(e)


async def test_account_scheduler():
    """测试账户调度器"""
    logger.info("👥 测试账户调度器...")

    try:
        scheduler = AccountScheduler()
        await scheduler.start()

        # 测试账户分配
        account = await scheduler.assign_account("test_task_001")
        if account:
            # 测试账户释放
            await scheduler.release_account(account.account_id, "test_task_001", True, 1.5)

        # 测试统计信息
        stats = scheduler.get_scheduler_stats()
        health_report = scheduler.get_account_health_report()

        await scheduler.stop()

        logger.info("✅ 账户调度器测试通过")
        return True, {
            'account_assigned': account is not None,
            'total_accounts': stats['total_accounts'],
            'strategy': health_report['strategy']
        }

    except Exception as e:
        logger.error(f"❌ 账户调度器测试失败: {e}")
        return False, str(e)


async def test_notification_system():
    """测试通知系统"""
    logger.info("📨 测试通知系统...")

    try:
        notification_system = NotificationSystem()
        await notification_system.start()

        # 测试发送通知
        success = await notification_system.send_event(
            event_type="system_test",
            title="集成测试通知",
            message="这是一个测试通知消息",
            level=NotificationLevel.INFO,
            source="integration_test"
        )

        assert success, "通知发送失败"

        # 等待通知处理
        await asyncio.sleep(0.5)

        # 测试统计信息
        stats = notification_system.get_system_stats()
        channel_stats = notification_system.get_channel_stats()

        await notification_system.stop()

        logger.info("✅ 通知系统测试通过")
        return True, {
            'notification_sent': success,
            'total_events': stats['total_events_processed'],
            'active_channels': stats['active_channels']
        }

    except Exception as e:
        logger.error(f"❌ 通知系统测试失败: {e}")
        return False, str(e)


async def test_base_worker():
    """测试BaseWorker框架"""
    logger.info("⚙️ 测试BaseWorker框架...")

    try:
        worker = TestWorker(worker_id="test_worker_001")

        # 创建测试任务
        test_task = Task(
            task_id="test_task_001",
            task_type="test_factor_generation",
            input_data={"factor_expression": "rank(close)"},
            max_retries=2
        )

        # 提交任务
        submit_success = await worker.submit_task(test_task)
        assert submit_success, "任务提交失败"

        # 启动Worker（短时间）
        worker_task = asyncio.create_task(worker.start())

        # 等待任务处理
        await asyncio.sleep(1)

        # 停止Worker
        await worker.stop(timeout=2)

        # 检查统计信息
        stats = worker.get_stats()

        logger.info("✅ BaseWorker框架测试通过")
        return True, {
            'task_submitted': submit_success,
            'tasks_processed': stats['tasks_processed'],
            'worker_state': stats['state']
        }

    except Exception as e:
        logger.error(f"❌ BaseWorker框架测试失败: {e}")
        return False, str(e)


async def run_integration_tests():
    """运行完整的集成测试"""
    logger.info("🚀 开始WQ因子挖掘系统集成测试")
    logger.info("=" * 60)

    test_results = {}
    test_functions = [
        ("数据库配置读取器", test_database_config),
        ("队列管理器", test_queue_manager),
        ("因子生成组件", test_factor_components),
        ("质量检测器", test_quality_checker),
        ("账户调度器", test_account_scheduler),
        ("通知系统", test_notification_system),
        ("BaseWorker框架", test_base_worker)
    ]

    passed_tests = 0
    total_tests = len(test_functions)

    for test_name, test_func in test_functions:
        try:
            success, result = await test_func()
            test_results[test_name] = {
                'success': success,
                'result': result,
                'timestamp': datetime.now().isoformat()
            }

            if success:
                passed_tests += 1
                logger.info(f"✅ {test_name} - 通过")
            else:
                logger.error(f"❌ {test_name} - 失败: {result}")

        except Exception as e:
            logger.error(f"💥 {test_name} - 异常: {e}")
            logger.debug(traceback.format_exc())
            test_results[test_name] = {
                'success': False,
                'result': f"异常: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }

    # 生成测试报告
    logger.info("=" * 60)
    logger.info("📊 集成测试报告")
    logger.info(
        f"通过率: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")

    if passed_tests == total_tests:
        logger.info("🎉 所有测试通过！系统集成成功！")
    else:
        logger.warning(f"⚠️ {total_tests - passed_tests} 个测试失败，需要检查")

    # 输出详细结果
    logger.info("\n📋 详细测试结果:")
    for test_name, result in test_results.items():
        status = "✅" if result['success'] else "❌"
        logger.info(f"{status} {test_name}: {result['result']}")

    # 保存测试报告
    try:
        report_file = Path("logs/integration_test_report.json")
        report_file.parent.mkdir(exist_ok=True)

        with open(report_file, 'w', encoding='utf-8') as f:
            import json
            json.dump({
                'timestamp': datetime.now().isoformat(),
                'passed_tests': passed_tests,
                'total_tests': total_tests,
                'pass_rate': passed_tests / total_tests,
                'results': test_results
            }, f, indent=2, ensure_ascii=False)

        logger.info(f"📄 测试报告已保存: {report_file}")

    except Exception as e:
        logger.error(f"保存测试报告失败: {e}")

    return passed_tests == total_tests


async def main():
    """主函数"""
    try:
        success = await run_integration_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
        return 130
    except Exception as e:
        logger.error(f"集成测试异常: {e}")
        logger.debug(traceback.format_exc())
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
