"""
数据库配置读取器

提供与原ConfigReader相同的接口，但从数据库获取配置信息
替代原有的JSON配置文件读取方式
"""

from typing import Dict, Any, List, Optional
from .db import create_database_manager, DatabaseManager
from .models import UserAccountModel, RegionModel, UrlConfigModel
from .logger import get_logger

logger = get_logger(__name__)

# 全局DbConfigReader实例缓存
_db_config_reader_cache = {}


class DbConfigReader:
    """数据库配置读取工具"""

    def __init__(self, db_path: str = None):
        """
        初始化数据库配置读取器

        Args:
            db_path: 数据库文件路径，默认使用默认路径
        """
        # 使用缓存的数据库管理器实例，避免重复初始化
        self.db_manager = create_database_manager(db_path)
        logger.debug(f"数据库配置读取器初始化完成，数据库路径: {self.db_manager.db_path}")

    def get_config(self, key: str) -> Optional[str]:
        """
        获取配置值

        Args:
            key: 配置键

        Returns:
            str: 配置值，如果不存在返回None
        """
        return self.db_manager.get_url(key)

    def get_url(self, url_key: str) -> Optional[str]:
        """
        获取URL配置

        Args:
            url_key: URL键

        Returns:
            str: URL路径，如果不存在返回None
        """
        return self.db_manager.get_url(url_key)

    def get_region_info(self, region_code: str) -> Optional[Dict[str, Any]]:
        """
        获取地区信息

        Args:
            region_code: 地区代码

        Returns:
            Dict: 地区信息字典，如果不存在返回None
        """
        return self.db_manager.get_region(region_code)

    def get_all_regions(self) -> List[Dict[str, Any]]:
        """
        获取所有地区信息

        Returns:
            List[Dict]: 地区信息列表
        """
        return self.db_manager.get_all_regions()

    def get_user_account(self, email: str) -> Optional[Dict[str, Any]]:
        """
        获取用户账户信息

        Args:
            email: 用户邮箱

        Returns:
            Dict: 用户账户信息字典，如果不存在返回None
        """
        return self.db_manager.get_user_account(email)

    def get_user_account_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        根据姓名获取用户账户信息

        Args:
            name: 用户姓名

        Returns:
            Dict: 用户账户信息字典，如果不存在返回None
        """
        return self.db_manager.get_user_account_by_name(name)

    def get_all_url_configs(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """
        获取所有URL配置

        Args:
            active_only: 是否只返回激活的配置

        Returns:
            List[Dict]: URL配置列表
        """
        return self.db_manager.get_all_url_configs(active_only)

    def get_all_datasets(self) -> List[Dict[str, Any]]:
        """
        获取所有数据集信息

        Returns:
            List[Dict]: 数据集列表
        """
        return self.db_manager.get_all_datasets()

    def get_all_accounts(self) -> List[Dict[str, Any]]:
        """
        获取所有用户账户

        Returns:
            List[Dict]: 所有用户账户信息的列表
        """
        return self.db_manager.get_all_user_accounts()

    # ===========================================
    # 向后兼容方法
    # ===========================================

    def get_alpha_base_url(self) -> Optional[str]:
        """获取Alpha基础URL（向后兼容）"""
        return self.get_url('alpha_base')

    def get_api_base_url(self) -> Optional[str]:
        """获取API基础URL（向后兼容）"""
        return self.get_url('api_base')

    # ===========================================
    # 数据库管理方法
    # ===========================================

    def is_initialized(self) -> bool:
        """检查数据库是否已初始化"""
        return self.db_manager.is_initialized()

    def close(self) -> None:
        """关闭数据库连接"""
        self.db_manager.close()


def get_config_reader(db_path: str = None) -> DbConfigReader:
    """
    获取DbConfigReader单例实例

    Args:
        db_path: 数据库文件路径，默认使用默认路径

    Returns:
        DbConfigReader: 单例实例
    """
    # 如果没有指定路径，使用默认路径
    if db_path is None:
        from .db import DatabaseManager
        temp_manager = DatabaseManager()
        actual_db_path = temp_manager.db_path
    else:
        actual_db_path = db_path

    # 标准化路径作为缓存键
    import os
    cache_key = os.path.abspath(actual_db_path)

    # 如果缓存中存在，返回缓存的实例
    if cache_key in _db_config_reader_cache:
        logger.debug(f"使用缓存的DbConfigReader实例: {cache_key}")
        return _db_config_reader_cache[cache_key]

    # 创建新的实例
    instance = DbConfigReader(db_path)
    _db_config_reader_cache[cache_key] = instance
    logger.debug(f"创建并缓存新的DbConfigReader实例: {cache_key}")

    return instance


def clear_config_reader_cache():
    """清理DbConfigReader缓存"""
    global _db_config_reader_cache
    for reader in _db_config_reader_cache.values():
        try:
            reader.close()
        except Exception as e:
            logger.warning(f"关闭缓存的DbConfigReader时出错: {e}")
    _db_config_reader_cache.clear()
    logger.debug("DbConfigReader缓存已清理")
