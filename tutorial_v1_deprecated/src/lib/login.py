import requests
import time
import json
from .db_config_reader import get_config_reader
from .logger import get_logger

# 获取当前模块的日志记录器
logger = get_logger()


def login(db_path: str = None) -> tuple[requests.Session, dict]:
    """
    使用数据库配置进行登录认证

    Args:
        db_path: 数据库文件路径，默认为None使用默认路径

    Returns:
        tuple[requests.Session, dict]: 已认证的会话对象和token信息
            - session: 已认证的会话对象
            - token_info: 包含token失效时间等信息的字典

    Raises:
        Exception: 当认证失败时抛出异常
    """
    # 读取配置信息
    config_reader = get_config_reader(db_path)
    all_accounts = config_reader.get_all_accounts()

    if not all_accounts:
        raise ValueError("数据库中未配置任何用户账户")

    # 默认使用第一个账户进行登录
    account_info = all_accounts[0]
    logger.info(f"使用账户 '{account_info.get('email')}' 进行登录")

    # 获取用户名和密码
    email = account_info.get('email', '')
    password = account_info.get('password', '')

    if not email or not password:
        raise ValueError("用户名或密码未在数据库中设置")

    # 创建会话以持久化存储头信息
    session = requests.Session()

    # 保存凭据到会话中
    session.auth = (email, password)

    # 重试机制：处理 API 速率限制和网络故障
    retry_count = 0
    network_retry_count = 0

    while True:
        try:
            # 发送POST请求到/authentication API
            response = session.post(auth_url)
            info_ = response.content.decode('utf-8')
            logger.info(info_)

            # 检查是否为 API 速率限制
            if "API rate limit exceeded" in info_:
                retry_count += 1
                logger.warning(
                    f"API 速率限制超出，第 {retry_count} 次重试，等待 3 秒后重新登录...")
                time.sleep(3)
                continue

            # 检查其他错误
            if "INVALID_CREDENTIALS" in info_:
                raise Exception(
                    "你的账号密码有误，请在配置文件中输入正确的邮箱和密码！\n"
                    "Your email or password is incorrect. Please enter the correct email and password in config file!"
                )

            # 登录成功，解析token信息
            logger.info("登录成功！Login successful!")
            if retry_count > 0:
                logger.info(f"经过 {retry_count} 次API重试后登录成功")
            if network_retry_count > 0:
                logger.info(f"经过 {network_retry_count} 次网络重试后登录成功")

            # 解析token信息
            token_info = {}
            try:
                response_data = json.loads(info_)
                if 'token' in response_data and 'expiry' in response_data['token']:
                    token_info = {
                        'expiry_seconds': response_data['token']['expiry'],
                        'login_time': time.time(),
                        'expiry_time': time.time() + response_data['token']['expiry']
                    }
                    logger.debug(
                        f"Token将在 {token_info['expiry_seconds']} 秒后失效")
                else:
                    logger.warning("响应中未找到token失效时间信息")
            except json.JSONDecodeError:
                logger.warning("无法解析登录响应为JSON格式")
            except Exception as e:
                logger.warning(f"解析token信息时出错: {e}")

            return session, token_info

        except requests.RequestException as e:
            network_retry_count += 1
            logger.warning(f"网络请求失败: {e}")
            logger.warning(f"第 {network_retry_count} 次网络重试，等待 60 秒后重新尝试登录...")
            time.sleep(60)
            continue

        except Exception as e:
            if "API rate limit exceeded" not in str(e):
                logger.error(f"登录过程中出错: {e}")
                raise
