"""
BaseWorker框架 - 统一的工作流处理器
支持任务调度、状态机管理、错误处理、重试机制和监控
"""

import asyncio
from abc import ABC, abstractmethod
from enum import Enum
from typing import Dict, Any, Optional, List, Callable, Type, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import traceback
import uuid
import json
from concurrent.futures import ThreadPoolExecutor, TimeoutError as FutureTimeoutError
from .logger import get_logger
from .db_config_reader import get_config_reader

logger = get_logger(__name__)


class WorkerState(Enum):
    """Worker状态枚举"""
    IDLE = "idle"
    RUNNING = "running"
    PAUSED = "paused"
    STOPPING = "stopping"
    STOPPED = "stopped"
    ERROR = "error"
    COMPLETED = "completed"


class TaskStatus(Enum):
    """任务状态枚举"""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    RETRYING = "retrying"
    CANCELLED = "cancelled"
    TIMEOUT = "timeout"


@dataclass
class TaskResult:
    """任务执行结果"""
    task_id: str
    status: TaskStatus
    start_time: datetime
    end_time: Optional[datetime] = None
    result_data: Optional[Any] = None
    error_message: Optional[str] = None
    error_traceback: Optional[str] = None
    retry_count: int = 0
    execution_time: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class Task:
    """任务定义"""
    task_id: str
    task_type: str
    input_data: Dict[str, Any]
    priority: int = 0
    max_retries: int = 3
    timeout_seconds: Optional[int] = None
    created_time: datetime = field(default_factory=datetime.now)
    scheduled_time: Optional[datetime] = None
    depends_on: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        if not self.task_id:
            self.task_id = str(uuid.uuid4())


@dataclass
class WorkerStats:
    """Worker统计信息"""
    worker_id: str
    state: WorkerState
    start_time: datetime
    tasks_processed: int = 0
    tasks_completed: int = 0
    tasks_failed: int = 0
    tasks_retried: int = 0
    average_execution_time: float = 0.0
    last_activity_time: Optional[datetime] = None
    current_task: Optional[str] = None
    error_count: int = 0
    last_error_time: Optional[datetime] = None


class BaseWorker(ABC):
    """基础Worker类 - 所有Worker的抽象基类"""

    def __init__(self,
                 worker_id: Optional[str] = None,
                 max_concurrent_tasks: int = 5,
                 heartbeat_interval: int = 60,
                 db_path: Optional[str] = None):
        self.worker_id = worker_id or f"{self.__class__.__name__}_{uuid.uuid4().hex[:8]}"
        self.max_concurrent_tasks = max_concurrent_tasks
        self.heartbeat_interval = heartbeat_interval

        # 状态管理
        self.state = WorkerState.IDLE
        self.stats = WorkerStats(
            worker_id=self.worker_id,
            state=self.state,
            start_time=datetime.now()
        )

        # 任务管理
        self.task_queue: asyncio.Queue = asyncio.Queue()
        self.active_tasks: Dict[str, Task] = {}
        self.completed_tasks: Dict[str, TaskResult] = {}
        self.failed_tasks: Dict[str, TaskResult] = {}

        # 同步控制
        self._stop_event = asyncio.Event()
        self._pause_event = asyncio.Event()
        self._running_tasks: set = set()
        self._semaphore = asyncio.Semaphore(max_concurrent_tasks)

        # 配置
        self.config_reader = get_config_reader(db_path)
        self._load_worker_config()

        # 监控和回调
        self.event_callbacks: Dict[str, List[Callable]] = {
            'task_started': [],
            'task_completed': [],
            'task_failed': [],
            'worker_started': [],
            'worker_stopped': [],
            'worker_error': []
        }

    def _load_worker_config(self):
        """加载Worker配置 - 使用默认值，因为数据库配置侧重于URL和认证"""
        self.timeout_minutes = 30  # 30分钟超时
        self.retry_attempts = 3  # 3次重试
        # heartbeat_interval已在初始化时设置

    @abstractmethod
    async def process_task(self, task: Task) -> Any:
        """
        处理单个任务 - 子类必须实现

        Args:
            task: 要处理的任务

        Returns:
            任务处理结果
        """
        pass

    @abstractmethod
    def get_supported_task_types(self) -> List[str]:
        """
        获取支持的任务类型 - 子类必须实现

        Returns:
            支持的任务类型列表
        """
        pass

    async def start(self) -> None:
        """启动Worker"""
        if self.state != WorkerState.IDLE:
            logger.warning(f"Worker {self.worker_id} 已在运行状态")
            return

        logger.info(f"启动Worker: {self.worker_id}")
        self.state = WorkerState.RUNNING
        self.stats.state = self.state

        # 触发启动事件
        await self._trigger_event('worker_started', {'worker_id': self.worker_id})

        # 启动主要任务
        try:
            await asyncio.gather(
                self._main_loop(),
                self._heartbeat_loop(),
                return_exceptions=True
            )
        except Exception as e:
            logger.error(f"Worker {self.worker_id} 运行异常: {e}")
            await self._handle_worker_error(e)
        finally:
            await self._cleanup()

    async def stop(self, timeout: Optional[float] = None) -> None:
        """停止Worker"""
        logger.info(f"停止Worker: {self.worker_id}")
        self.state = WorkerState.STOPPING
        self.stats.state = self.state

        # 设置停止事件
        self._stop_event.set()

        # 等待正在执行的任务完成
        if self._running_tasks and timeout:
            try:
                await asyncio.wait_for(
                    asyncio.gather(*self._running_tasks,
                                   return_exceptions=True),
                    timeout=timeout
                )
            except asyncio.TimeoutError:
                logger.warning(f"Worker {self.worker_id} 停止超时，强制终止")

        self.state = WorkerState.STOPPED
        self.stats.state = self.state

        # 触发停止事件
        await self._trigger_event('worker_stopped', {'worker_id': self.worker_id})

    async def pause(self) -> None:
        """暂停Worker"""
        logger.info(f"暂停Worker: {self.worker_id}")
        self.state = WorkerState.PAUSED
        self.stats.state = self.state
        self._pause_event.set()

    async def resume(self) -> None:
        """恢复Worker"""
        logger.info(f"恢复Worker: {self.worker_id}")
        self.state = WorkerState.RUNNING
        self.stats.state = self.state
        self._pause_event.clear()

    async def submit_task(self, task: Task) -> bool:
        """
        提交任务到队列

        Args:
            task: 要提交的任务

        Returns:
            是否提交成功
        """
        if task.task_type not in self.get_supported_task_types():
            logger.error(f"Worker {self.worker_id} 不支持任务类型: {task.task_type}")
            return False

        try:
            await self.task_queue.put(task)
            logger.debug(f"任务已提交: {task.task_id} -> {self.worker_id}")
            return True
        except Exception as e:
            logger.error(f"提交任务失败: {task.task_id}, 错误: {e}")
            return False

    async def submit_batch_tasks(self, tasks: List[Task]) -> Dict[str, bool]:
        """批量提交任务"""
        results = {}
        for task in tasks:
            results[task.task_id] = await self.submit_task(task)
        return results

    async def _main_loop(self) -> None:
        """主处理循环"""
        while not self._stop_event.is_set():
            try:
                # 检查暂停状态
                if self._pause_event.is_set():
                    await asyncio.sleep(1)
                    continue

                # 获取任务
                try:
                    task = await asyncio.wait_for(self.task_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue

                # 检查任务依赖
                if not await self._check_task_dependencies(task):
                    # 重新放回队列等待依赖完成
                    await self.task_queue.put(task)
                    await asyncio.sleep(1)
                    continue

                # 处理任务
                await self._execute_task_with_semaphore(task)

            except Exception as e:
                logger.error(f"主循环异常: {e}")
                await asyncio.sleep(1)

    async def _execute_task_with_semaphore(self, task: Task) -> None:
        """使用信号量控制的任务执行"""
        async with self._semaphore:
            task_coroutine = self._execute_task(task)
            self._running_tasks.add(task_coroutine)

            try:
                await task_coroutine
            finally:
                self._running_tasks.discard(task_coroutine)

    async def _execute_task(self, task: Task) -> None:
        """执行单个任务"""
        task_id = task.task_id
        start_time = datetime.now()

        # 初始化任务结果
        result = TaskResult(
            task_id=task_id,
            status=TaskStatus.RUNNING,
            start_time=start_time
        )

        try:
            # 更新统计和状态
            self.active_tasks[task_id] = task
            self.stats.current_task = task_id
            self.stats.tasks_processed += 1
            self.stats.last_activity_time = start_time

            # 触发任务开始事件
            await self._trigger_event('task_started', {
                'task_id': task_id,
                'task_type': task.task_type,
                'worker_id': self.worker_id
            })

            # 执行任务（带超时控制）
            timeout = task.timeout_seconds or (self.timeout_minutes * 60)

            try:
                task_result = await asyncio.wait_for(
                    self.process_task(task),
                    timeout=timeout
                )

                # 任务成功完成
                result.status = TaskStatus.COMPLETED
                result.result_data = task_result
                result.end_time = datetime.now()
                result.execution_time = (
                    result.end_time - start_time).total_seconds()

                self.completed_tasks[task_id] = result
                self.stats.tasks_completed += 1

                # 更新平均执行时间
                self._update_average_execution_time(result.execution_time)

                logger.info(
                    f"任务完成: {task_id} 耗时: {result.execution_time:.2f}秒")

                # 触发任务完成事件
                await self._trigger_event('task_completed', {
                    'task_id': task_id,
                    'result': result,
                    'worker_id': self.worker_id
                })

            except asyncio.TimeoutError:
                result.status = TaskStatus.TIMEOUT
                result.error_message = f"任务执行超时 ({timeout}秒)"
                raise

            except Exception as e:
                result.status = TaskStatus.FAILED
                result.error_message = str(e)
                result.error_traceback = traceback.format_exc()
                raise

        except Exception as e:
            # 任务执行失败
            result.end_time = datetime.now()
            result.execution_time = (
                result.end_time - start_time).total_seconds()

            # 尝试重试
            if task.max_retries > 0 and result.retry_count < task.max_retries:
                result.retry_count += 1
                result.status = TaskStatus.RETRYING
                self.stats.tasks_retried += 1

                logger.warning(
                    f"任务重试: {task_id} ({result.retry_count}/{task.max_retries})")

                # 延迟后重新提交任务
                await asyncio.sleep(min(2 ** result.retry_count, 60))  # 指数退避
                task.max_retries -= 1  # 减少剩余重试次数
                await self.task_queue.put(task)
            else:
                # 重试次数用尽，任务最终失败
                self.failed_tasks[task_id] = result
                self.stats.tasks_failed += 1
                self.stats.error_count += 1
                self.stats.last_error_time = datetime.now()

                logger.error(f"任务失败: {task_id}, 错误: {result.error_message}")

                # 触发任务失败事件
                await self._trigger_event('task_failed', {
                    'task_id': task_id,
                    'result': result,
                    'worker_id': self.worker_id
                })

        finally:
            # 清理
            self.active_tasks.pop(task_id, None)
            self.stats.current_task = None

    async def _check_task_dependencies(self, task: Task) -> bool:
        """检查任务依赖是否满足"""
        if not task.depends_on:
            return True

        for dep_task_id in task.depends_on:
            if dep_task_id not in self.completed_tasks:
                return False

            dep_result = self.completed_tasks[dep_task_id]
            if dep_result.status != TaskStatus.COMPLETED:
                return False

        return True

    def _update_average_execution_time(self, execution_time: float) -> None:
        """更新平均执行时间"""
        completed = self.stats.tasks_completed
        if completed == 1:
            self.stats.average_execution_time = execution_time
        else:
            # 滑动平均
            self.stats.average_execution_time = (
                (self.stats.average_execution_time *
                 (completed - 1) + execution_time) / completed
            )

    async def _heartbeat_loop(self) -> None:
        """心跳循环"""
        while not self._stop_event.is_set():
            try:
                await self._send_heartbeat()
                await asyncio.sleep(self.heartbeat_interval)
            except Exception as e:
                logger.error(f"心跳发送失败: {e}")
                await asyncio.sleep(5)

    async def _send_heartbeat(self) -> None:
        """发送心跳"""
        heartbeat_data = {
            'worker_id': self.worker_id,
            'timestamp': datetime.now().isoformat(),
            'state': self.state.value,
            'stats': {
                'tasks_processed': self.stats.tasks_processed,
                'tasks_completed': self.stats.tasks_completed,
                'tasks_failed': self.stats.tasks_failed,
                'queue_size': self.task_queue.qsize(),
                'active_tasks': len(self.active_tasks),
                'average_execution_time': self.stats.average_execution_time
            }
        }

        logger.debug(f"心跳: {self.worker_id} -> {heartbeat_data}")

        # 这里可以实现心跳发送逻辑，比如写入数据库或发送到监控系统
        # await self._send_heartbeat_to_monitor(heartbeat_data)

    async def _handle_worker_error(self, error: Exception) -> None:
        """处理Worker级别的错误"""
        self.state = WorkerState.ERROR
        self.stats.state = self.state
        self.stats.error_count += 1
        self.stats.last_error_time = datetime.now()

        error_data = {
            'worker_id': self.worker_id,
            'error_message': str(error),
            'error_traceback': traceback.format_exc(),
            'timestamp': datetime.now().isoformat()
        }

        logger.error(f"Worker错误: {self.worker_id}, 错误: {error}")

        # 触发Worker错误事件
        await self._trigger_event('worker_error', error_data)

    async def _cleanup(self) -> None:
        """清理资源"""
        logger.info(f"清理Worker资源: {self.worker_id}")

        # 取消所有活动任务
        for task_coroutine in list(self._running_tasks):
            if not task_coroutine.done():
                task_coroutine.cancel()

        # 清理任务队列中的剩余任务
        while not self.task_queue.empty():
            try:
                task = self.task_queue.get_nowait()
                logger.warning(f"清理未处理任务: {task.task_id}")
            except asyncio.QueueEmpty:
                break

    async def _trigger_event(self, event_type: str, data: Dict[str, Any]) -> None:
        """触发事件回调"""
        callbacks = self.event_callbacks.get(event_type, [])

        for callback in callbacks:
            try:
                if asyncio.iscoroutinefunction(callback):
                    await callback(data)
                else:
                    callback(data)
            except Exception as e:
                logger.error(f"事件回调执行失败: {event_type}, 错误: {e}")

    def add_event_callback(self, event_type: str, callback: Callable) -> None:
        """添加事件回调"""
        if event_type in self.event_callbacks:
            self.event_callbacks[event_type].append(callback)
        else:
            logger.warning(f"未知事件类型: {event_type}")

    def remove_event_callback(self, event_type: str, callback: Callable) -> None:
        """移除事件回调"""
        if event_type in self.event_callbacks:
            try:
                self.event_callbacks[event_type].remove(callback)
            except ValueError:
                pass

    def get_stats(self) -> Dict[str, Any]:
        """获取Worker统计信息"""
        return {
            'worker_id': self.worker_id,
            'state': self.state.value,
            'start_time': self.stats.start_time.isoformat(),
            'tasks_processed': self.stats.tasks_processed,
            'tasks_completed': self.stats.tasks_completed,
            'tasks_failed': self.stats.tasks_failed,
            'tasks_retried': self.stats.tasks_retried,
            'average_execution_time': self.stats.average_execution_time,
            'current_task': self.stats.current_task,
            'queue_size': self.task_queue.qsize(),
            'active_tasks_count': len(self.active_tasks),
            'error_count': self.stats.error_count,
            'last_activity_time': (self.stats.last_activity_time.isoformat()
                                   if self.stats.last_activity_time else None),
            'last_error_time': (self.stats.last_error_time.isoformat()
                                if self.stats.last_error_time else None)
        }

    def get_task_status(self, task_id: str) -> Optional[Dict[str, Any]]:
        """获取任务状态"""
        # 检查活动任务
        if task_id in self.active_tasks:
            task = self.active_tasks[task_id]
            return {
                'task_id': task_id,
                'status': 'running',
                'worker_id': self.worker_id,
                'task_type': task.task_type,
                'started_time': task.created_time.isoformat()
            }

        # 检查完成任务
        if task_id in self.completed_tasks:
            result = self.completed_tasks[task_id]
            return {
                'task_id': task_id,
                'status': result.status.value,
                'worker_id': self.worker_id,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat() if result.end_time else None,
                'execution_time': result.execution_time,
                'result_data': result.result_data
            }

        # 检查失败任务
        if task_id in self.failed_tasks:
            result = self.failed_tasks[task_id]
            return {
                'task_id': task_id,
                'status': result.status.value,
                'worker_id': self.worker_id,
                'start_time': result.start_time.isoformat(),
                'end_time': result.end_time.isoformat() if result.end_time else None,
                'execution_time': result.execution_time,
                'error_message': result.error_message,
                'retry_count': result.retry_count
            }

        return None

    def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        uptime = datetime.now() - self.stats.start_time

        # 计算健康分数
        health_score = 100.0

        # 基于错误率调整
        if self.stats.tasks_processed > 0:
            error_rate = self.stats.tasks_failed / self.stats.tasks_processed
            health_score -= error_rate * 50

        # 基于状态调整
        if self.state == WorkerState.ERROR:
            health_score = 0
        elif self.state == WorkerState.PAUSED:
            health_score = 50
        elif self.state == WorkerState.STOPPING:
            health_score = 25

        return {
            'worker_id': self.worker_id,
            'state': self.state.value,
            'health_score': max(0, min(100, health_score)),
            'uptime_seconds': uptime.total_seconds(),
            'is_healthy': health_score > 70,
            'last_activity': (self.stats.last_activity_time.isoformat()
                              if self.stats.last_activity_time else None),
            'queue_status': {
                'size': self.task_queue.qsize(),
                'is_full': self.task_queue.qsize() > 1000,  # 队列过满判断
                'active_tasks': len(self.active_tasks)
            }
        }
