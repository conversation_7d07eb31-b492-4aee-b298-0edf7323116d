"""
数据模型定义 - 基于Peewee ORM
专注于数据结构定义，不包含业务逻辑
"""

import json
from datetime import datetime
from peewee import *
from playhouse.sqlite_ext import SqliteExtDatabase

# 数据库实例
db = SqliteExtDatabase(None)


class BaseModel(Model):
    """基础模型类"""
    id = AutoField(primary_key=True)
    created_at = DateTimeField(default=datetime.now)
    updated_at = DateTimeField(default=datetime.now)

    class Meta:
        database = db

    def save(self, *args, **kwargs):
        self.updated_at = datetime.now()
        return super().save(*args, **kwargs)


class UserAccountModel(BaseModel):
    """用户账户数据模型 - 扩展支持多账户管理"""
    email = CharField(max_length=255, unique=True, index=True)
    name = CharField(max_length=100, unique=True, index=True)  # 新增用户名字段
    password = CharField(max_length=255)  # 加密存储
    name = CharField(max_length=100, index=True)

    # 多账户管理字段
    # 'active', 'disabled', 'error', 'resting'
    status = CharField(max_length=20, default='active', index=True)
    last_login = DateTimeField(null=True, index=True)
    error_count = IntegerField(default=0)
    max_concurrent_tasks = IntegerField(default=5)
    preferred_datasets = TextField(null=True)  # JSON格式存储偏好数据集
    last_rate_limit = DateTimeField(null=True)  # 最后一次遇到限流的时间

    class Meta:
        table_name = 'user_accounts'


class DatasetModel(BaseModel):
    """数据集模型"""
    name = CharField(max_length=100, unique=True, index=True)
    description = TextField(null=True)

    class Meta:
        table_name = 'datasets'

    def save(self, *args, **kwargs):
        # 数据集表不需要 updated_at 字段，只保留 created_at
        return Model.save(self, *args, **kwargs)


class AlphaFactorQueueModel(BaseModel):
    """因子队列模型 - 核心队列管理"""

    # 基础信息
    factor_expression = TextField()  # 因子表达式
    # 来源数据集 (analyst4, news18等)
    source_dataset = CharField(max_length=50, index=True)

    # 队列管理
    # 'stage1', 'stage2', 'stage3', 'check', 'submit'
    stage = CharField(max_length=20, index=True)
    # 'pending', 'processing', 'completed', 'failed', 'skipped'
    status = CharField(max_length=20, index=True)
    priority = IntegerField(default=0, index=True)  # 优先级 (0-100, 数值越大越优先)

    # 处理参数
    region = CharField(max_length=10, default='USA')  # 地区
    universe = CharField(max_length=20, default='TOP3000')  # 股票池
    delay = IntegerField(default=1)  # 延迟天数
    decay = IntegerField(default=6)  # 衰减参数
    neutralization = CharField(max_length=50, default='SUBINDUSTRY')  # 中性化

    # 标签和分类
    tags = TextField(null=True)  # JSON格式的标签列表
    batch_id = CharField(max_length=100, null=True, index=True)  # 批次ID

    # 错误处理
    retry_count = IntegerField(default=0)  # 重试次数
    max_retries = IntegerField(default=3)  # 最大重试次数
    last_error = TextField(null=True)  # 最后一次错误信息
    error_type = CharField(max_length=50, null=True)  # 错误类型分类

    # 处理结果
    alpha_id = CharField(max_length=100, null=True,
                         index=True)  # WQ平台返回的Alpha ID
    sharpe = FloatField(null=True)  # 夏普比率
    fitness = FloatField(null=True)  # 适应度
    turnover = FloatField(null=True)  # 换手率

    # 时间戳
    submitted_at = DateTimeField(null=True, index=True)  # 提交到队列时间
    started_at = DateTimeField(null=True)  # 开始处理时间
    completed_at = DateTimeField(null=True)  # 完成处理时间

    # 分配的账户
    assigned_account = ForeignKeyField(
        UserAccountModel, null=True, backref='assigned_factors')

    class Meta:
        table_name = 'alpha_factor_queue'
        indexes = (
            (('stage', 'status', 'priority'), False),  # 组合索引用于队列查询
            (('source_dataset', 'stage'), False),      # 数据集阶段索引
            (('batch_id', 'status'), False),           # 批次状态索引
        )


class AlphaProcessingLogModel(BaseModel):
    """因子处理日志模型 - 详细的处理记录"""

    factor_queue = ForeignKeyField(
        AlphaFactorQueueModel, backref='processing_logs')
    operation = CharField(max_length=50)  # 'simulate', 'check', 'submit'
    status = CharField(max_length=20)     # 'started', 'completed', 'failed'

    # 处理详情
    worker_id = CharField(max_length=100, null=True)   # 处理器标识
    session_id = CharField(max_length=100, null=True)  # 会话ID

    # 性能指标
    duration = FloatField(null=True)        # 处理耗时(秒)
    cpu_usage = FloatField(null=True)       # CPU使用率
    memory_usage = FloatField(null=True)    # 内存使用量

    # 错误信息
    error_message = TextField(null=True)    # 错误详情
    stack_trace = TextField(null=True)      # 堆栈信息

    # 请求响应
    request_data = TextField(null=True)     # 请求数据(JSON)
    response_data = TextField(null=True)    # 响应数据(JSON)

    class Meta:
        table_name = 'alpha_processing_log'
        indexes = (
            (('factor_queue', 'status'), False),
            (('operation', 'created_at'), False),
        )


class AlphaBatchModel(BaseModel):
    """批次管理模型 - 批量处理管理"""

    batch_id = CharField(max_length=100, unique=True, index=True)
    batch_name = CharField(max_length=200)  # 批次描述名称

    # 批次配置
    source_dataset = CharField(max_length=50)  # 来源数据集
    stage = CharField(max_length=20)           # 目标阶段
    total_factors = IntegerField(default=0)    # 总因子数量

    # 进度统计
    pending_count = IntegerField(default=0)    # 待处理数量
    processing_count = IntegerField(default=0)  # 处理中数量
    completed_count = IntegerField(default=0)  # 已完成数量
    failed_count = IntegerField(default=0)     # 失败数量

    # 时间管理
    started_at = DateTimeField(null=True)      # 批次开始时间
    estimated_completion = DateTimeField(null=True)  # 预计完成时间
    completed_at = DateTimeField(null=True)    # 实际完成时间

    # 状态管理
    # 'pending', 'running', 'completed', 'failed'
    status = CharField(max_length=20, default='pending')

    class Meta:
        table_name = 'alpha_batch'
        indexes = (
            (('source_dataset', 'stage', 'status'), False),
            (('status', 'started_at'), False),
        )


class SystemStatsModel(BaseModel):
    """系统统计模型 - 性能监控数据"""

    # 统计分类
    # 'hourly', 'daily', 'worker_performance'
    stat_type = CharField(max_length=50, index=True)
    stat_key = CharField(max_length=100, index=True)    # 具体指标键
    stat_value = TextField()                            # JSON格式的统计值

    # 时间维度
    period_start = DateTimeField(index=True)            # 统计周期开始
    period_end = DateTimeField(index=True)              # 统计周期结束

    # 元数据
    metadata = TextField(null=True)                     # 额外的元数据信息

    class Meta:
        table_name = 'system_stats'
        indexes = (
            (('stat_type', 'period_start'), False),     # 时间序列查询
            (('stat_key', 'period_start'), False),      # 指标时间查询
        )


class AccountExecutionLogModel(BaseModel):
    """账户执行记录模型 - 用于参数调优"""

    account = ForeignKeyField(UserAccountModel, backref='execution_logs')
    date = DateField(index=True)
    dataset_name = CharField(max_length=100, index=True)
    stage = CharField(max_length=20)

    # 执行统计
    factors_processed = IntegerField(default=0)
    factors_submitted = IntegerField(default=0)
    success_rate = FloatField(default=0.0)
    avg_response_time = FloatField(default=0.0)
    error_count = IntegerField(default=0)

    # 参数配置和性能
    parameters_config = TextField()  # JSON存储参数配置
    performance_score = FloatField(default=0.0)

    class Meta:
        table_name = 'account_execution_log'
        indexes = (
            (('account', 'date'), False),
            (('dataset_name', 'date'), False),
        )


class ParameterOptimizationModel(BaseModel):
    """参数优化记录模型"""

    account = ForeignKeyField(UserAccountModel, backref='optimization_records')
    parameter_set = TextField()  # JSON存储参数组合

    # 测试周期
    test_period_start = DateTimeField()
    test_period_end = DateTimeField()

    # 测试结果
    total_factors = IntegerField()
    success_factors = IntegerField()
    optimization_score = FloatField()  # 综合优化分数
    is_optimal = BooleanField(default=False)  # 是否为最优参数集

    notes = TextField(null=True)  # 备注信息

    class Meta:
        table_name = 'parameter_optimization'
        indexes = (
            (('account', 'is_optimal'), False),
            (('optimization_score', 'test_period_end'), False),
        )


# 保留原有模型
class AccountDatasetUsageModel(BaseModel):
    """账户使用数据集记录模型"""
    account = ForeignKeyField(UserAccountModel, backref='dataset_usage')
    dataset = ForeignKeyField(DatasetModel, backref='usage_records')
    start_time = DateTimeField(index=True)
    end_time = DateTimeField(null=True, index=True)
    success_factor_count = IntegerField(default=0)

    class Meta:
        table_name = 'account_dataset_usage'
        indexes = (
            (('account', 'dataset'), False),
            (('start_time', 'end_time'), False),
        )


class AccountAlphaFactorModel(BaseModel):
    """账户因子Alpha模型"""
    account_name = CharField(max_length=100, index=True)
    date = DateField(index=True)
    dataset_name = CharField(max_length=100, index=True)
    success_alpha_id = CharField(max_length=100, index=True)

    class Meta:
        table_name = 'account_alpha_factors'
        indexes = (
            (('account_name', 'date', 'dataset_name'), False),
        )

    def save(self, *args, **kwargs):
        # Alpha因子表不需要 updated_at 字段，只保留 created_at
        return Model.save(self, *args, **kwargs)


class AlphaDiggingProcessModel(BaseModel):
    """因子挖掘过程模型"""
    account = ForeignKeyField(
        UserAccountModel, backref='alpha_digging_records', index=True)
    alpha_id = CharField(max_length=100, null=True, index=True)
    type = CharField(max_length=50, null=True)
    author = CharField(max_length=100, null=True)
    instrument_type = CharField(max_length=50, null=True)
    region = CharField(max_length=50, null=True, index=True)
    universe = CharField(max_length=100, null=True)
    delay = IntegerField(null=True)
    decay = FloatField(null=True)
    neutralization = CharField(max_length=100, null=True)
    truncation = FloatField(null=True)
    pasteurization = CharField(max_length=100, null=True)
    unit_handling = CharField(max_length=100, null=True)
    nan_handling = CharField(max_length=100, null=True)
    language = CharField(max_length=50, null=True)
    visualization = CharField(max_length=100, null=True)
    code = TextField(null=True)
    description = TextField(null=True)
    operator_count = IntegerField(null=True)
    date_created = DateTimeField(null=True, index=True)
    date_submitted = DateTimeField(null=True)
    date_modified = DateTimeField(null=True)
    name = CharField(max_length=200, null=True)
    favorite = BooleanField(default=False)
    hidden = BooleanField(default=False)
    color = CharField(max_length=50, null=True)
    category = CharField(max_length=100, null=True)
    tags = TextField(null=True)
    classifications = CharField(max_length=200, null=True)
    grade = CharField(max_length=50, null=True)
    stage = CharField(max_length=50, null=True)
    status = CharField(max_length=50, null=True)
    pnl = FloatField(null=True)
    book_size = FloatField(null=True)
    long_count = IntegerField(null=True)
    short_count = IntegerField(null=True)
    turnover = FloatField(null=True)
    returns = FloatField(null=True)
    drawdown = FloatField(null=True)
    margin = FloatField(null=True)
    fitness = FloatField(null=True)
    sharpe = FloatField(null=True)
    start_date = DateField(null=True)
    checks = CharField(max_length=200, null=True)
    os = CharField(max_length=100, null=True)
    train = FloatField(null=True)
    test = FloatField(null=True)
    prod = FloatField(null=True)
    competitions = CharField(max_length=200, null=True)
    themes = CharField(max_length=200, null=True)
    team = CharField(max_length=100, null=True)
    pyramids = IntegerField(null=True)
    self_corr = FloatField(null=True)

    # 标签字段
    tag1 = CharField(max_length=100, null=True)
    tag2 = CharField(max_length=100, null=True)
    tag3 = CharField(max_length=100, null=True)
    tag4 = CharField(max_length=100, null=True)
    tag5 = CharField(max_length=100, null=True)
    tag6 = CharField(max_length=100, null=True)
    tag7 = CharField(max_length=100, null=True)
    tag8 = CharField(max_length=100, null=True)
    tag9 = CharField(max_length=100, null=True)
    tag10 = CharField(max_length=100, null=True)

    # 挖掘过程相关字段
    digging_time = DateTimeField(index=True)
    digging_status = CharField(
        max_length=50, default='in_progress', index=True)

    class Meta:
        table_name = 'alpha_digging_process'
        indexes = (
            (('account', 'digging_status'), False),
            (('digging_status', 'digging_time'), False),
        )


class RegionModel(BaseModel):
    """地区配置模型"""
    region_code = CharField(max_length=10)
    region_name = CharField(max_length=100)
    instrument_type = CharField(max_length=50)
    description = TextField(null=True)

    class Meta:
        table_name = 'regions'


class UrlConfigModel(BaseModel):
    """URL配置模型"""
    url_key = CharField(max_length=100, unique=True, index=True)
    url_path = CharField(max_length=500)
    description = TextField(null=True)
    is_active = BooleanField(default=True, index=True)

    class Meta:
        table_name = 'url_config'


# 所有模型的列表，用于批量操作
ALL_MODELS = [
    UserAccountModel,
    DatasetModel,
    AlphaFactorQueueModel,           # 新增核心队列模型
    AlphaProcessingLogModel,         # 新增处理日志模型
    AlphaBatchModel,                 # 新增批次管理模型
    SystemStatsModel,                # 新增系统统计模型
    AccountExecutionLogModel,        # 新增账户执行记录模型
    ParameterOptimizationModel,      # 新增参数优化模型
    AccountDatasetUsageModel,        # 保留原有模型
    AccountAlphaFactorModel,         # 保留原有模型
    AlphaDiggingProcessModel,        # 保留原有模型
    RegionModel,                     # 保留原有模型
    UrlConfigModel                   # 保留原有模型
]
