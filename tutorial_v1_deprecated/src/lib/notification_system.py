"""
事件驱动通知系统 - 多渠道消息推送
支持邮件、webhook、文件等多种通知渠道，事件过滤、批量通知和消息模板
"""

import asyncio
import smtplib
import aiohttp
import json
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from pathlib import Path
import ssl
import jinja2
from collections import deque, defaultdict
from .logger import get_logger
from .db_config_reader import get_config_reader

logger = get_logger(__name__)


class NotificationLevel(Enum):
    """通知级别"""
    DEBUG = "debug"
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


class ChannelType(Enum):
    """通知渠道类型"""
    EMAIL = "email"
    WEBHOOK = "webhook"
    FILE = "file"
    CONSOLE = "console"


@dataclass
class NotificationEvent:
    """通知事件"""
    event_id: str
    event_type: str
    level: NotificationLevel
    title: str
    message: str
    timestamp: datetime = field(default_factory=datetime.now)
    source: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    retry_count: int = 0


@dataclass
class NotificationChannel:
    """通知渠道"""
    channel_id: str
    channel_type: ChannelType
    enabled: bool = True
    config: Dict[str, Any] = field(default_factory=dict)
    filters: List[Callable[[NotificationEvent], bool]
                  ] = field(default_factory=list)

    # 统计信息
    total_sent: int = 0
    successful_sent: int = 0
    failed_sent: int = 0
    last_sent_time: Optional[datetime] = None
    last_error: Optional[str] = None


class NotificationSystem:
    """通知系统主类"""

    def __init__(self, db_path: Optional[str] = None):
        self.config_reader = get_config_reader(db_path)
        self.channels: Dict[str, NotificationChannel] = {}
        self.event_queue: asyncio.Queue = asyncio.Queue()
        self.batch_events: Dict[str,
                                List[NotificationEvent]] = defaultdict(list)

        # 消息模板引擎
        self.template_env = jinja2.Environment(
            loader=jinja2.DictLoader({}),
            autoescape=jinja2.select_autoescape(['html', 'xml'])
        )

        # 批量通知配置 - 使用默认值，因为数据库配置侧重于URL和认证
        self.batch_interval = 30 * 60  # 30分钟
        self.batch_threshold = 5  # 最少5个事件触发批量通知

        # 运行状态
        self._running = False
        self._stop_event = asyncio.Event()

        # 统计信息
        self.stats = {
            'total_events_processed': 0,
            'total_notifications_sent': 0,
            'total_failures': 0,
            'batch_notifications_sent': 0,
            'start_time': datetime.now()
        }

        # 加载配置
        self._load_notification_config()
        self._load_message_templates()

    def _load_notification_config(self):
        """加载通知配置"""
        notification_config = self.config.get_section('notification')

        if not notification_config.get('enabled', True):
            logger.info("通知系统已禁用")
            return

        channels_config = notification_config.get('channels', {})

        # 加载邮件渠道
        if channels_config.get('email', {}).get('enabled', False):
            self._setup_email_channel(channels_config['email'])

        # 加载Webhook渠道
        if channels_config.get('webhook', {}).get('enabled', False):
            self._setup_webhook_channel(channels_config['webhook'])

        # 加载文件渠道
        if channels_config.get('file', {}).get('enabled', True):
            self._setup_file_channel(channels_config['file'])

        logger.info(f"通知系统初始化完成，加载了 {len(self.channels)} 个渠道")

    def _setup_email_channel(self, email_config: Dict[str, Any]):
        """设置邮件通知渠道"""
        channel = NotificationChannel(
            channel_id="email_default",
            channel_type=ChannelType.EMAIL,
            enabled=True,
            config=email_config
        )

        # 添加邮件特定过滤器
        channel.filters.append(
            lambda event: event.level.value in ['warning', 'error', 'critical']
        )

        self.channels[channel.channel_id] = channel
        logger.info("邮件通知渠道已设置")

    def _setup_webhook_channel(self, webhook_config: Dict[str, Any]):
        """设置Webhook通知渠道"""
        urls = webhook_config.get('urls', [])

        for i, url in enumerate(urls):
            channel = NotificationChannel(
                channel_id=f"webhook_{i}",
                channel_type=ChannelType.WEBHOOK,
                enabled=True,
                config={
                    'url': url,
                    'timeout': webhook_config.get('timeout_seconds', 10),
                    'headers': webhook_config.get('headers', {})
                }
            )

            self.channels[channel.channel_id] = channel

        logger.info(f"Webhook通知渠道已设置: {len(urls)} 个")

    def _setup_file_channel(self, file_config: Dict[str, Any]):
        """设置文件通知渠道"""
        log_directory = file_config.get('log_directory', 'logs/notifications')

        # 确保目录存在
        Path(log_directory).mkdir(parents=True, exist_ok=True)

        channel = NotificationChannel(
            channel_id="file_default",
            channel_type=ChannelType.FILE,
            enabled=True,
            config={
                'log_directory': log_directory,
                'file_pattern': 'notifications_%Y%m%d.log'
            }
        )

        self.channels[channel.channel_id] = channel
        logger.info("文件通知渠道已设置")

    def _load_message_templates(self):
        """加载消息模板"""
        templates = {
            'default': """
时间: {{ event.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
事件类型: {{ event.event_type }}
级别: {{ event.level.value.upper() }}
标题: {{ event.title }}
消息: {{ event.message }}
{% if event.source %}来源: {{ event.source }}{% endif %}
{% if event.tags %}标签: {{ event.tags | join(', ') }}{% endif %}
""",

            'factor_generation_complete': """
🎉 因子生成完成通知

批次信息:
- 时间: {{ event.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
- 生成因子数量: {{ event.metadata.get('factor_count', 'N/A') }}
- 成功率: {{ event.metadata.get('success_rate', 'N/A') }}%
- 耗时: {{ event.metadata.get('duration_minutes', 'N/A') }} 分钟

详细信息: {{ event.message }}
""",

            'quality_check_failed': """
⚠️ 质量检查失败通知

检查详情:
- 时间: {{ event.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
- Alpha ID: {{ event.metadata.get('alpha_id', 'N/A') }}
- 失败原因: {{ event.message }}
- 检查类型: {{ event.metadata.get('check_type', 'N/A') }}

建议措施: {{ event.metadata.get('recommendation', '请检查因子质量参数') }}
""",

            'submission_batch_complete': """
📋 批量提交完成通知

提交结果:
- 时间: {{ event.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
- 总提交数量: {{ event.metadata.get('total_count', 'N/A') }}
- 成功提交: {{ event.metadata.get('success_count', 'N/A') }}
- 失败提交: {{ event.metadata.get('failed_count', 'N/A') }}
- 成功率: {{ event.metadata.get('success_rate', 'N/A') }}%

处理详情: {{ event.message }}
""",

            'system_error': """
🚨 系统错误通知

错误信息:
- 时间: {{ event.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}
- 错误级别: {{ event.level.value.upper() }}
- 错误来源: {{ event.source or 'Unknown' }}
- 错误描述: {{ event.message }}

{% if event.metadata.get('stack_trace') %}
错误堆栈:
{{ event.metadata.get('stack_trace') }}
{% endif %}

建议: 请立即检查系统状态并采取相应措施。
""",

            'batch_summary': """
📊 批量通知摘要 ({{ batch_start.strftime('%H:%M') }} - {{ batch_end.strftime('%H:%M') }})

事件统计:
{% for event_type, count in event_counts.items() %}
- {{ event_type }}: {{ count }} 次
{% endfor %}

级别分布:
{% for level, count in level_counts.items() %}
- {{ level.upper() }}: {{ count }} 次
{% endfor %}

详细事件列表:
{% for event in events %}
[{{ event.timestamp.strftime('%H:%M:%S') }}] {{ event.level.value.upper() }} - {{ event.title }}
{% endfor %}
"""
        }

        # 添加模板到Jinja2环境
        for name, template in templates.items():
            self.template_env.get_or_select_template(name)
            self.template_env.loader.mapping[name] = template

    async def start(self):
        """启动通知系统"""
        if self._running:
            logger.warning("通知系统已经在运行")
            return

        self._running = True
        logger.info("启动通知系统")

        # 启动事件处理循环
        asyncio.create_task(self._event_processing_loop())

        # 启动批量通知循环
        if self.batch_config.get('enabled', True):
            asyncio.create_task(self._batch_notification_loop())

    async def stop(self):
        """停止通知系统"""
        logger.info("停止通知系统")
        self._running = False
        self._stop_event.set()

        # 发送剩余的批量通知
        await self._send_pending_batch_notifications()

    async def send_notification(self, event: NotificationEvent) -> bool:
        """发送通知"""
        try:
            await self.event_queue.put(event)
            return True
        except Exception as e:
            logger.error(f"添加通知事件失败: {e}")
            return False

    async def send_event(self,
                         event_type: str,
                         title: str,
                         message: str,
                         level: NotificationLevel = NotificationLevel.INFO,
                         source: Optional[str] = None,
                         tags: Optional[List[str]] = None,
                         metadata: Optional[Dict[str, Any]] = None) -> bool:
        """发送事件通知（便捷方法）"""
        event = NotificationEvent(
            event_id=f"{event_type}_{datetime.now().timestamp()}",
            event_type=event_type,
            level=level,
            title=title,
            message=message,
            source=source,
            tags=tags or [],
            metadata=metadata or {}
        )

        return await self.send_notification(event)

    async def _event_processing_loop(self):
        """事件处理循环"""
        while self._running and not self._stop_event.is_set():
            try:
                # 获取事件（带超时）
                try:
                    event = await asyncio.wait_for(self.event_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue

                # 处理事件
                await self._process_event(event)
                self.stats['total_events_processed'] += 1

            except Exception as e:
                logger.error(f"事件处理循环异常: {e}")
                await asyncio.sleep(1)

    async def _process_event(self, event: NotificationEvent):
        """处理单个事件"""
        # 检查是否启用了事件类型
        enabled_events = self.config.get_section('notification.events')
        if not enabled_events.get(event.event_type, True):
            logger.debug(f"事件类型已禁用: {event.event_type}")
            return

        # 判断是否加入批量通知
        if self._should_batch_event(event):
            self.batch_events[event.event_type].append(event)
            logger.debug(f"事件加入批量队列: {event.event_type}")
            return

        # 立即发送通知
        await self._send_immediate_notification(event)

    def _should_batch_event(self, event: NotificationEvent) -> bool:
        """判断事件是否应该批量处理"""
        if not self.batch_config.get('enabled', True):
            return False

        # 高优先级事件立即发送
        if event.level in [NotificationLevel.ERROR, NotificationLevel.CRITICAL]:
            return False

        # 特定事件类型可以批量处理
        batchable_events = [
            'factor_generation_complete',
            'quality_check_passed',
            'ml_model_updated'
        ]

        return event.event_type in batchable_events

    async def _send_immediate_notification(self, event: NotificationEvent):
        """立即发送通知"""
        for channel in self.channels.values():
            if not channel.enabled:
                continue

            # 应用过滤器
            if not all(filter_func(event) for filter_func in channel.filters):
                continue

            try:
                await self._send_to_channel(event, channel)
                channel.successful_sent += 1
                channel.last_sent_time = datetime.now()

            except Exception as e:
                channel.failed_sent += 1
                channel.last_error = str(e)
                logger.error(f"发送通知到渠道 {channel.channel_id} 失败: {e}")

            finally:
                channel.total_sent += 1

        self.stats['total_notifications_sent'] += 1

    async def _send_to_channel(self, event: NotificationEvent, channel: NotificationChannel):
        """发送通知到指定渠道"""
        if channel.channel_type == ChannelType.EMAIL:
            await self._send_email_notification(event, channel)

        elif channel.channel_type == ChannelType.WEBHOOK:
            await self._send_webhook_notification(event, channel)

        elif channel.channel_type == ChannelType.FILE:
            await self._send_file_notification(event, channel)

        elif channel.channel_type == ChannelType.CONSOLE:
            await self._send_console_notification(event, channel)

        else:
            logger.warning(f"未知渠道类型: {channel.channel_type}")

    async def _send_email_notification(self, event: NotificationEvent, channel: NotificationChannel):
        """发送邮件通知"""
        config = channel.config

        # 格式化消息
        message_content = self._format_message(event)

        # 创建邮件
        msg = MIMEMultipart()
        msg['From'] = config['name']
        msg['To'] = ', '.join(config.get('recipients', []))
        msg['Subject'] = f"[WQ通知] {event.title}"

        msg.attach(MIMEText(message_content, 'plain', 'utf-8'))

        # 发送邮件
        context = ssl.create_default_context()

        with smtplib.SMTP(config['smtp_server'], config['smtp_port']) as server:
            server.starttls(context=context)
            server.login(config['email'], config['password'])
            server.send_message(msg)

        logger.debug(f"邮件通知已发送: {event.title}")

    async def _send_webhook_notification(self, event: NotificationEvent, channel: NotificationChannel):
        """发送Webhook通知"""
        config = channel.config

        payload = {
            'event_id': event.event_id,
            'event_type': event.event_type,
            'level': event.level.value,
            'title': event.title,
            'message': event.message,
            'timestamp': event.timestamp.isoformat(),
            'source': event.source,
            'tags': event.tags,
            'metadata': event.metadata
        }

        headers = {
            'Content-Type': 'application/json',
            **config.get('headers', {})
        }

        timeout = aiohttp.ClientTimeout(total=config.get('timeout', 10))

        async with aiohttp.ClientSession(timeout=timeout) as session:
            async with session.post(
                config['url'],
                json=payload,
                headers=headers
            ) as response:
                if response.status >= 400:
                    raise aiohttp.ClientError(
                        f"Webhook返回错误状态: {response.status}")

        logger.debug(f"Webhook通知已发送: {event.title}")

    async def _send_file_notification(self, event: NotificationEvent, channel: NotificationChannel):
        """发送文件通知"""
        config = channel.config

        # 生成文件路径
        log_directory = Path(config['log_directory'])
        file_pattern = config['file_pattern']
        file_name = datetime.now().strftime(file_pattern)
        file_path = log_directory / file_name

        # 格式化消息
        message_content = self._format_message(event)
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        # 写入文件
        with open(file_path, 'a', encoding='utf-8') as f:
            f.write(f"[{timestamp}] {message_content}\n\n")

        logger.debug(f"文件通知已记录: {file_path}")

    async def _send_console_notification(self, event: NotificationEvent, channel: NotificationChannel):
        """发送控制台通知"""
        message_content = self._format_message(event)
        print(f"[NOTIFICATION] {message_content}")

    def _format_message(self, event: NotificationEvent) -> str:
        """格式化消息内容"""
        try:
            # 尝试使用特定模板
            template_name = event.event_type
            if template_name in self.template_env.loader.mapping:
                template = self.template_env.get_template(template_name)
                return template.render(event=event)

            # 使用默认模板
            template = self.template_env.get_template('default')
            return template.render(event=event)

        except Exception as e:
            logger.warning(f"模板渲染失败: {e}, 使用简单格式")
            return f"[{event.level.value.upper()}] {event.title}: {event.message}"

    async def _batch_notification_loop(self):
        """批量通知循环"""
        while self._running and not self._stop_event.is_set():
            try:
                await asyncio.sleep(self.batch_interval)
                await self._send_pending_batch_notifications()
            except Exception as e:
                logger.error(f"批量通知循环异常: {e}")

    async def _send_pending_batch_notifications(self):
        """发送待处理的批量通知"""
        if not self.batch_events:
            return

        # 获取当前批量事件
        current_batch = dict(self.batch_events)
        self.batch_events.clear()

        for event_type, events in current_batch.items():
            if len(events) < self.batch_threshold:
                # 事件数量不足，放回队列
                self.batch_events[event_type].extend(events)
                continue

            # 创建批量通知事件
            batch_event = self._create_batch_event(event_type, events)
            await self._send_immediate_notification(batch_event)

            self.stats['batch_notifications_sent'] += 1
            logger.info(f"批量通知已发送: {event_type}, 包含 {len(events)} 个事件")

    def _create_batch_event(self, event_type: str, events: List[NotificationEvent]) -> NotificationEvent:
        """创建批量事件"""
        if not events:
            raise ValueError("事件列表不能为空")

        # 统计信息
        event_counts = defaultdict(int)
        level_counts = defaultdict(int)

        for event in events:
            event_counts[event.event_type] += 1
            level_counts[event.level] += 1

        # 时间范围
        batch_start = min(event.timestamp for event in events)
        batch_end = max(event.timestamp for event in events)

        # 创建批量事件
        batch_event = NotificationEvent(
            event_id=f"batch_{event_type}_{datetime.now().timestamp()}",
            event_type=f"batch_{event_type}",
            level=NotificationLevel.INFO,
            title=f"批量通知: {event_type} ({len(events)} 个事件)",
            message=f"时间范围: {batch_start.strftime('%H:%M')} - {batch_end.strftime('%H:%M')}",
            metadata={
                'batch_start': batch_start,
                'batch_end': batch_end,
                'events': events,
                'event_counts': dict(event_counts),
                'level_counts': dict(level_counts),
                'total_events': len(events)
            }
        )

        return batch_event

    def add_channel_filter(self, channel_id: str, filter_func: Callable[[NotificationEvent], bool]):
        """为渠道添加过滤器"""
        if channel_id in self.channels:
            self.channels[channel_id].filters.append(filter_func)
            logger.info(f"过滤器已添加到渠道: {channel_id}")
        else:
            logger.warning(f"渠道不存在: {channel_id}")

    def get_system_stats(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        return {
            'running': self._running,
            'uptime_seconds': (datetime.now() - self.stats['start_time']).total_seconds(),
            'total_events_processed': self.stats['total_events_processed'],
            'total_notifications_sent': self.stats['total_notifications_sent'],
            'batch_notifications_sent': self.stats['batch_notifications_sent'],
            'total_failures': self.stats['total_failures'],
            'active_channels': len([c for c in self.channels.values() if c.enabled]),
            'total_channels': len(self.channels),
            'queue_size': self.event_queue.qsize(),
            'pending_batch_events': sum(len(events) for events in self.batch_events.values())
        }

    def get_channel_stats(self) -> Dict[str, Any]:
        """获取渠道统计信息"""
        channel_stats = {}

        for channel_id, channel in self.channels.items():
            success_rate = (channel.successful_sent / channel.total_sent * 100
                            if channel.total_sent > 0 else 0)

            channel_stats[channel_id] = {
                'type': channel.channel_type.value,
                'enabled': channel.enabled,
                'total_sent': channel.total_sent,
                'successful_sent': channel.successful_sent,
                'failed_sent': channel.failed_sent,
                'success_rate': round(success_rate, 2),
                'last_sent_time': (channel.last_sent_time.isoformat()
                                   if channel.last_sent_time else None),
                'last_error': channel.last_error
            }

        return channel_stats

    async def test_channel(self, channel_id: str) -> Dict[str, Any]:
        """测试通知渠道"""
        if channel_id not in self.channels:
            return {'success': False, 'error': f'渠道不存在: {channel_id}'}

        channel = self.channels[channel_id]

        # 创建测试事件
        test_event = NotificationEvent(
            event_id=f"test_{datetime.now().timestamp()}",
            event_type="system_test",
            level=NotificationLevel.INFO,
            title="通知渠道测试",
            message=f"这是对通知渠道 {channel_id} 的测试消息",
            source="notification_system",
            tags=["test"]
        )

        try:
            await self._send_to_channel(test_event, channel)
            return {
                'success': True,
                'message': f'渠道 {channel_id} 测试成功',
                'channel_type': channel.channel_type.value
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'channel_type': channel.channel_type.value
            }


# 便捷的全局通知函数
_global_notification_system: Optional[NotificationSystem] = None


async def get_notification_system() -> NotificationSystem:
    """获取全局通知系统实例"""
    global _global_notification_system

    if _global_notification_system is None:
        _global_notification_system = NotificationSystem()
        await _global_notification_system.start()

    return _global_notification_system


async def notify(event_type: str, title: str, message: str,
                 level: NotificationLevel = NotificationLevel.INFO,
                 source: Optional[str] = None,
                 tags: Optional[List[str]] = None,
                 metadata: Optional[Dict[str, Any]] = None) -> bool:
    """全局通知函数"""
    notification_system = await get_notification_system()
    return await notification_system.send_event(
        event_type=event_type,
        title=title,
        message=message,
        level=level,
        source=source,
        tags=tags,
        metadata=metadata
    )
