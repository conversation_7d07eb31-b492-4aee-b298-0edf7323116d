"""
统一API客户端 - 整合所有API调用逻辑
支持重试、熔断、会话管理和错误处理
"""

from typing import List, Dict, Any, Optional, Union, Callable
import asyncio
import aiohttp
import time
import json
from datetime import datetime, timedelta
from dataclasses import dataclass
from enum import Enum
import backoff
from .simple_logger import get_logger

logger = get_logger(__name__)


class APIStatus(Enum):
    """API状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    CIRCUIT_OPEN = "circuit_open"
    MAINTENANCE = "maintenance"


@dataclass
class CircuitBreakerState:
    """熔断器状态"""
    failure_count: int = 0
    success_count: int = 0
    last_failure_time: Optional[datetime] = None
    state: APIStatus = APIStatus.HEALTHY
    failure_threshold: int = 5
    recovery_timeout: int = 60  # 秒
    half_open_max_calls: int = 3


@dataclass
class RetryConfig:
    """重试配置"""
    max_attempts: int = 3
    base_delay: float = 1.0
    max_delay: float = 60.0
    exponential_base: float = 2.0
    jitter: bool = True


class RobustAPIClient:
    """强健的API客户端 - 统一处理所有API调用"""

    def __init__(self,
                 base_url: str = "https://api.worldquantbrain.com",
                 email: Optional[str] = None,
                 password: Optional[str] = None):
        self.base_url = base_url
        self.email = email
        self.password = password

        # 会话管理
        self.session: Optional[aiohttp.ClientSession] = None
        self.auth_token: Optional[str] = None
        self.session_expires_at: Optional[datetime] = None
        self.session_duration = timedelta(hours=3)

        # 熔断器
        self.circuit_breaker = CircuitBreakerState()

        # 重试配置
        self.retry_config = RetryConfig()

        # 请求统计
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'circuit_breaker_trips': 0,
            'average_response_time': 0.0,
            'last_request_time': None
        }

        # API端点配置
        self.endpoints = {
            'authentication': '/authentication',
            'alphas': '/alphas',
            'operators': '/operators',
            'datafields': '/datafields',
            'simulate': '/simulate',
            'correlations': '/alphas/{alpha_id}/correlations'
        }

    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self.initialize()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器出口"""
        await self.close()

    async def initialize(self):
        """初始化客户端"""
        await self._create_session()
        if self.email and self.password:
            await self.authenticate()
        logger.info("API客户端初始化完成")

    async def close(self):
        """关闭客户端"""
        if self.session:
            await self.session.close()
            self.session = None
        logger.info("API客户端已关闭")

    async def _create_session(self):
        """创建HTTP会话"""
        timeout = aiohttp.ClientTimeout(total=300, connect=30)
        connector = aiohttp.TCPConnector(
            limit=100,
            limit_per_host=30,
            ttl_dns_cache=300,
            use_dns_cache=True
        )

        self.session = aiohttp.ClientSession(
            timeout=timeout,
            connector=connector,
            headers={
                'User-Agent': 'WQ-FactorMining-Client/1.0',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        )

    async def authenticate(self) -> bool:
        """用户认证"""
        if not self.email or not self.password:
            raise ValueError("邮箱和密码不能为空")

        try:
            logger.info("开始用户认证...")

            # 设置认证信息
            auth = aiohttp.BasicAuth(self.email, self.password)

            response = await self._make_request(
                'POST',
                self.endpoints['authentication'],
                auth=auth,
                skip_auth_check=True
            )

            if response['success']:
                self.session_expires_at = datetime.now() + self.session_duration
                logger.info("用户认证成功")
                return True
            else:
                logger.error(f"认证失败: {response.get('message', '未知错误')}")
                return False

        except Exception as e:
            logger.error(f"认证过程异常: {e}")
            return False

    async def _make_request(self,
                            method: str,
                            endpoint: str,
                            data: Optional[Dict] = None,
                            params: Optional[Dict] = None,
                            auth: Optional[aiohttp.BasicAuth] = None,
                            skip_auth_check: bool = False,
                            **kwargs) -> Dict[str, Any]:
        """发起HTTP请求的核心方法"""

        # 检查熔断器状态
        if not self._check_circuit_breaker():
            return {
                'success': False,
                'error': 'circuit_breaker_open',
                'message': 'API熔断器开启，请稍后重试'
            }

        # 检查会话有效性
        if not skip_auth_check and not await self._ensure_valid_session():
            return {
                'success': False,
                'error': 'authentication_failed',
                'message': '认证失败或会话过期'
            }

        url = self.base_url + endpoint
        request_start = time.time()

        try:
            self.stats['total_requests'] += 1

            # 准备请求参数
            request_kwargs = {
                'url': url,
                'params': params,
                **kwargs
            }

            if auth:
                request_kwargs['auth'] = auth

            if data:
                request_kwargs['json'] = data

            # 发起请求
            async with self.session.request(method, **request_kwargs) as response:
                response_time = time.time() - request_start
                self._update_stats(response_time, True)

                # 处理响应
                if response.status == 200 or response.status == 201:
                    try:
                        result_data = await response.json()
                        self._record_success()

                        return {
                            'success': True,
                            'status_code': response.status,
                            'data': result_data,
                            'response_time': response_time
                        }
                    except Exception as e:
                        logger.warning(f"解析响应JSON失败: {e}")
                        text_data = await response.text()
                        self._record_success()

                        return {
                            'success': True,
                            'status_code': response.status,
                            'data': text_data,
                            'response_time': response_time
                        }

                elif response.status == 429:
                    # 处理限流
                    retry_after = response.headers.get('Retry-After', '60')
                    self._record_failure()

                    return {
                        'success': False,
                        'error': 'rate_limited',
                        'status_code': response.status,
                        'retry_after': int(retry_after),
                        'message': f'API限流，建议等待{retry_after}秒后重试'
                    }

                else:
                    # 其他错误状态
                    error_text = await response.text()
                    self._record_failure()

                    return {
                        'success': False,
                        'error': 'http_error',
                        'status_code': response.status,
                        'message': f'HTTP错误: {response.status}',
                        'details': error_text
                    }

        except asyncio.TimeoutError:
            self._record_failure()
            logger.warning(f"请求超时: {url}")

            return {
                'success': False,
                'error': 'timeout',
                'message': '请求超时'
            }

        except Exception as e:
            self._record_failure()
            logger.error(f"请求异常: {url}, 错误: {e}")

            return {
                'success': False,
                'error': 'request_exception',
                'message': f'请求异常: {str(e)}'
            }

    @backoff.on_exception(
        backoff.expo,
        (aiohttp.ClientError, asyncio.TimeoutError),
        max_tries=3,
        max_time=60
    )
    async def make_request_with_retry(self,
                                      method: str,
                                      endpoint: str,
                                      **kwargs) -> Dict[str, Any]:
        """带重试的请求方法"""
        return await self._make_request(method, endpoint, **kwargs)

    def _check_circuit_breaker(self) -> bool:
        """检查熔断器状态"""
        cb = self.circuit_breaker

        if cb.state == APIStatus.HEALTHY:
            return True

        elif cb.state == APIStatus.CIRCUIT_OPEN:
            # 检查是否可以尝试恢复
            if (cb.last_failure_time and
                    datetime.now() - cb.last_failure_time > timedelta(seconds=cb.recovery_timeout)):
                cb.state = APIStatus.DEGRADED
                cb.success_count = 0
                logger.info("熔断器进入半开状态，尝试恢复")
                return True
            return False

        elif cb.state == APIStatus.DEGRADED:
            # 半开状态，限制请求数量
            return cb.success_count < cb.half_open_max_calls

        return False

    def _record_success(self):
        """记录成功请求"""
        cb = self.circuit_breaker
        cb.success_count += 1

        if cb.state == APIStatus.DEGRADED:
            if cb.success_count >= cb.half_open_max_calls:
                cb.state = APIStatus.HEALTHY
                cb.failure_count = 0
                logger.info("熔断器恢复到健康状态")

        self.stats['successful_requests'] += 1

    def _record_failure(self):
        """记录失败请求"""
        cb = self.circuit_breaker
        cb.failure_count += 1
        cb.last_failure_time = datetime.now()

        if cb.failure_count >= cb.failure_threshold:
            cb.state = APIStatus.CIRCUIT_OPEN
            self.stats['circuit_breaker_trips'] += 1
            logger.warning(f"熔断器开启，失败次数: {cb.failure_count}")

        self.stats['failed_requests'] += 1

    def _update_stats(self, response_time: float, success: bool):
        """更新统计信息"""
        # 更新平均响应时间
        total_requests = self.stats['total_requests']
        current_avg = self.stats['average_response_time']
        self.stats['average_response_time'] = (
            current_avg * (total_requests - 1) + response_time) / total_requests
        self.stats['last_request_time'] = datetime.now()

    async def _ensure_valid_session(self) -> bool:
        """确保会话有效"""
        if not self.session:
            await self._create_session()

        # 检查会话是否过期
        if (self.session_expires_at and
                datetime.now() >= self.session_expires_at - timedelta(minutes=5)):
            logger.info("会话即将过期，重新认证...")
            return await self.authenticate()

        return True

    # === 业务API方法 ===

    async def get_datafields(self,
                             dataset_id: str,
                             region: str = 'USA',
                             universe: str = 'TOP3000',
                             delay: int = 1) -> Dict[str, Any]:
        """获取数据字段"""
        params = {
            'dataset_id': dataset_id,
            'region': region,
            'universe': universe,
            'delay': delay
        }

        result = await self.make_request_with_retry(
            'GET',
            self.endpoints['datafields'],
            params=params
        )

        if result['success']:
            logger.info(f"成功获取数据字段: {dataset_id}/{region}")

        return result

    async def simulate_alpha(self,
                             alpha_expression: str,
                             region: str = 'USA',
                             universe: str = 'TOP3000',
                             decay: int = 6,
                             delay: int = 1,
                             neutralization: str = 'SUBINDUSTRY') -> Dict[str, Any]:
        """模拟Alpha因子"""
        data = {
            'expression': alpha_expression,
            'region': region,
            'universe': universe,
            'decay': decay,
            'delay': delay,
            'neutralization': neutralization
        }

        result = await self.make_request_with_retry(
            'POST',
            self.endpoints['simulate'],
            data=data
        )

        if result['success']:
            logger.info(f"成功提交模拟: {alpha_expression[:50]}...")

        return result

    async def submit_alpha(self, alpha_id: str) -> Dict[str, Any]:
        """提交Alpha因子"""
        endpoint = f"{self.endpoints['alphas']}/{alpha_id}/submit"

        result = await self.make_request_with_retry(
            'POST',
            endpoint
        )

        if result['success']:
            logger.info(f"成功提交Alpha: {alpha_id}")

        return result

    async def get_alpha_info(self, alpha_id: str) -> Dict[str, Any]:
        """获取Alpha信息"""
        endpoint = f"{self.endpoints['alphas']}/{alpha_id}"

        result = await self.make_request_with_retry(
            'GET',
            endpoint
        )

        return result

    async def get_self_correlation(self, alpha_id: str) -> Dict[str, Any]:
        """获取自相关性数据"""
        endpoint = f"{self.endpoints['alphas']}/{alpha_id}/correlations/self"

        result = await self.make_request_with_retry(
            'GET',
            endpoint
        )

        return result

    async def get_production_correlation(self, alpha_id: str) -> Dict[str, Any]:
        """获取生产相关性数据"""
        endpoint = f"{self.endpoints['alphas']}/{alpha_id}/correlations/production"

        result = await self.make_request_with_retry(
            'GET',
            endpoint
        )

        return result

    async def get_available_operators(self) -> Dict[str, Any]:
        """获取可用操作符列表"""
        result = await self.make_request_with_retry(
            'GET',
            self.endpoints['operators']
        )

        return result

    async def batch_simulate_alphas(self,
                                    alpha_expressions: List[str],
                                    simulation_params: Dict[str, Any],
                                    max_concurrent: int = 5) -> List[Dict[str, Any]]:
        """批量模拟Alpha因子"""
        semaphore = asyncio.Semaphore(max_concurrent)

        async def simulate_single(expression):
            async with semaphore:
                return await self.simulate_alpha(expression, **simulation_params)

        tasks = [simulate_single(expr) for expr in alpha_expressions]
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理异常结果
        processed_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                processed_results.append({
                    'success': False,
                    'error': 'batch_exception',
                    'expression': alpha_expressions[i],
                    'message': str(result)
                })
            else:
                result['expression'] = alpha_expressions[i]
                processed_results.append(result)

        success_count = sum(
            1 for r in processed_results if r.get('success', False))
        logger.info(f"批量模拟完成: {success_count}/{len(alpha_expressions)} 成功")

        return processed_results

    # === 健康检查和监控 ===

    async def health_check(self) -> Dict[str, Any]:
        """API健康检查"""
        health_info = {
            'timestamp': datetime.now().isoformat(),
            'circuit_breaker_state': self.circuit_breaker.state.value,
            'session_valid': bool(self.session and not self._is_session_expired()),
            'statistics': self.stats.copy(),
            'health_score': 0.0
        }

        # 计算健康分数
        total_requests = self.stats['total_requests']
        if total_requests > 0:
            success_rate = self.stats['successful_requests'] / total_requests
            avg_response_time = self.stats['average_response_time']

            # 基础分数（成功率）
            health_score = success_rate * 70

            # 响应时间评分（响应时间越短分数越高）
            if avg_response_time < 1.0:
                health_score += 20
            elif avg_response_time < 3.0:
                health_score += 15
            elif avg_response_time < 5.0:
                health_score += 10
            else:
                health_score += 5

            # 熔断器状态评分
            if self.circuit_breaker.state == APIStatus.HEALTHY:
                health_score += 10
            elif self.circuit_breaker.state == APIStatus.DEGRADED:
                health_score += 5

            health_info['health_score'] = min(health_score, 100.0)

        return health_info

    def _is_session_expired(self) -> bool:
        """检查会话是否过期"""
        return (self.session_expires_at and
                datetime.now() >= self.session_expires_at)

    def get_performance_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        metrics = {
            'request_statistics': self.stats.copy(),
            'circuit_breaker_status': {
                'state': self.circuit_breaker.state.value,
                'failure_count': self.circuit_breaker.failure_count,
                'success_count': self.circuit_breaker.success_count,
                'last_failure_time': (self.circuit_breaker.last_failure_time.isoformat()
                                      if self.circuit_breaker.last_failure_time else None)
            },
            'session_info': {
                'is_valid': bool(self.session and not self._is_session_expired()),
                'expires_at': (self.session_expires_at.isoformat()
                               if self.session_expires_at else None)
            }
        }

        # 计算成功率
        total = self.stats['total_requests']
        if total > 0:
            metrics['success_rate'] = self.stats['successful_requests'] / total * 100
            metrics['failure_rate'] = self.stats['failed_requests'] / total * 100

        return metrics

    def reset_circuit_breaker(self):
        """重置熔断器"""
        self.circuit_breaker = CircuitBreakerState()
        logger.info("熔断器已重置")

    def update_retry_config(self, **kwargs):
        """更新重试配置"""
        for key, value in kwargs.items():
            if hasattr(self.retry_config, key):
                setattr(self.retry_config, key, value)
                logger.info(f"重试配置已更新: {key} = {value}")


# 工厂函数
async def create_api_client(email: str,
                            password: str,
                            base_url: str = "https://api.worldquantbrain.com") -> RobustAPIClient:
    """创建并初始化API客户端"""
    client = RobustAPIClient(
        base_url=base_url, email=email, password=password)
    await client.initialize()
    return client


# 全局客户端实例（单例模式）
_global_client: Optional[RobustAPIClient] = None


async def get_global_client() -> RobustAPIClient:
    """获取全局API客户端实例"""
    global _global_client

    if _global_client is None:
        # 从配置文件读取认证信息
        try:
            from ..config.config_reader import ConfigReader
            config_reader = ConfigReader()
            credentials = config_reader.get_credentials()

            _global_client = await create_api_client(
                credentials['email'],
                credentials['password']
            )
        except Exception as e:
            logger.error(f"创建全局API客户端失败: {e}")
            raise

    return _global_client


async def close_global_client():
    """关闭全局API客户端"""
    global _global_client

    if _global_client:
        await _global_client.close()
        _global_client = None
