"""
统一的因子挖掘调度器 - 支持传统和ML两种模式
"""

import asyncio
from typing import Dict, List, Optional
from datetime import datetime
from lib.logger import get_logger
from lib.models import AlphaFactorQueueModel, UserAccountModel
from lib.queue_manager import AlphaQueue

logger = get_logger(__name__)


class FactorDiggingScheduler:
    """统一的因子挖掘调度器 - 支持传统和ML两种模式"""

    def __init__(self, quiet: bool = False, ml_mode: str = 'traditional'):
        self.quiet = quiet
        self.logger = get_logger(__name__)
        self.ml_mode = ml_mode  # 'traditional', 'hybrid', 'full_ml'

        # 基础组件
        self.task_queue = AlphaQueue()

        # 根据模式选择Worker
        if ml_mode in ['hybrid', 'full_ml']:
            # ML增强的Worker组件
            from workers.ml_enhanced_step1_worker import MLEnhancedStep1Worker
            from workers.ml_enhanced_step2_worker import MLEnhancedStep2Worker
            from workers.ml_enhanced_step3_worker import MLEnhancedStep3Worker
            from workers.check_worker import CheckWorker
            from workers.submit_worker import SubmitWorker

            self.workers = {
                'step1': MLEnhancedStep1Worker("ML-Step1", quiet),
                'step2': MLEnhancedStep2Worker("ML-Step2", quiet),
                'step3': MLEnhancedStep3Worker("ML-Step3", quiet),
                'check': CheckWorker("Check", quiet),
                'submit': SubmitWorker("Submit", quiet)
            }

            # 传统Worker作为备选
            from workers.step1_worker import Step1Worker
            from workers.step2_worker import Step2Worker
            from workers.step3_worker import Step3Worker

            self.fallback_workers = {
                'step1': Step1Worker("Fallback-Step1", quiet),
                'step2': Step2Worker("Fallback-Step2", quiet),
                'step3': Step3Worker("Fallback-Step3", quiet)
            }
        else:
            # 纯传统模式
            from workers.step1_worker import Step1Worker
            from workers.step2_worker import Step2Worker
            from workers.step3_worker import Step3Worker
            from workers.check_worker import CheckWorker
            from workers.submit_worker import SubmitWorker

            self.workers = {
                'step1': Step1Worker("Step1", quiet),
                'step2': Step2Worker("Step2", quiet),
                'step3': Step3Worker("Step3", quiet),
                'check': CheckWorker("Check", quiet),
                'submit': SubmitWorker("Submit", quiet)
            }

    async def run_pipeline(self):
        """运行完整的挖掘流水线 - 自动选择最优模式"""

        self.logger.info(f"🚀 WQ因子挖掘系统启动 (模式: {self.ml_mode})")

        while True:
            try:
                # 智能模式决策（仅在hybrid模式下）
                if self.ml_mode == 'hybrid':
                    await self._adaptive_mode_selection()

                # 执行各阶段处理
                await self.schedule_step1_tasks()
                await self.schedule_step2_tasks()
                await self.schedule_step3_tasks()
                await self.schedule_check_tasks()
                await self.schedule_submit_tasks()

                # 更新统计数据
                await asyncio.sleep(60)  # 调度间隔

            except Exception as e:
                self.logger.error(f"调度器异常: {e}")
                await asyncio.sleep(30)

    async def _adaptive_mode_selection(self):
        """智能自适应模式选择（仅hybrid模式）"""

        # 评估ML vs 传统方法的性能
        # ml_performance = await self._evaluate_ml_performance()

        # 动态切换Worker（暂时保持ML模式）
        pass

    async def schedule_step1_tasks(self):
        """调度第一阶段任务"""
        pending_factors = self.task_queue.get_pending_factors(
            'step1', limit=100)
        if pending_factors:
            # accounts = self.account_scheduler.get_available_accounts()
            await self.workers['step1'].process_batch(pending_factors, [])

    async def schedule_step2_tasks(self):
        """调度第二阶段任务"""
        pending_factors = self.task_queue.get_pending_factors(
            'step2', limit=50)
        if pending_factors:
            await self.workers['step2'].process_batch(pending_factors, [])

    async def schedule_step3_tasks(self):
        """调度第三阶段任务"""
        pending_factors = self.task_queue.get_pending_factors(
            'step3', limit=30)
        if pending_factors:
            await self.workers['step3'].process_batch(pending_factors, [])

    async def schedule_check_tasks(self):
        """调度检查任务"""
        pending_factors = self.task_queue.get_pending_factors(
            'check', limit=20)
        if pending_factors:
            await self.workers['check'].process_batch(pending_factors, [])

    async def schedule_submit_tasks(self):
        """调度提交任务"""
        pending_factors = self.task_queue.get_pending_factors(
            'submit', limit=10)
        if pending_factors:
            await self.workers['submit'].process_batch(pending_factors, [])

    async def show_status(self):
        """显示当前状态"""
        self.logger.info(f"系统状态 - 模式: {self.ml_mode}")

        # 显示队列状态
        queue_stats = {}
        for stage in ['stage1', 'stage2', 'stage3', 'check', 'submit']:
            pending_count = await self._count_factors_by_status(stage, 'pending')
            completed_count = await self._count_factors_by_status(stage, 'completed')

            queue_stats[stage] = {
                'pending': pending_count,
                'completed': completed_count
            }

        if not self.quiet:
            from rich.console import Console
            from rich.table import Table

            console = Console()
            table = Table(title=f"🤖 WQ因子挖掘系统状态 ({self.ml_mode}模式)")
            table.add_column("阶段", style="cyan")
            table.add_column("待处理", style="yellow")
            table.add_column("已完成", style="green")

            for stage, stats in queue_stats.items():
                table.add_row(
                    stage.upper(),
                    str(stats['pending']),
                    str(stats['completed'])
                )

            console.print(table)

    async def run_monitor_mode(self):
        """监控模式"""
        self.logger.info("进入监控模式...")

        while True:
            await self.show_status()
            await asyncio.sleep(30)  # 30秒刷新一次

    async def _count_factors_by_status(self, stage: str, status: str) -> int:
        """统计特定阶段和状态的因子数量"""
        try:
            return (AlphaFactorQueueModel
                    .select()
                    .where(
                        AlphaFactorQueueModel.stage == stage,
                        AlphaFactorQueueModel.status == status
                    )
                    .count())
        except Exception as e:
            self.logger.error(f"统计因子数量失败: {e}")
            return 0
