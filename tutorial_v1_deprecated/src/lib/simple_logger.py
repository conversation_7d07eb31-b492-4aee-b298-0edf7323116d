"""
简化日志系统 - 集成rich组件提供美观的控制台输出
基于设计文档中的SimpleLogger实现
"""

import logging
import os
from datetime import datetime
from typing import Dict, Any, Optional
from rich.console import Console
from rich.table import Table
from rich.progress import Progress, TaskID
from rich import print as rich_print


class SimpleLogger:
    """简化的处理日志系统"""

    def __init__(self, name: str, quiet: bool = False):
        self.name = name
        self.quiet = quiet
        self.console = Console() if not quiet else None
        self.file_logger = self._setup_file_logger()
        self.stats = {'processed': 0, 'succeeded': 0, 'failed': 0}

    def _setup_file_logger(self) -> logging.Logger:
        """设置文件日志"""
        # 确保日志目录存在
        log_dir = "logs"
        if not os.path.exists(log_dir):
            os.makedirs(log_dir)

        logger = logging.getLogger(f"wq_{self.name}")
        logger.setLevel(logging.INFO)

        # 避免重复添加handler
        if not logger.handlers:
            handler = logging.FileHandler(
                f"{log_dir}/wq_{datetime.now().strftime('%Y%m%d')}.log",
                encoding='utf-8'
            )
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            logger.addHandler(handler)

        return logger

    def info(self, message: str, factor_id: int = None):
        """记录信息"""
        log_msg = f"[{self.name}] {message}"
        if factor_id:
            log_msg = f"[{self.name}] Factor #{factor_id}: {message}"

        self.file_logger.info(log_msg)

        if not self.quiet and self.console:
            if factor_id:
                self.console.print(
                    f"  📊 因子 #{factor_id}: [cyan]{message}[/cyan]")
            else:
                self.console.print(f"🚀 {log_msg}")

    def success(self, factor_id: int, result: Dict, duration: float):
        """记录成功"""
        self.stats['processed'] += 1
        self.stats['succeeded'] += 1

        result_text = f"({duration:.2f}s)"
        if 'sharpe' in result:
            result_text += f" Sharpe={result['sharpe']:.2f}"
        if 'fitness' in result:
            result_text += f" Fitness={result['fitness']:.2f}"

        self.file_logger.info(
            f"Factor {factor_id} succeeded in {duration:.2f}s - {result}")

        if not self.quiet and self.console:
            self.console.print(
                f"  ✅ 因子 #{factor_id} 成功 [green]{result_text}[/green]")

    def error(self, factor_id: int, error: str, duration: float):
        """记录错误"""
        self.stats['processed'] += 1
        self.stats['failed'] += 1

        self.file_logger.error(
            f"Factor {factor_id} failed in {duration:.2f}s - {error}")

        if not self.quiet and self.console:
            self.console.print(
                f"  ❌ 因子 #{factor_id} 失败 [red]({duration:.2f}s) {error}[/red]")

    def warning(self, message: str, factor_id: int = None):
        """记录警告"""
        log_msg = f"[{self.name}] {message}"
        if factor_id:
            log_msg = f"[{self.name}] Factor #{factor_id}: {message}"

        self.file_logger.warning(log_msg)

        if not self.quiet and self.console:
            if factor_id:
                self.console.print(
                    f"  ⚠️ 因子 #{factor_id}: [yellow]{message}[/yellow]")
            else:
                self.console.print(f"⚠️ {log_msg}")

    def batch_summary(self, batch_size: int, duration: float):
        """批次总结"""
        success_rate = self.stats['succeeded'] / \
            self.stats['processed'] if self.stats['processed'] > 0 else 0

        summary = f"批次完成: {self.stats['succeeded']}/{batch_size} 成功 ({success_rate:.1%}) 耗时 {duration:.1f}s"
        self.file_logger.info(summary)

        if not self.quiet and self.console:
            table = Table(title=f"{self.name} 批次摘要")
            table.add_column("指标", style="cyan")
            table.add_column("数值", style="green")
            table.add_row("成功/总数", f"{self.stats['succeeded']}/{batch_size}")
            table.add_row("成功率", f"{success_rate:.1%}")
            table.add_row("总耗时", f"{duration:.1f}s")
            self.console.print(table)

    def queue_status(self, stats: Dict):
        """显示队列状态"""
        if self.quiet or not self.console:
            return

        table = Table(title="📋 队列状态")
        table.add_column("阶段", style="cyan")
        table.add_column("待处理", style="yellow")
        table.add_column("已完成", style="green")
        table.add_column("失败", style="red")

        for stage, data in stats.items():
            table.add_row(
                stage.upper(),
                str(data.get('pending', 0)),
                str(data.get('completed', 0)),
                str(data.get('failed', 0))
            )

        self.console.print(table)

    def show_progress(self, description: str, total: int) -> Optional[TaskID]:
        """显示进度条（如果不是静默模式）"""
        if self.quiet or not self.console:
            return None

        progress = Progress()
        task = progress.add_task(description, total=total)
        return task

    def system_status(self, status_data: Dict):
        """显示系统状态"""
        if self.quiet or not self.console:
            return

        # 账户状态表
        if 'accounts' in status_data:
            accounts_table = Table(title="👥 账户状态")
            accounts_table.add_column("账户", style="cyan")
            accounts_table.add_column("状态", style="green")
            accounts_table.add_column("当前负载", style="yellow")
            accounts_table.add_column("成功率", style="blue")

            for account_data in status_data['accounts']:
                status_color = "green" if account_data['status'] == 'active' else "red"
                accounts_table.add_row(
                    account_data['name'],
                    f"[{status_color}]{account_data['status']}[/{status_color}]",
                    str(account_data.get('current_load', 0)),
                    f"{account_data.get('success_rate', 0):.1%}"
                )

            self.console.print(accounts_table)

        # 系统统计
        if 'system_stats' in status_data:
            stats = status_data['system_stats']
            self.console.print(f"\n📈 系统统计:")
            self.console.print(f"  总队列大小: {stats.get('total_queue_size', 0)}")
            self.console.print(f"  处理中任务: {stats.get('total_processing', 0)}")
            self.console.print(
                f"  今日完成: {stats.get('total_completed_today', 0)}")
            self.console.print(
                f"  今日提交: {stats.get('total_submitted_today', 0)}")
            self.console.print(
                f"  系统错误率: {stats.get('system_error_rate', 0):.2%}")

    def ml_performance_comparison(self, comparison_data: Dict):
        """显示ML vs 传统方法性能对比"""
        if self.quiet or not self.console:
            return

        table = Table(title="🤖 ML vs 传统方法性能对比")
        table.add_column("指标", style="cyan")
        table.add_column("传统方法", style="yellow")
        table.add_column("ML增强", style="green")
        table.add_column("改进幅度", style="blue")

        for metric, data in comparison_data.items():
            traditional = data.get('traditional', 0)
            ml_enhanced = data.get('ml_enhanced', 0)

            if traditional > 0:
                improvement = ((ml_enhanced - traditional) / traditional) * 100
                improvement_text = f"{improvement:+.1f}%"
                if improvement > 0:
                    improvement_text = f"[green]{improvement_text}[/green]"
                else:
                    improvement_text = f"[red]{improvement_text}[/red]"
            else:
                improvement_text = "N/A"

            table.add_row(
                metric,
                f"{traditional:.2f}" if isinstance(
                    traditional, float) else str(traditional),
                f"{ml_enhanced:.2f}" if isinstance(
                    ml_enhanced, float) else str(ml_enhanced),
                improvement_text
            )

        self.console.print(table)

    def reset_stats(self):
        """重置统计数据"""
        self.stats = {'processed': 0, 'succeeded': 0, 'failed': 0}

    def get_stats(self) -> Dict:
        """获取统计数据"""
        return self.stats.copy()


class LoggerManager:
    """日志管理器 - 管理多个Logger实例"""

    def __init__(self):
        self.loggers = {}

    def get_logger(self, name: str, quiet: bool = False) -> SimpleLogger:
        """获取或创建Logger实例"""
        key = f"{name}_{quiet}"
        if key not in self.loggers:
            self.loggers[key] = SimpleLogger(name, quiet)
        return self.loggers[key]

    def get_all_stats(self) -> Dict[str, Dict]:
        """获取所有Logger的统计数据"""
        return {name: logger.get_stats() for name, logger in self.loggers.items()}


# 全局日志管理器实例
_logger_manager = LoggerManager()


def get_logger(name: str, quiet: bool = False) -> SimpleLogger:
    """获取Logger实例的便捷函数"""
    return _logger_manager.get_logger(name, quiet)
