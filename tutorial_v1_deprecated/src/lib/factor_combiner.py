"""
因子组合器 - 重构自digging_2step.py的逻辑
支持因子筛选、二阶因子生成和智能组合
"""

from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict
from datetime import datetime, timedelta
import json
from .simple_logger import get_logger
from .factor_generator import FactorGenerator

logger = get_logger(__name__)


class FactorCombiner:
    """因子组合器 - 处理因子筛选、组合和参数优化"""

    def __init__(self):
        self.factor_generator = FactorGenerator()

        # 因子筛选阈值
        self.default_thresholds = {
            'min_returns': 0.75,
            'min_sharpe': 0.5,
            'max_days_lookback': 100,
            'min_turnover': 0.1,
            'max_drawdown': 0.3,
            'min_daily_pnl': 100
        }

        # 地区-宇宙映射
        self.region_universe_mapping = {
            "usa": ("USA", "TOP3000"),
            "asi": ("ASI", "MINVOL1M"),
            "eur": ("EUR", "TOP1200"),
            "glb": ("GLB", "TOP3000"),
            "hkg": ("HKG", "TOP800"),
            "twn": ("TWN", "TOP500"),
            "jpn": ("JPN", "TOP1600"),
            "kor": ("KOR", "TOP600"),
            "chn": ("CHN", "TOP2000U"),
            "amr": ("AMR", "TOP600")
        }

    def get_qualified_factors(self,
                              start_date: str,
                              end_date: str,
                              region: str = "USA",
                              universe: str = "TOP3000",
                              step1_tag: str = "",
                              **thresholds) -> Dict[str, List[Tuple[str, int]]]:
        """
        获取符合条件的一阶因子 - 重构自get_alphas逻辑

        Args:
            start_date: 开始日期
            end_date: 结束日期
            region: 地区
            universe: 股票池
            step1_tag: 第一阶段标签
            **thresholds: 筛选阈值

        Returns:
            符合条件的因子字典 {'next': [(expr, decay)], 'decay': [(expr, decay)]}
        """
        # 合并默认阈值和用户自定义阈值
        criteria = {**self.default_thresholds, **thresholds}

        logger.info(f"开始筛选因子: {start_date} to {end_date}, 标签: {step1_tag}")
        logger.info(f"筛选条件: {criteria}")

        # 模拟从数据库查询的逻辑（实际应该从AlphaFactorQueueModel查询）
        qualified_factors = {
            'next': [],  # 准备进入下一阶段的因子
            'decay': []  # 表现正在衰减但仍可用的因子
        }

        # 这里应该替换为真实的数据库查询逻辑
        # 暂时返回模拟数据结构
        sample_factors = [
            ("rank(close)", 6),
            ("ts_rank(volume, 20)", 8),
            ("group_rank(returns, sector)", 5)
        ]

        for expr, decay in sample_factors:
            # 模拟筛选逻辑
            if self._evaluate_factor_performance(expr, criteria):
                if decay <= 6:
                    qualified_factors['next'].append((expr, decay))
                else:
                    qualified_factors['decay'].append((expr, decay))

        logger.info(
            f"筛选结果: next={len(qualified_factors['next'])}, decay={len(qualified_factors['decay'])}")
        return qualified_factors

    def _evaluate_factor_performance(self, expression: str, criteria: Dict[str, float]) -> bool:
        """评估因子表现是否符合标准"""
        # 这里应该实现真实的因子表现评估逻辑
        # 暂时返回简单的启发式判断

        # 基于表达式复杂度的简单评估
        complexity = len(expression.split('('))
        if complexity > 3:  # 过于复杂的因子可能过拟合
            return False

        # 检查是否包含常见的好因子模式
        good_patterns = ['rank', 'ts_rank', 'group_rank', 'zscore', 'returns']
        if any(pattern in expression for pattern in good_patterns):
            return True

        return False

    def transform_factors(self, factor_tuples: List[Tuple[str, int]]) -> List[Tuple[str, int]]:
        """
        转换因子格式 - 重构自transform函数

        Args:
            factor_tuples: 因子元组列表 [(expression, decay)]

        Returns:
            转换后的因子列表
        """
        transformed = []

        for expr, decay in factor_tuples:
            # 规范化表达式格式
            normalized_expr = self._normalize_expression(expr)

            # 调整decay参数
            adjusted_decay = self._adjust_decay_parameter(decay, expr)

            transformed.append((normalized_expr, adjusted_decay))

        logger.info(f"转换了 {len(factor_tuples)} 个因子")
        return transformed

    def _normalize_expression(self, expression: str) -> str:
        """规范化因子表达式格式"""
        # 移除多余空格
        normalized = ' '.join(expression.split())

        # 标准化函数名称
        replacements = {
            'ts_rank': 'ts_rank',
            'group_rank': 'group_rank',
            'ts_zscore': 'ts_zscore'
        }

        for old, new in replacements.items():
            normalized = normalized.replace(old, new)

        return normalized

    def _adjust_decay_parameter(self, decay: int, expression: str) -> int:
        """根据因子类型调整decay参数"""
        # 基于因子复杂度调整decay
        if 'group_' in expression:
            # 分组因子通常需要更长的衰减期
            return max(decay, 8)
        elif 'ts_' in expression:
            # 时间序列因子适中衰减
            return max(decay, 6)
        else:
            # 基础因子较短衰减
            return max(decay, 4)

    def generate_second_order_factors(self,
                                      first_order_factors: List[Tuple[str, int]],
                                      region: str = "USA") -> Dict[str, List[Tuple[str, int]]]:
        """
        生成二阶因子组合

        Args:
            first_order_factors: 一阶因子列表
            region: 地区标识

        Returns:
            按地区分组的二阶因子字典
        """
        so_alpha_dict = defaultdict(list)

        for expr, decay in first_order_factors:
            # 生成分组操作的二阶因子
            group_factors = self.factor_generator.get_second_order_factory(
                [expr],
                self.factor_generator.group_ops,
                region.lower()
            )

            # 为每个生成的因子添加decay参数
            for alpha in group_factors:
                so_alpha_dict[region].append((alpha, decay))

        logger.info(f"为地区 {region} 生成了 {len(so_alpha_dict[region])} 个二阶因子")
        return dict(so_alpha_dict)

    def filter_completed_factors(self,
                                 factors: List[Tuple[str, int]],
                                 completed_file: str) -> List[Tuple[str, int]]:
        """
        过滤已完成的因子

        Args:
            factors: 待过滤的因子列表
            completed_file: 已完成因子记录文件

        Returns:
            过滤后的因子列表
        """
        completed_expressions = self._read_completed_alphas(completed_file)

        filtered = [
            (expr, decay) for expr, decay in factors
            if expr not in completed_expressions
        ]

        completed_count = len(factors) - len(filtered)
        logger.info(f"过滤结果: {len(filtered)} 个待处理, {completed_count} 个已完成")

        return filtered

    def _read_completed_alphas(self, filepath: str) -> set:
        """从文件读取已完成的因子表达式"""
        completed_alphas = set()
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                for line in f:
                    completed_alphas.add(line.strip())
        except FileNotFoundError:
            logger.warning(f"文件未找到: {filepath}")
        except Exception as e:
            logger.error(f"读取完成记录失败: {e}")

        return completed_alphas

    def optimize_factor_parameters(self,
                                   factors: List[Tuple[str, int]],
                                   target_metrics: Dict[str, float]) -> List[Tuple[str, int, Dict[str, Any]]]:
        """
        优化因子参数

        Args:
            factors: 因子列表
            target_metrics: 目标指标

        Returns:
            优化后的因子列表，包含参数建议
        """
        optimized_factors = []

        for expr, decay in factors:
            # 分析因子类型并优化参数
            optimization_result = self._optimize_single_factor(
                expr, decay, target_metrics)

            optimized_factors.append((
                expr,
                optimization_result['optimized_decay'],
                optimization_result['parameters']
            ))

        logger.info(f"优化了 {len(factors)} 个因子的参数")
        return optimized_factors

    def _optimize_single_factor(self,
                                expression: str,
                                decay: int,
                                target_metrics: Dict[str, float]) -> Dict[str, Any]:
        """优化单个因子的参数"""
        result = {
            'optimized_decay': decay,
            'parameters': {},
            'confidence_score': 0.0
        }

        # 基于表达式类型优化decay
        if 'ts_' in expression:
            # 时间序列因子优化
            if 'ts_rank' in expression:
                result['optimized_decay'] = min(max(decay, 5), 10)
            elif 'ts_std_dev' in expression:
                result['optimized_decay'] = min(max(decay, 8), 15)

        elif 'group_' in expression:
            # 分组因子优化
            result['optimized_decay'] = min(max(decay, 6), 12)

        # 添加推荐的模拟参数
        result['parameters'] = {
            'region': 'USA',
            'universe': 'TOP3000',
            'neutralization': 'SUBINDUSTRY',
            'delay': 1
        }

        # 置信度评分（基于因子复杂度和类型）
        result['confidence_score'] = self._calculate_confidence_score(
            expression)

        return result

    def _calculate_confidence_score(self, expression: str) -> float:
        """计算因子的置信度评分"""
        score = 0.5  # 基础分数

        # 基于已知良好模式加分
        good_patterns = {
            'rank': 0.1,
            'ts_rank': 0.15,
            'group_rank': 0.2,
            'zscore': 0.1,
            'ts_std_dev': 0.05
        }

        for pattern, points in good_patterns.items():
            if pattern in expression:
                score += points

        # 基于复杂度调整
        complexity = len(expression.split('('))
        if complexity <= 2:
            score += 0.1  # 简单因子加分
        elif complexity > 4:
            score -= 0.2  # 过复杂减分

        return min(max(score, 0.0), 1.0)

    def create_factor_batches(self,
                              factors: List[Tuple[str, int]],
                              batch_size: int = 50,
                              priority_rules: Optional[Dict[str, float]] = None) -> List[Dict[str, Any]]:
        """
        创建因子批次，用于分批处理

        Args:
            factors: 因子列表
            batch_size: 批次大小
            priority_rules: 优先级规则

        Returns:
            因子批次列表
        """
        if priority_rules is None:
            priority_rules = {
                'rank': 1.0,
                'ts_rank': 0.9,
                'group_rank': 0.8,
                'zscore': 0.7
            }

        # 按优先级排序
        scored_factors = []
        for expr, decay in factors:
            priority = self._calculate_priority(expr, priority_rules)
            scored_factors.append((priority, expr, decay))

        scored_factors.sort(reverse=True)  # 高优先级在前

        # 创建批次
        batches = []
        for i in range(0, len(scored_factors), batch_size):
            batch_factors = scored_factors[i:i + batch_size]

            batch = {
                'batch_id': f"batch_{i // batch_size + 1}",
                'factors': [(expr, decay) for _, expr, decay in batch_factors],
                'priority_scores': [priority for priority, _, _ in batch_factors],
                'size': len(batch_factors),
                'estimated_time': len(batch_factors) * 30  # 估算处理时间（秒）
            }
            batches.append(batch)

        logger.info(f"创建了 {len(batches)} 个批次，总计 {len(factors)} 个因子")
        return batches

    def _calculate_priority(self, expression: str, priority_rules: Dict[str, float]) -> float:
        """计算因子优先级"""
        base_priority = 0.5

        for pattern, weight in priority_rules.items():
            if pattern in expression:
                base_priority += weight * 0.1

        # 简单因子优先
        complexity = len(expression.split('('))
        if complexity <= 2:
            base_priority += 0.1

        return base_priority

    def analyze_factor_relationships(self, factors: List[str]) -> Dict[str, Any]:
        """
        分析因子之间的关系

        Args:
            factors: 因子表达式列表

        Returns:
            因子关系分析结果
        """
        analysis = {
            'total_factors': len(factors),
            'factor_families': defaultdict(list),
            'complexity_distribution': {},
            'potential_correlations': []
        }

        # 按因子族群分类
        for factor in factors:
            family = self._identify_factor_family(factor)
            analysis['factor_families'][family].append(factor)

        # 复杂度分布
        complexities = [len(f.split('(')) for f in factors]
        analysis['complexity_distribution'] = {
            'min': min(complexities) if complexities else 0,
            'max': max(complexities) if complexities else 0,
            'avg': sum(complexities) / len(complexities) if complexities else 0
        }

        # 识别潜在相关因子
        analysis['potential_correlations'] = self._find_potential_correlations(
            factors)

        logger.info(f"分析了 {len(factors)} 个因子的关系")
        return dict(analysis)

    def _identify_factor_family(self, expression: str) -> str:
        """识别因子所属族群"""
        if 'ts_rank' in expression:
            return 'time_series_rank'
        elif 'group_rank' in expression:
            return 'cross_sectional_rank'
        elif 'ts_' in expression:
            return 'time_series'
        elif 'group_' in expression:
            return 'cross_sectional'
        elif 'rank' in expression:
            return 'ranking'
        else:
            return 'basic'

    def _find_potential_correlations(self, factors: List[str]) -> List[Dict[str, Any]]:
        """查找潜在相关的因子对"""
        correlations = []

        for i, factor1 in enumerate(factors):
            for factor2 in factors[i+1:]:
                similarity = self._calculate_expression_similarity(
                    factor1, factor2)
                if similarity > 0.7:  # 高相似度阈值
                    correlations.append({
                        'factor1': factor1,
                        'factor2': factor2,
                        'similarity': similarity,
                        'risk_level': 'high' if similarity > 0.9 else 'medium'
                    })

        return correlations

    def _calculate_expression_similarity(self, expr1: str, expr2: str) -> float:
        """计算两个因子表达式的相似度"""
        # 简单的字符串相似度计算
        set1 = set(expr1.replace('(', ' ').replace(')', ' ').split())
        set2 = set(expr2.replace('(', ' ').replace(')', ' ').split())

        if not set1 or not set2:
            return 0.0

        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))

        return intersection / union if union > 0 else 0.0
