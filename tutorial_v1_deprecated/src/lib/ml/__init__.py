"""
机器学习组件模块 - 基础接口定义
支持ML增强的因子挖掘功能，但保持与传统方法的兼容性
"""

from .factor_intelligence_engine import FactorIntelligenceEngine
from .genetic_factor_evolution import GeneticFactorEvolution
from .factor_quality_predictor import FactorQualityPredictor
from .intelligent_factor_combiner import IntelligentFactor<PERSON>ombiner
from .multi_objective_optimizer import MultiObjectiveOptimizer
from .performance_predictor import PerformancePredictor
from .intelligent_trade_when_generator import IntelligentTradeWhenGenerator
from .market_regime_detector import MarketRegimeDetector
from .factor_timing_optimizer import FactorTimingOptimizer

__all__ = [
    'FactorIntelligenceEngine',
    'GeneticFactorEvolution',
    'FactorQualityPredictor',
    'IntelligentFactorCombiner',
    'MultiObjectiveOptimizer',
    'PerformancePredictor',
    'IntelligentTradeWhenGenerator',
    'MarketRegimeDetector',
    'FactorTimingOptimizer'
]
