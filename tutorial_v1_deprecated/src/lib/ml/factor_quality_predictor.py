"""
因子质量预测器 - ML预筛选低质量因子
避免无效API调用，提高处理效率
"""

from typing import List, Dict, Any
import asyncio
import hashlib


class FactorQualityPredictor:
    """因子质量预测器 - 基础接口实现"""

    def __init__(self):
        self.enabled = True
        self.quality_threshold = 0.6  # 质量阈值
        self.feature_cache = {}

    async def predict_batch(self, expressions: List[str]) -> List[float]:
        """批量质量预测 - 过滤低质量因子"""

        if not self.enabled:
            # ML功能禁用时，返回高分不过滤
            return [0.8] * len(expressions)

        # 模拟批量预测
        await asyncio.sleep(0.05)  # 模拟预测时间

        scores = []
        for expr in expressions:
            score = await self._predict_single_quality(expr)
            scores.append(score)

        return scores

    async def _predict_single_quality(self, expression: str) -> float:
        """单个因子质量预测"""

        # 使用表达式hash作为伪随机种子，确保结果可重现
        expr_hash = int(hashlib.md5(expression.encode()).hexdigest()[:8], 16)

        # 基础质量分数
        base_score = 0.4 + (expr_hash % 100) / 250  # 0.4-0.8之间

        # 根据表达式特征调整分数
        features = self._extract_features(expression)

        # 长度特征
        if 50 <= features['length'] <= 150:
            base_score += 0.1
        elif features['length'] > 200:
            base_score -= 0.1

        # 复杂度特征
        if 3 <= features['operator_count'] <= 8:
            base_score += 0.1
        elif features['operator_count'] > 12:
            base_score -= 0.15

        # 常见优质模式
        quality_patterns = ['rank', 'ts_mean', 'ts_std', 'delta', 'decay']
        pattern_score = sum(
            1 for pattern in quality_patterns if pattern in expression.lower())
        base_score += min(pattern_score * 0.05, 0.2)

        # 避免过于简单的表达式
        if features['operator_count'] < 2:
            base_score -= 0.2

        return min(max(base_score, 0.1), 0.95)

    def _extract_features(self, expression: str) -> Dict[str, Any]:
        """提取因子表达式特征"""

        cache_key = hashlib.md5(expression.encode()).hexdigest()
        if cache_key in self.feature_cache:
            return self.feature_cache[cache_key]

        features = {
            'length': len(expression),
            'operator_count': 0,
            'function_count': 0,
            'has_rank': 'rank' in expression.lower(),
            'has_ts_operations': any(op in expression.lower() for op in ['ts_mean', 'ts_std', 'ts_sum']),
            'has_delay': 'delay' in expression.lower(),
            'parentheses_depth': 0
        }

        # 计算操作符数量
        operators = ['+', '-', '*', '/', '>', '<', '==', '!=', '&', '|']
        for op in operators:
            features['operator_count'] += expression.count(op)

        # 计算函数数量
        functions = ['rank', 'ts_mean', 'ts_std', 'ts_sum',
                     'delay', 'delta', 'abs', 'log', 'sqrt']
        for func in functions:
            if func in expression.lower():
                features['function_count'] += expression.lower().count(func)

        # 计算括号深度
        max_depth = 0
        current_depth = 0
        for char in expression:
            if char == '(':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char == ')':
                current_depth -= 1
        features['parentheses_depth'] = max_depth

        # 缓存结果
        self.feature_cache[cache_key] = features
        return features

    def set_quality_threshold(self, threshold: float):
        """设置质量阈值"""
        self.quality_threshold = max(0.1, min(threshold, 0.9))

    def get_qualified_factors(self, expressions: List[str], scores: List[float]) -> List[str]:
        """根据分数筛选合格的因子"""
        qualified = []
        for expr, score in zip(expressions, scores):
            if score >= self.quality_threshold:
                qualified.append(expr)
        return qualified

    async def analyze_factor_distribution(self, expressions: List[str]) -> Dict[str, Any]:
        """分析因子分布特征"""
        if not expressions:
            return {}

        scores = await self.predict_batch(expressions)

        analysis = {
            'total_factors': len(expressions),
            'avg_quality_score': sum(scores) / len(scores),
            'qualified_count': sum(1 for score in scores if score >= self.quality_threshold),
            'qualification_rate': sum(1 for score in scores if score >= self.quality_threshold) / len(scores),
            'score_distribution': {
                'high_quality': sum(1 for score in scores if score >= 0.8),
                'medium_quality': sum(1 for score in scores if 0.6 <= score < 0.8),
                'low_quality': sum(1 for score in scores if score < 0.6)
            }
        }

        return analysis

    def enable_ml_features(self):
        """启用ML功能"""
        self.enabled = True

    def disable_ml_features(self):
        """禁用ML功能"""
        self.enabled = False

    def get_predictor_stats(self) -> Dict:
        """获取预测器统计信息"""
        return {
            'enabled': self.enabled,
            'quality_threshold': self.quality_threshold,
            'cache_size': len(self.feature_cache),
            'version': '1.0.0-basic'
        }
