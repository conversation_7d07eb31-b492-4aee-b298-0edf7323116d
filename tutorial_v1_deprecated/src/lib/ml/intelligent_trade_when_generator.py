"""
智能交易条件生成器 - 生成trade_when因子
"""

from typing import List
import asyncio


class IntelligentTradeWhenGenerator:
    """智能交易条件生成器 - 基础接口实现"""

    def __init__(self):
        self.enabled = True

    async def generate_trade_when_variants(self, base_expression: str, market_regime: dict = None) -> List[str]:
        """生成trade_when变体"""

        if not self.enabled:
            return []

        await asyncio.sleep(0.1)

        # 基础trade_when模板
        templates = [
            f"trade_when({base_expression}, {base_expression} > delay({base_expression}, 1))",
            f"trade_when({base_expression}, {base_expression} > ts_mean({base_expression}, 5))",
            f"trade_when({base_expression}, abs({base_expression}) > ts_std({base_expression}, 20))"
        ]

        return templates[:3]  # 返回前3个变体

    def enable_ml_features(self):
        self.enabled = True

    def disable_ml_features(self):
        self.enabled = False
