"""
lib包 - 包含项目的核心功能模块

包含以下模块：
- models: 数据模型定义
- db: 数据库操作功能  
- db_config_reader: 数据库配置读取器
- login: 登录认证功能
- session: 会话管理功能
- logger: 统一日志配置
"""

from .models import (
    db, ALL_MODELS, BaseModel,
    UserAccountModel, DatasetModel, AccountDatasetUsageModel,
    AccountAlphaFactorModel, AlphaDiggingProcessModel,
    RegionModel, UrlConfigModel
)
from .db import DatabaseManager, create_database_manager, clear_database_manager_cache
from .db_config_reader import DbConfigReader, get_config_reader, clear_config_reader_cache
from .login import login
from .session import get_cached_session, get_session_manager, invalidate_cached_session, get_session_info
from .logger import get_logger, set_log_level, add_file_logging, reload_logging_config, logger, get_ui, UserInterface

__all__ = [
    # 数据模型
    'db', 'ALL_MODELS', 'BaseModel',
    'UserAccountModel', 'DatasetModel', 'AccountDatasetUsageModel',
    'AccountAlphaFactorModel', 'AlphaDiggingProcessModel',
    'RegionModel', 'UrlConfigModel',

    # 数据库操作
    'DatabaseManager', 'create_database_manager', 'clear_database_manager_cache',

    # 配置读取
    'DbConfigReader', 'get_config_reader', 'clear_config_reader_cache',

    # 登录认证
    'login',

    # 会话管理
    'get_cached_session', 'get_session_manager', 'invalidate_cached_session', 'get_session_info',

    # 日志功能
    'get_logger', 'set_log_level', 'add_file_logging', 'reload_logging_config', 'logger', 'get_ui', 'UserInterface'
]
