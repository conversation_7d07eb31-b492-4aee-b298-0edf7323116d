"""
因子生成器 - 重构自machine_lib.py的first_order_factory逻辑
支持多种算子和数据字段的智能组合生成
"""

from typing import List, Dict, Any, Optional
from itertools import product
import json
from .simple_logger import get_logger

logger = get_logger(__name__)


class FactorGenerator:
    """因子生成器 - 一阶因子生成的核心组件"""

    def __init__(self):
        # 基础操作符集合
        self.basic_ops = [
            "log", "sqrt", "reverse", "inverse", "rank", "zscore",
            "log_diff", "s_log_1p", "fraction", "quantile",
            "normalize", "scale_down"
        ]

        # 时间序列操作符
        self.ts_ops = [
            "ts_rank", "ts_zscore", "ts_delta", "ts_sum", "ts_product",
            "ts_ir", "ts_std_dev", "ts_mean", "ts_arg_min", "ts_arg_max",
            "ts_min_diff", "ts_max_diff", "ts_returns", "ts_scale",
            "ts_skewness", "ts_kurtosis", "ts_quantile"
        ]

        # 分组操作符
        self.group_ops = [
            "group_neutralize", "group_rank", "group_normalize",
            "group_scale", "group_zscore"
        ]

        # 高级操作符
        self.arsenal = [
            "ts_moment", "ts_entropy", "ts_min_max_cps", "ts_min_max_diff",
            "inst_tvr", "sigmoid", "ts_decay_exp_window", "ts_percentage",
            "vector_neut", "vector_proj", "signed_power"
        ]

        # 时间窗口参数
        self.time_windows = [5, 22, 66, 120, 240]

        # 向量字段
        self.vectors = ["cap"]

    def first_order_factory(self, fields: List[str], ops_set: List[str]) -> List[str]:
        """
        生成一阶因子表达式 - 重构自machine_lib.py

        Args:
            fields: 基础字段列表
            ops_set: 操作符集合

        Returns:
            生成的因子表达式列表
        """
        alpha_set = []

        for field in fields:
            # 添加原始字段
            alpha_set.append(field)

            for op in ops_set:
                if op == "ts_percentage":
                    alpha_set += self._ts_comp_factory(
                        op, field, "percentage", [0.5])

                elif op == "ts_decay_exp_window":
                    alpha_set += self._ts_comp_factory(op,
                                                       field, "factor", [0.5])

                elif op == "ts_moment":
                    alpha_set += self._ts_comp_factory(op,
                                                       field, "k", [2, 3, 4])

                elif op == "ts_entropy":
                    alpha_set += self._ts_comp_factory(op,
                                                       field, "buckets", [10])

                elif op.startswith("ts_") or op == "inst_tvr":
                    alpha_set += self._ts_factory(op, field)

                elif op.startswith("group_"):
                    alpha_set += self._group_factory(op, field, "usa")

                elif op.startswith("vector"):
                    alpha_set += self._vector_factory(op, field)

                elif op == "signed_power":
                    alpha = f"{op}({field}, 2)"
                    alpha_set.append(alpha)

                else:
                    alpha = f"{op}({field})"
                    alpha_set.append(alpha)

        logger.info(f"生成了 {len(alpha_set)} 个一阶因子表达式")
        return alpha_set

    def _ts_factory(self, op: str, field: str) -> List[str]:
        """生成时间序列因子"""
        output = []
        for day in self.time_windows:
            alpha = f"{op}({field}, {day})"
            output.append(alpha)
        return output

    def _ts_comp_factory(self, op: str, field: str, factor: str, paras: List) -> List[str]:
        """生成复合时间序列因子"""
        output = []
        comb = list(product(self.time_windows, paras))

        for day, para in comb:
            if isinstance(para, float):
                alpha = f"{op}({field}, {day}, {factor}={para:.1f})"
            elif isinstance(para, int):
                alpha = f"{op}({field}, {day}, {factor}={para})"
            output.append(alpha)

        return output

    def _vector_factory(self, op: str, field: str) -> List[str]:
        """生成向量因子"""
        output = []
        for vector in self.vectors:
            alpha = f"{op}({field}, {vector})"
            output.append(alpha)
        return output

    def _group_factory(self, op: str, field: str, region: str) -> List[str]:
        """生成分组因子"""
        output = []

        # 基础分组字段
        groups = [
            "market", "sector", "industry", "subindustry",
            "bucket(rank(cap), range='0.1, 1, 0.1')",
            "bucket(group_rank(cap,sector),range='0,1,0.1')",
            "bucket(rank(ts_std_dev(ts_returns(close,1),20)),range = '0.1,1,0.1')"
        ]

        for group in groups:
            if op.startswith("group_vector"):
                for vector in self.vectors:
                    alpha = f"{op}({field},{vector},densify({group}))"
                    output.append(alpha)
            elif op.startswith("group_percentage"):
                alpha = f"{op}({field},densify({group}),percentage=0.5)"
                output.append(alpha)
            else:
                alpha = f"{op}({field},densify({group}))"
                output.append(alpha)

        return output

    def get_second_order_factory(self, first_order: List[str], ops_set: List[str], region: str = "usa") -> List[str]:
        """
        生成二阶因子表达式

        Args:
            first_order: 一阶因子列表
            ops_set: 操作符集合
            region: 地区标识

        Returns:
            二阶因子表达式列表
        """
        second_order = []

        for fo in first_order:
            for op in ops_set:
                if op.startswith("group_"):
                    second_order += self._group_factory(op, fo, region)
                elif op.startswith("ts_"):
                    second_order += self._ts_factory(op, fo)
                else:
                    # 基础操作符直接应用
                    alpha = f"{op}({fo})"
                    second_order.append(alpha)

        logger.info(f"从 {len(first_order)} 个一阶因子生成了 {len(second_order)} 个二阶因子")
        return second_order

    def get_twin_field_factory(self, op: str, field: str, fields: List[str]) -> List[str]:
        """生成双字段因子（如相关性因子）"""
        output = []
        days = [5, 22, 66, 240]
        outset = list(set(fields) - {field})

        for day in days:
            for counterpart in outset:
                alpha = f"{op}({field}, {counterpart}, {day})"
                output.append(alpha)

        return output

    def generate_recommended_factors(self, dataset_id: str, fields: List[str]) -> Dict[str, List[str]]:
        """
        生成推荐的因子组合

        Args:
            dataset_id: 数据集ID
            fields: 基础字段列表

        Returns:
            按类型分类的因子字典
        """
        factor_groups = {}

        # 基础因子
        basic_factors = self.first_order_factory(fields, self.basic_ops)
        factor_groups['basic'] = basic_factors

        # 时间序列因子
        ts_factors = self.first_order_factory(fields, self.ts_ops)
        factor_groups['timeseries'] = ts_factors

        # 高级因子
        advanced_factors = self.first_order_factory(fields, self.arsenal)
        factor_groups['advanced'] = advanced_factors

        # 根据数据集特点调整推荐
        if dataset_id == 'analyst4':
            # 分析师数据集更适合基础和时间序列因子
            factor_groups['recommended'] = basic_factors[:100] + \
                ts_factors[:200]
        elif dataset_id == 'news18':
            # 新闻数据集更适合高级因子
            factor_groups['recommended'] = advanced_factors[:150] + \
                basic_factors[:100]
        else:
            # 默认推荐
            factor_groups['recommended'] = basic_factors[:50] + \
                ts_factors[:100] + advanced_factors[:50]

        logger.info(f"为数据集 {dataset_id} 生成了 {len(factor_groups)} 个因子组")
        return factor_groups

    def filter_factors_by_complexity(self, factors: List[str], max_complexity: int = 10) -> List[str]:
        """根据复杂度过滤因子"""
        filtered = []

        for factor in factors:
            complexity = self._calculate_complexity(factor)
            if complexity <= max_complexity:
                filtered.append(factor)

        logger.info(f"复杂度过滤: {len(factors)} -> {len(filtered)} 个因子")
        return filtered

    def _calculate_complexity(self, expression: str) -> int:
        """计算因子表达式的复杂度"""
        complexity = 0

        # 括号深度
        max_depth = 0
        current_depth = 0
        for char in expression:
            if char == '(':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char == ')':
                current_depth -= 1
        complexity += max_depth

        # 操作符数量
        operators = ['ts_', 'group_', 'rank', 'delay', 'delta']
        for op in operators:
            complexity += expression.count(op)

        return complexity

    def validate_expressions(self, expressions: List[str]) -> Dict[str, List[str]]:
        """验证因子表达式的有效性"""
        result = {
            'valid': [],
            'invalid': [],
            'warnings': []
        }

        for expr in expressions:
            try:
                # 基础语法检查
                if self._basic_syntax_check(expr):
                    result['valid'].append(expr)
                else:
                    result['invalid'].append(expr)
            except Exception as e:
                logger.warning(f"验证表达式失败: {expr}, 错误: {e}")
                result['invalid'].append(expr)

        logger.info(
            f"表达式验证完成: {len(result['valid'])} 有效, {len(result['invalid'])} 无效")
        return result

    def _basic_syntax_check(self, expression: str) -> bool:
        """基础语法检查"""
        # 检查括号匹配
        if expression.count('(') != expression.count(')'):
            return False

        # 检查空表达式
        if not expression.strip():
            return False

        # 检查基本格式
        if not any(op in expression for op in ['rank', 'ts_', 'group_', 'close', 'open', 'high', 'low', 'volume']):
            return False

        return True

    def get_factor_statistics(self, factors: List[str]) -> Dict[str, Any]:
        """获取因子统计信息"""
        stats = {
            'total_count': len(factors),
            'avg_length': sum(len(f) for f in factors) / len(factors) if factors else 0,
            'complexity_distribution': {},
            'operator_usage': {},
            'field_usage': {}
        }

        # 复杂度分布
        complexities = [self._calculate_complexity(f) for f in factors]
        stats['complexity_distribution'] = {
            'min': min(complexities) if complexities else 0,
            'max': max(complexities) if complexities else 0,
            'avg': sum(complexities) / len(complexities) if complexities else 0
        }

        # 操作符使用统计
        all_ops = self.basic_ops + self.ts_ops + self.group_ops
        for op in all_ops:
            count = sum(1 for f in factors if op in f)
            if count > 0:
                stats['operator_usage'][op] = count

        return stats
