"""
质量检测器 - 重构自check.py的逻辑
支持因子的自相关性检测、生产相关性检测和综合质量评估
"""

from typing import List, Dict, Any, Optional, Tuple
import pandas as pd
from datetime import datetime, timedelta
import asyncio
import json
from concurrent.futures import ThreadPoolExecutor, as_completed
from .simple_logger import get_logger

logger = get_logger(__name__)


class QualityChecker:
    """因子质量检测器 - 检测因子质量和合规性"""

    def __init__(self):
        # 质量检测阈值
        self.quality_thresholds = {
            'max_self_corr': 0.7,
            'max_prod_corr': 0.7,
            'min_sharpe': 0.5,
            'min_returns': 0.01,
            'max_drawdown': 0.3,
            'min_turnover': 0.1,
            'max_turnover': 10.0
        }

        # 检测类型
        self.check_types = [
            'self_correlation',
            'production_correlation',
            'performance_metrics',
            'compliance_check',
            'risk_assessment'
        ]

    def run_comprehensive_check(self,
                                alpha_ids: List[str],
                                check_types: Optional[List[str]] = None,
                                parallel_workers: int = 5) -> Dict[str, Any]:
        """
        运行综合质量检测

        Args:
            alpha_ids: Alpha ID列表
            check_types: 检测类型列表
            parallel_workers: 并发工作数

        Returns:
            综合检测结果
        """
        if check_types is None:
            check_types = self.check_types.copy()

        logger.info(f"开始综合质量检测: {len(alpha_ids)} 个Alpha")

        results = {
            'summary': {
                'total_alphas': len(alpha_ids),
                'passed_count': 0,
                'failed_count': 0,
                'warning_count': 0
            },
            'detailed_results': {},
            'batch_statistics': {},
            'recommendations': []
        }

        # 并发执行检测
        with ThreadPoolExecutor(max_workers=parallel_workers) as executor:
            future_to_alpha = {
                executor.submit(self._check_single_alpha, alpha_id, check_types): alpha_id
                for alpha_id in alpha_ids
            }

            for future in as_completed(future_to_alpha):
                alpha_id = future_to_alpha[future]
                try:
                    check_result = future.result()
                    results['detailed_results'][alpha_id] = check_result

                    # 更新统计
                    if check_result['overall_status'] == 'passed':
                        results['summary']['passed_count'] += 1
                    elif check_result['overall_status'] == 'failed':
                        results['summary']['failed_count'] += 1
                    else:
                        results['summary']['warning_count'] += 1

                except Exception as e:
                    logger.error(f"检测Alpha {alpha_id} 失败: {e}")
                    results['detailed_results'][alpha_id] = {
                        'overall_status': 'error',
                        'error_message': str(e)
                    }
                    results['summary']['failed_count'] += 1

        # 生成批次统计和建议
        results['batch_statistics'] = self._generate_batch_statistics(
            results['detailed_results'])
        results['recommendations'] = self._generate_recommendations(
            results['detailed_results'])

        logger.info(f"检测完成: 通过 {results['summary']['passed_count']}, "
                    f"失败 {results['summary']['failed_count']}, "
                    f"警告 {results['summary']['warning_count']}")

        return results

    def _check_single_alpha(self, alpha_id: str, check_types: List[str]) -> Dict[str, Any]:
        """检测单个Alpha的质量"""
        result = {
            'alpha_id': alpha_id,
            'check_timestamp': datetime.now().isoformat(),
            'checks_performed': check_types,
            'check_results': {},
            'overall_status': 'passed',
            'issues': [],
            'warnings': []
        }

        # 执行各种检测
        for check_type in check_types:
            try:
                if check_type == 'self_correlation':
                    check_result = self._check_self_correlation(alpha_id)
                elif check_type == 'production_correlation':
                    check_result = self._check_production_correlation(alpha_id)
                elif check_type == 'performance_metrics':
                    check_result = self._check_performance_metrics(alpha_id)
                elif check_type == 'compliance_check':
                    check_result = self._check_compliance(alpha_id)
                elif check_type == 'risk_assessment':
                    check_result = self._check_risk_assessment(alpha_id)
                else:
                    check_result = {'status': 'skipped',
                                    'message': f'未知检测类型: {check_type}'}

                result['check_results'][check_type] = check_result

                # 更新整体状态
                if check_result['status'] == 'failed':
                    result['overall_status'] = 'failed'
                    result['issues'].append(
                        f"{check_type}: {check_result.get('message', '检测失败')}")
                elif check_result['status'] == 'warning' and result['overall_status'] != 'failed':
                    result['overall_status'] = 'warning'
                    result['warnings'].append(
                        f"{check_type}: {check_result.get('message', '检测警告')}")

            except Exception as e:
                logger.error(f"执行 {check_type} 检测失败: {e}")
                result['check_results'][check_type] = {
                    'status': 'error',
                    'message': str(e)
                }
                result['overall_status'] = 'failed'
                result['issues'].append(f"{check_type}: 检测过程出错")

        return result

    def _check_self_correlation(self, alpha_id: str) -> Dict[str, Any]:
        """
        检测自相关性 - 重构自get_self_corr逻辑

        Args:
            alpha_id: Alpha ID

        Returns:
            自相关性检测结果
        """
        if hasattr(self, 'api_client') and self.api_client:
            # TODO: 实际应该调用真实的API
            logger.warning("自相关性检测API尚未实现，使用模拟数据")

        # 模拟API调用获取自相关性数据
        try:
            # 模拟数据 - 实际应该从API获取
            logger.debug(f"Alpha {alpha_id}: 使用模拟数据进行自相关性检测")
            mock_corr_data = {
                'max_correlation': 0.65,
                'avg_correlation': 0.45,
                'correlation_count': 150,
                'high_corr_factors': ['factor_1', 'factor_2']
            }

            max_corr = mock_corr_data['max_correlation']
            threshold = self.quality_thresholds['max_self_corr']

            if max_corr > threshold:
                return {
                    'status': 'failed',
                    'message': f'自相关性过高: {max_corr:.3f} > {threshold}',
                    'max_correlation': max_corr,
                    'threshold': threshold,
                    'details': mock_corr_data
                }
            elif max_corr > threshold * 0.8:
                return {
                    'status': 'warning',
                    'message': f'自相关性较高: {max_corr:.3f}',
                    'max_correlation': max_corr,
                    'threshold': threshold,
                    'details': mock_corr_data
                }
            else:
                return {
                    'status': 'passed',
                    'message': f'自相关性正常: {max_corr:.3f}',
                    'max_correlation': max_corr,
                    'threshold': threshold,
                    'details': mock_corr_data
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'自相关性检测失败: {e}'
            }

    def _check_production_correlation(self, alpha_id: str) -> Dict[str, Any]:
        """
        检测生产相关性 - 重构自get_prod_corr逻辑

        Args:
            alpha_id: Alpha ID

        Returns:
            生产相关性检测结果
        """
        if hasattr(self, 'api_client') and self.api_client:
            # TODO: 实际应该调用真实的API
            logger.warning("生产相关性检测API尚未实现，使用模拟数据")

        try:
            # 模拟数据 - 实际应该从API获取
            logger.debug(f"Alpha {alpha_id}: 使用模拟数据进行生产相关性检测")
            mock_prod_data = {
                'max_correlation': 0.55,
                'avg_correlation': 0.25,
                'production_factors_count': 85,
                'high_corr_production_factors': ['prod_factor_1']
            }

            max_corr = mock_prod_data['max_correlation']
            threshold = self.quality_thresholds['max_prod_corr']

            if max_corr > threshold:
                return {
                    'status': 'failed',
                    'message': f'生产相关性过高: {max_corr:.3f} > {threshold}',
                    'max_correlation': max_corr,
                    'threshold': threshold,
                    'details': mock_prod_data
                }
            elif max_corr > threshold * 0.8:
                return {
                    'status': 'warning',
                    'message': f'生产相关性较高: {max_corr:.3f}',
                    'max_correlation': max_corr,
                    'threshold': threshold,
                    'details': mock_prod_data
                }
            else:
                return {
                    'status': 'passed',
                    'message': f'生产相关性正常: {max_corr:.3f}',
                    'max_correlation': max_corr,
                    'threshold': threshold,
                    'details': mock_prod_data
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'生产相关性检测失败: {e}'
            }

    def _check_performance_metrics(self, alpha_id: str) -> Dict[str, Any]:
        """检测性能指标"""
        logger.warning(f"Alpha {alpha_id}: 正在使用模拟性能数据，非真实指标")
        try:
            # 模拟性能数据
            mock_performance = {
                'sharpe_ratio': 0.75,
                'annual_returns': 0.12,
                'max_drawdown': 0.15,
                'turnover': 2.5,
                'information_ratio': 0.8,
                'volatility': 0.16
            }

            issues = []
            warnings = []

            # 检查各项指标
            if mock_performance['sharpe_ratio'] < self.quality_thresholds['min_sharpe']:
                issues.append(
                    f"Sharpe比率过低: {mock_performance['sharpe_ratio']:.3f}")

            if mock_performance['max_drawdown'] > self.quality_thresholds['max_drawdown']:
                issues.append(
                    f"最大回撤过大: {mock_performance['max_drawdown']:.3f}")

            if mock_performance['turnover'] < self.quality_thresholds['min_turnover']:
                warnings.append(f"换手率偏低: {mock_performance['turnover']:.3f}")
            elif mock_performance['turnover'] > self.quality_thresholds['max_turnover']:
                issues.append(f"换手率过高: {mock_performance['turnover']:.3f}")

            if issues:
                status = 'failed'
                message = f"性能指标不合格: {'; '.join(issues)}"
            elif warnings:
                status = 'warning'
                message = f"性能指标需注意: {'; '.join(warnings)}"
            else:
                status = 'passed'
                message = "性能指标正常"

            return {
                'status': status,
                'message': message,
                'performance_metrics': mock_performance,
                'issues': issues,
                'warnings': warnings
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'性能指标检测失败: {e}'
            }

    def _check_compliance(self, alpha_id: str) -> Dict[str, Any]:
        """检测合规性"""
        try:
            # 合规性检查项
            compliance_checks = {
                'expression_length': True,  # 表达式长度合规
                'operator_usage': True,     # 操作符使用合规
                'data_field_usage': True,   # 数据字段使用合规
                'calculation_complexity': True,  # 计算复杂度合规
                'risk_model_compatibility': True  # 风险模型兼容性
            }

            # 模拟检查结果
            failed_checks = []
            warning_checks = []

            # 这里应该实现真实的合规检查逻辑
            # 暂时使用模拟结果

            if failed_checks:
                return {
                    'status': 'failed',
                    'message': f"合规性检查失败: {', '.join(failed_checks)}",
                    'compliance_results': compliance_checks,
                    'failed_checks': failed_checks
                }
            elif warning_checks:
                return {
                    'status': 'warning',
                    'message': f"合规性检查警告: {', '.join(warning_checks)}",
                    'compliance_results': compliance_checks,
                    'warning_checks': warning_checks
                }
            else:
                return {
                    'status': 'passed',
                    'message': "合规性检查通过",
                    'compliance_results': compliance_checks
                }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'合规性检测失败: {e}'
            }

    def _check_risk_assessment(self, alpha_id: str) -> Dict[str, Any]:
        """风险评估"""
        try:
            # 风险评估指标
            risk_metrics = {
                'var_95': 0.05,         # 95% VaR
                'expected_shortfall': 0.07,  # 期望损失
                'beta_exposure': 0.15,   # Beta暴露
                'sector_concentration': 0.25,  # 行业集中度
                'liquidity_risk': 0.1,  # 流动性风险
                'tail_risk': 0.08       # 尾部风险
            }

            risk_level = 'low'
            risk_warnings = []

            # 评估风险级别
            if risk_metrics['var_95'] > 0.08:
                risk_level = 'high'
                risk_warnings.append("VaR风险较高")
            elif risk_metrics['var_95'] > 0.06:
                risk_level = 'medium'
                risk_warnings.append("VaR风险中等")

            if risk_metrics['sector_concentration'] > 0.4:
                risk_level = 'high' if risk_level != 'high' else risk_level
                risk_warnings.append("行业集中度过高")

            if risk_metrics['liquidity_risk'] > 0.15:
                risk_warnings.append("流动性风险较高")

            if risk_level == 'high':
                status = 'failed'
                message = f"风险水平过高: {', '.join(risk_warnings)}"
            elif risk_level == 'medium' or risk_warnings:
                status = 'warning'
                message = f"风险需要关注: {', '.join(risk_warnings) if risk_warnings else '中等风险'}"
            else:
                status = 'passed'
                message = "风险水平可接受"

            return {
                'status': status,
                'message': message,
                'risk_level': risk_level,
                'risk_metrics': risk_metrics,
                'risk_warnings': risk_warnings
            }

        except Exception as e:
            return {
                'status': 'error',
                'message': f'风险评估失败: {e}'
            }

    def _generate_batch_statistics(self, detailed_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成批次统计信息"""
        stats = {
            'check_type_statistics': {},
            'failure_analysis': {},
            'performance_distribution': {},
            'correlation_analysis': {}
        }

        # 按检测类型统计
        for alpha_id, result in detailed_results.items():
            if 'check_results' in result:
                for check_type, check_result in result['check_results'].items():
                    if check_type not in stats['check_type_statistics']:
                        stats['check_type_statistics'][check_type] = {
                            'passed': 0, 'failed': 0, 'warning': 0, 'error': 0
                        }

                    status = check_result.get('status', 'unknown')
                    if status in stats['check_type_statistics'][check_type]:
                        stats['check_type_statistics'][check_type][status] += 1

        # 失败原因分析
        failure_reasons = {}
        for alpha_id, result in detailed_results.items():
            if result.get('overall_status') == 'failed':
                for issue in result.get('issues', []):
                    reason = issue.split(':')[0] if ':' in issue else issue
                    failure_reasons[reason] = failure_reasons.get(
                        reason, 0) + 1

        stats['failure_analysis'] = {
            'common_failure_reasons': failure_reasons,
            'total_failures': sum(failure_reasons.values())
        }

        return stats

    def _generate_recommendations(self, detailed_results: Dict[str, Any]) -> List[Dict[str, Any]]:
        """生成改进建议"""
        recommendations = []

        # 分析共同问题并提供建议
        common_issues = {}
        for alpha_id, result in detailed_results.items():
            for issue in result.get('issues', []):
                issue_type = issue.split(':')[0] if ':' in issue else issue
                common_issues[issue_type] = common_issues.get(
                    issue_type, 0) + 1

        # 根据常见问题生成建议
        for issue_type, count in common_issues.items():
            if count >= len(detailed_results) * 0.3:  # 30%以上的Alpha有此问题
                if 'self_correlation' in issue_type.lower():
                    recommendations.append({
                        'type': 'correlation',
                        'priority': 'high',
                        'description': '多个因子存在高自相关性问题',
                        'suggestion': '建议检查因子构造方法，避免使用过于相似的基础数据或操作符',
                        'affected_count': count
                    })
                elif 'performance' in issue_type.lower():
                    recommendations.append({
                        'type': 'performance',
                        'priority': 'medium',
                        'description': '多个因子存在性能问题',
                        'suggestion': '建议优化因子参数，或重新评估数据源质量',
                        'affected_count': count
                    })

        # 添加通用建议
        total_alphas = len(detailed_results)
        passed_count = sum(1 for r in detailed_results.values()
                           if r.get('overall_status') == 'passed')
        pass_rate = passed_count / total_alphas if total_alphas > 0 else 0

        if pass_rate < 0.5:
            recommendations.append({
                'type': 'general',
                'priority': 'high',
                'description': f'整体通过率较低: {pass_rate:.1%}',
                'suggestion': '建议重新审视因子生成策略和质量控制流程',
                'affected_count': total_alphas
            })

        return recommendations

    def generate_quality_report(self,
                                check_results: Dict[str, Any],
                                output_format: str = 'dict') -> Any:
        """
        生成质量检测报告

        Args:
            check_results: 检测结果
            output_format: 输出格式 ('dict', 'json', 'html')

        Returns:
            格式化的报告
        """
        report = {
            'report_metadata': {
                'generated_at': datetime.now().isoformat(),
                'total_alphas_checked': check_results['summary']['total_alphas'],
                'report_version': '1.0'
            },
            'executive_summary': {
                'overall_pass_rate': (check_results['summary']['passed_count'] /
                                      check_results['summary']['total_alphas'] * 100
                                      if check_results['summary']['total_alphas'] > 0 else 0),
                'key_findings': self._extract_key_findings(check_results),
                'recommendations_count': len(check_results['recommendations'])
            },
            'detailed_analysis': check_results['batch_statistics'],
            'recommendations': check_results['recommendations'],
            'individual_results': check_results['detailed_results']
        }

        if output_format == 'json':
            return json.dumps(report, indent=2, ensure_ascii=False)
        elif output_format == 'html':
            return self._generate_html_report(report)
        else:
            return report

    def _extract_key_findings(self, check_results: Dict[str, Any]) -> List[str]:
        """提取关键发现"""
        findings = []

        summary = check_results['summary']
        total = summary['total_alphas']

        if total > 0:
            pass_rate = summary['passed_count'] / total * 100
            findings.append(f"总体通过率: {pass_rate:.1f}%")

            if summary['failed_count'] > 0:
                fail_rate = summary['failed_count'] / total * 100
                findings.append(f"失败率: {fail_rate:.1f}%")

            if summary['warning_count'] > 0:
                warning_rate = summary['warning_count'] / total * 100
                findings.append(f"警告率: {warning_rate:.1f}%")

        # 添加主要问题类型
        if 'failure_analysis' in check_results.get('batch_statistics', {}):
            failure_analysis = check_results['batch_statistics']['failure_analysis']
            common_reasons = failure_analysis.get('common_failure_reasons', {})
            if common_reasons:
                top_reason = max(common_reasons.items(), key=lambda x: x[1])
                findings.append(
                    f"主要失败原因: {top_reason[0]} ({top_reason[1]}个Alpha)")

        return findings

    def _generate_html_report(self, report: Dict[str, Any]) -> str:
        """生成HTML格式报告"""
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>因子质量检测报告</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { background-color: #f0f0f0; padding: 20px; border-radius: 5px; }
                .summary { margin: 20px 0; }
                .section { margin: 20px 0; border: 1px solid #ddd; padding: 15px; }
                .metric { display: inline-block; margin: 10px; padding: 10px; background: #f9f9f9; }
                .passed { color: green; }
                .failed { color: red; }
                .warning { color: orange; }
                .recommendations { background: #fff3cd; padding: 15px; border-radius: 5px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>因子质量检测报告</h1>
                <p>生成时间: {generated_at}</p>
                <p>检测Alpha数量: {total_alphas}</p>
            </div>
            
            <div class="summary section">
                <h2>执行摘要</h2>
                <div class="metric passed">通过率: {pass_rate:.1f}%</div>
                <h3>关键发现:</h3>
                <ul>
                    {key_findings}
                </ul>
            </div>
            
            <div class="recommendations section">
                <h2>建议事项</h2>
                {recommendations}
            </div>
        </body>
        </html>
        """

        # 填充模板
        key_findings_html = ''.join(
            [f"<li>{finding}</li>" for finding in report['executive_summary']['key_findings']])
        recommendations_html = ''.join([
            f"<p><strong>{rec['type'].upper()}:</strong> {rec['description']} - {rec['suggestion']}</p>"
            for rec in report['recommendations']
        ])

        return html_template.format(
            generated_at=report['report_metadata']['generated_at'],
            total_alphas=report['report_metadata']['total_alphas_checked'],
            pass_rate=report['executive_summary']['overall_pass_rate'],
            key_findings=key_findings_html,
            recommendations=recommendations_html
        )
