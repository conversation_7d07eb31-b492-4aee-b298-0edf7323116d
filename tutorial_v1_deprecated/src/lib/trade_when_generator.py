"""
TradeWhen生成器 - 重构自machine_lib.py的trade_when_factory逻辑
支持不同地区的交易条件因子生成
"""

from typing import List, Dict, Any, Optional, Tuple
import json
from .simple_logger import get_logger

logger = get_logger(__name__)


class TradeWhenGenerator:
    """TradeWhen生成器 - 生成交易条件因子"""

    def __init__(self):
        # 通用开仓事件
        self.open_events = [
            "ts_arg_max(volume, 5) == 0",
            "ts_corr(close, volume, 20) < 0",
            "ts_corr(close, volume, 5) < 0",
            "ts_mean(volume,10)>ts_mean(volume,60)",
            "group_rank(ts_std_dev(returns,60), sector) > 0.7",
            "ts_zscore(returns,60) > 2",
            "ts_arg_min(volume, 5) > 3",
            "ts_std_dev(returns, 5) > ts_std_dev(returns, 20)",
            "ts_arg_max(close, 5) == 0",
            "ts_arg_max(close, 20) == 0",
            "ts_corr(close, volume, 5) > 0",
            "ts_corr(close, volume, 5) > 0.3",
            "ts_corr(close, volume, 5) > 0.5",
            "ts_corr(close, volume, 20) > 0",
            "ts_corr(close, volume, 20) > 0.3",
            "ts_corr(close, volume, 20) > 0.5"
        ]

        # 通用平仓事件
        self.exit_events_delay1 = [
            "abs(returns) > 0.1",
            "-1",
            "days_from_last_change(ern3_pre_reptime) > 20"
        ]

        self.exit_events_other = [
            "abs(returns) > 0.1",
            "-1"
        ]

        # 地区特定事件
        self.region_events = {
            'usa': [
                "rank(rp_css_business) > 0.8",
                "ts_rank(rp_css_business, 22) > 0.8",
                "rank(vec_avg(mws82_sentiment)) > 0.8",
                "ts_rank(vec_avg(mws82_sentiment),22) > 0.8",
                "rank(vec_avg(nws48_ssc)) > 0.8",
                "ts_rank(vec_avg(nws48_ssc),22) > 0.8",
                "rank(vec_avg(mws50_ssc)) > 0.8",
                "ts_rank(vec_avg(mws50_ssc),22) > 0.8",
                "ts_rank(vec_sum(scl12_alltype_buzzvec),22) > 0.9",
                "pcr_oi_270 < 1",
                "pcr_oi_270 > 1"
            ],

            'asi': [
                "rank(vec_avg(mws38_score)) > 0.8",
                "ts_rank(vec_avg(mws38_score),22) > 0.8"
            ],

            'eur': [
                "rank(rp_css_business) > 0.8",
                "ts_rank(rp_css_business, 22) > 0.8",
                "rank(vec_avg(oth429_research_reports_fundamental_keywords_4_method_2_pos)) > 0.8",
                "ts_rank(vec_avg(oth429_research_reports_fundamental_keywords_4_method_2_pos),22) > 0.8",
                "rank(vec_avg(mws84_sentiment)) > 0.8",
                "ts_rank(vec_avg(mws84_sentiment),22) > 0.8",
                "rank(vec_avg(mws85_sentiment)) > 0.8",
                "ts_rank(vec_avg(mws85_sentiment),22) > 0.8",
                "rank(mdl110_analyst_sentiment) > 0.8",
                "ts_rank(mdl110_analyst_sentiment, 22) > 0.8",
                "rank(vec_avg(nws3_scores_posnormscr)) > 0.8",
                "ts_rank(vec_avg(nws3_scores_posnormscr),22) > 0.8",
                "rank(vec_avg(mws36_sentiment_words_positive)) > 0.8",
                "ts_rank(vec_avg(mws36_sentiment_words_positive),22) > 0.8"
            ],

            'glb': [
                "rank(vec_avg(mdl109_news_sent_1m)) > 0.8",
                "ts_rank(vec_avg(mdl109_news_sent_1m),22) > 0.8",
                "rank(vec_avg(nws20_ssc)) > 0.8",
                "ts_rank(vec_avg(nws20_ssc),22) > 0.8",
                "vec_avg(nws20_ssc) > 0",
                "rank(vec_avg(nws20_bee)) > 0.8",
                "ts_rank(vec_avg(nws20_bee),22) > 0.8",
                "rank(vec_avg(nws20_qmb)) > 0.8",
                "ts_rank(vec_avg(nws20_qmb),22) > 0.8"
            ]
        }

    def trade_when_factory(self,
                           op: str,
                           field: str,
                           region: str = "usa",
                           delay: int = 1) -> List[str]:
        """
        生成trade_when因子 - 重构自machine_lib.py

        Args:
            op: 操作符（通常为'trade_when'）
            field: 基础字段表达式
            region: 地区标识
            delay: 延迟天数

        Returns:
            trade_when因子表达式列表
        """
        output = []
        region_lower = region.lower()

        # 获取开仓事件
        open_events = self._get_open_events(field)

        # 获取平仓事件
        exit_events = (self.exit_events_delay1 if delay == 1
                       else self.exit_events_other)

        # 获取地区特定事件
        region_specific_events = self.region_events.get(region_lower, [])

        # 组合开仓和平仓事件
        all_open_events = open_events + region_specific_events

        for open_event in all_open_events:
            for exit_event in exit_events:
                trade_when_expr = f"{op}({field}, {open_event}, {exit_event})"
                output.append(trade_when_expr)

        logger.info(f"为地区 {region} 生成了 {len(output)} 个trade_when因子")
        return output

    def _get_open_events(self, field: str) -> List[str]:
        """获取包含字段的开仓事件"""
        events = self.open_events.copy()

        # 添加与字段相关的特定回归事件
        field_specific_events = [
            f"ts_regression(returns, {field}, 5, lag = 0, rettype = 2) > 0",
            f"ts_regression(returns, {field}, 20, lag = 0, rettype = 2) > 0"
        ]

        events.extend(field_specific_events)
        return events

    def generate_trade_when_variants(self,
                                     base_factors: List[str],
                                     region: str = "usa",
                                     max_variants_per_factor: int = 10) -> Dict[str, List[str]]:
        """
        为基础因子生成trade_when变体

        Args:
            base_factors: 基础因子列表
            region: 地区标识
            max_variants_per_factor: 每个因子的最大变体数

        Returns:
            因子变体字典
        """
        variants = {}

        for factor in base_factors:
            factor_variants = self.trade_when_factory(
                "trade_when", factor, region, 1)

            # 限制变体数量
            if len(factor_variants) > max_variants_per_factor:
                # 按优先级排序并选择最佳变体
                prioritized_variants = self._prioritize_variants(
                    factor_variants, factor)
                factor_variants = prioritized_variants[:max_variants_per_factor]

            variants[factor] = factor_variants

        total_variants = sum(len(v) for v in variants.values())
        logger.info(
            f"为 {len(base_factors)} 个基础因子生成了 {total_variants} 个trade_when变体")

        return variants

    def _prioritize_variants(self, variants: List[str], base_factor: str) -> List[str]:
        """为变体排序，优先级高的在前"""
        scored_variants = []

        for variant in variants:
            score = self._calculate_variant_score(variant, base_factor)
            scored_variants.append((score, variant))

        scored_variants.sort(reverse=True)
        return [variant for _, variant in scored_variants]

    def _calculate_variant_score(self, variant: str, base_factor: str) -> float:
        """计算变体的优先级分数"""
        score = 0.5  # 基础分数

        # 基于开仓条件的评分
        if "ts_corr(close, volume" in variant:
            score += 0.2  # 价量相关性是重要指标

        if "rank(" in variant or "ts_rank(" in variant:
            score += 0.15  # 排名类指标通常较稳定

        if "sentiment" in variant:
            score += 0.1  # 情绪类指标在某些市场有效

        # 基于平仓条件的评分
        if "abs(returns) > 0.1" in variant:
            score += 0.1  # 止损条件

        # 基于地区适配性的评分
        if "vec_avg" in variant:
            score += 0.05  # 向量平均通常较稳定

        # 惩罚过于复杂的条件
        complexity = variant.count('(') + variant.count('>')
        if complexity > 6:
            score -= 0.1

        return score

    def create_event_library(self) -> Dict[str, Dict[str, List[str]]]:
        """创建事件库，便于管理和扩展"""
        return {
            'open_events': {
                'volume_based': [
                    "ts_arg_max(volume, 5) == 0",
                    "ts_arg_min(volume, 5) > 3",
                    "ts_mean(volume,10)>ts_mean(volume,60)"
                ],
                'price_volume_correlation': [
                    "ts_corr(close, volume, 20) < 0",
                    "ts_corr(close, volume, 5) < 0",
                    "ts_corr(close, volume, 5) > 0",
                    "ts_corr(close, volume, 5) > 0.3",
                    "ts_corr(close, volume, 5) > 0.5",
                    "ts_corr(close, volume, 20) > 0",
                    "ts_corr(close, volume, 20) > 0.3",
                    "ts_corr(close, volume, 20) > 0.5"
                ],
                'volatility_based': [
                    "group_rank(ts_std_dev(returns,60), sector) > 0.7",
                    "ts_zscore(returns,60) > 2",
                    "ts_std_dev(returns, 5) > ts_std_dev(returns, 20)"
                ],
                'price_action': [
                    "ts_arg_max(close, 5) == 0",
                    "ts_arg_max(close, 20) == 0"
                ]
            },
            'exit_events': {
                'risk_management': [
                    "abs(returns) > 0.1"
                ],
                'time_based': [
                    "-1",
                    "days_from_last_change(ern3_pre_reptime) > 20"
                ]
            },
            'region_events': self.region_events
        }

    def optimize_trade_when_parameters(self,
                                       trade_when_factors: List[str],
                                       optimization_targets: Dict[str, float]) -> List[Dict[str, Any]]:
        """
        优化trade_when因子的参数

        Args:
            trade_when_factors: trade_when因子列表
            optimization_targets: 优化目标

        Returns:
            优化结果列表
        """
        optimized_results = []

        for factor in trade_when_factors:
            result = self._optimize_single_trade_when(
                factor, optimization_targets)
            optimized_results.append(result)

        logger.info(f"优化了 {len(trade_when_factors)} 个trade_when因子")
        return optimized_results

    def _optimize_single_trade_when(self,
                                    factor: str,
                                    targets: Dict[str, float]) -> Dict[str, Any]:
        """优化单个trade_when因子"""
        # 解析trade_when结构
        parts = self._parse_trade_when_structure(factor)

        result = {
            'original_factor': factor,
            'optimized_factor': factor,
            'optimization_suggestions': [],
            'confidence_score': 0.5,
            'risk_assessment': 'medium'
        }

        if not parts:
            return result

        # 分析开仓条件
        open_condition = parts.get('open_condition', '')
        if self._is_high_frequency_condition(open_condition):
            result['optimization_suggestions'].append(
                "开仓条件可能过于频繁，建议增加阈值"
            )
            result['risk_assessment'] = 'high'

        # 分析平仓条件
        exit_condition = parts.get('exit_condition', '')
        if exit_condition == '-1':
            result['optimization_suggestions'].append(
                "使用永久持有策略，建议添加风险控制条件"
            )

        # 计算置信度
        result['confidence_score'] = self._calculate_trade_when_confidence(
            parts)

        return result

    def _parse_trade_when_structure(self, factor: str) -> Dict[str, str]:
        """解析trade_when因子结构"""
        try:
            if not factor.startswith('trade_when('):
                return {}

            # 移除外层函数名
            content = factor[11:-1]  # 移除 'trade_when(' 和 ')'

            # 简单分割（实际应该用更复杂的解析器）
            parts = content.split(', ', 2)

            if len(parts) >= 3:
                return {
                    'base_factor': parts[0].strip(),
                    'open_condition': parts[1].strip(),
                    'exit_condition': parts[2].strip()
                }
        except Exception as e:
            logger.warning(f"解析trade_when结构失败: {factor}, 错误: {e}")

        return {}

    def _is_high_frequency_condition(self, condition: str) -> bool:
        """判断是否为高频交易条件"""
        high_freq_patterns = [
            "== 0",  # 等于0的条件通常触发频繁
            "> 0.9",  # 极高阈值
            "< 0.1"   # 极低阈值
        ]

        return any(pattern in condition for pattern in high_freq_patterns)

    def _calculate_trade_when_confidence(self, parts: Dict[str, str]) -> float:
        """计算trade_when因子的置信度"""
        score = 0.5

        base_factor = parts.get('base_factor', '')
        open_condition = parts.get('open_condition', '')
        exit_condition = parts.get('exit_condition', '')

        # 基础因子质量
        if any(pattern in base_factor for pattern in ['rank', 'ts_rank', 'group_rank']):
            score += 0.1

        # 开仓条件质量
        if 'ts_corr' in open_condition:
            score += 0.1
        if 'rank(' in open_condition:
            score += 0.05

        # 平仓条件质量
        if 'abs(returns)' in exit_condition:
            score += 0.1  # 有止损
        elif exit_condition == '-1':
            score -= 0.1  # 无止损风险

        return min(max(score, 0.0), 1.0)

    def validate_trade_when_syntax(self, factors: List[str]) -> Dict[str, List[str]]:
        """验证trade_when因子的语法"""
        result = {
            'valid': [],
            'invalid': [],
            'warnings': []
        }

        for factor in factors:
            if self._validate_single_trade_when(factor):
                result['valid'].append(factor)
            else:
                result['invalid'].append(factor)

        logger.info(
            f"验证结果: {len(result['valid'])} 有效, {len(result['invalid'])} 无效")
        return result

    def _validate_single_trade_when(self, factor: str) -> bool:
        """验证单个trade_when因子的语法"""
        # 基本格式检查
        if not factor.startswith('trade_when(') or not factor.endswith(')'):
            return False

        # 括号匹配检查
        if factor.count('(') != factor.count(')'):
            return False

        # 参数数量检查（应该有3个参数）
        try:
            content = factor[11:-1]
            # 简单计数逗号（实际应该更精确）
            comma_count = content.count(',')
            if comma_count < 2:
                return False
        except Exception:
            return False

        return True

    def get_trade_when_statistics(self, factors: List[str]) -> Dict[str, Any]:
        """获取trade_when因子的统计信息"""
        stats = {
            'total_count': len(factors),
            'event_type_distribution': {},
            'complexity_distribution': {},
            'region_usage': {}
        }

        # 事件类型分布
        event_types = ['volume', 'correlation',
                       'volatility', 'sentiment', 'regression']
        for event_type in event_types:
            count = sum(1 for f in factors if event_type in f.lower())
            if count > 0:
                stats['event_type_distribution'][event_type] = count

        # 复杂度分布
        complexities = [f.count('(') + f.count(',') for f in factors]
        if complexities:
            stats['complexity_distribution'] = {
                'min': min(complexities),
                'max': max(complexities),
                'avg': sum(complexities) / len(complexities)
            }

        return stats
