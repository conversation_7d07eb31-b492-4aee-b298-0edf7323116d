"""
因子队列管理器 - 核心队列管理功能
基于设计文档中的AlphaQueue实现
"""

import json
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from collections import defaultdict
from peewee import fn

from .models import (
    AlphaFactorQueueModel,
    AlphaBatchModel,
    AlphaProcessingLogModel,
    UserAccountModel,
    db
)
from .simple_logger import get_logger

logger = get_logger(__name__)


class AlphaQueue:
    """因子队列管理器"""

    def __init__(self):
        self.db = db

    def add_factors_batch(self,
                          expressions: List[str],
                          source_dataset: str,
                          stage: str,
                          tags: List[str] = None,
                          priority: int = 0,
                          **processing_params) -> str:
        """批量添加因子到队列"""

        batch_id = f"{source_dataset}_{stage}_{int(time.time())}"
        tags_json = json.dumps(tags) if tags else None

        # 创建批次记录
        batch = AlphaBatchModel.create(
            batch_id=batch_id,
            batch_name=f"{source_dataset} {stage} batch",
            source_dataset=source_dataset,
            stage=stage,
            total_factors=len(expressions),
            pending_count=len(expressions),
            status='pending',
            started_at=datetime.now()
        )

        # 批量创建因子记录
        factors = []
        for expr in expressions:
            factors.append(AlphaFactorQueueModel(
                factor_expression=expr,
                source_dataset=source_dataset,
                stage=stage,
                status='pending',
                priority=priority,
                tags=tags_json,
                batch_id=batch_id,
                submitted_at=datetime.now(),
                **processing_params
            ))

        # 使用批量插入提高性能
        with self.db.atomic():
            AlphaFactorQueueModel.bulk_create(factors, batch_size=100)

        logger.info(f"添加了 {len(expressions)} 个因子到队列 (批次: {batch_id})")
        return batch_id

    def get_pending_factors(self, stage: str = None, limit: int = 50,
                            dataset: str = None, account: Optional[UserAccountModel] = None) -> List[AlphaFactorQueueModel]:
        """获取待处理因子 - 优化版本"""
        query = (AlphaFactorQueueModel
                 .select()
                 .where(AlphaFactorQueueModel.status == 'pending'))

        if stage:
            query = query.where(AlphaFactorQueueModel.stage == stage)

        if dataset:
            query = query.where(
                AlphaFactorQueueModel.source_dataset == dataset)

        if account:
            query = query.where(
                AlphaFactorQueueModel.assigned_account == account)

        # 按优先级和提交时间排序
        query = query.order_by(
            AlphaFactorQueueModel.priority.desc(),
            AlphaFactorQueueModel.submitted_at.asc()
        )

        return list(query.limit(limit))

    def update_factor_status(self, factor_id: int, status: str,
                             alpha_id: str = None, error: str = None, **result_data):
        """更新因子状态 - 增强版本"""

        try:
            factor = AlphaFactorQueueModel.get_by_id(factor_id)
        except AlphaFactorQueueModel.DoesNotExist:
            logger.error(f"因子 {factor_id} 不存在")
            return

        # 更新基本状态
        factor.status = status

        if status == 'processing':
            factor.started_at = datetime.now()
        elif status in ['completed', 'failed']:
            factor.completed_at = datetime.now()

        # 更新结果数据
        if alpha_id:
            factor.alpha_id = alpha_id

        if error:
            factor.last_error = error
            factor.error_type = self._classify_error(error)
            factor.retry_count += 1

        # 更新其他结果数据 (sharpe, fitness, turnover等)
        for key, value in result_data.items():
            if hasattr(factor, key):
                setattr(factor, key, value)

        factor.save()

        # 更新批次统计
        if factor.batch_id:
            self._update_batch_stats(factor.batch_id)

        # 记录处理日志
        self._log_processing(factor, status, error)

    def get_queue_stats(self) -> Dict[str, Any]:
        """获取队列统计信息 - 完整版本"""

        stats = {}

        # 按状态统计
        status_stats = (AlphaFactorQueueModel
                        .select(AlphaFactorQueueModel.status, fn.COUNT().alias('count'))
                        .group_by(AlphaFactorQueueModel.status))

        stats['by_status'] = {stat.status: stat.count for stat in status_stats}

        # 按阶段统计
        stage_stats = (AlphaFactorQueueModel
                       .select(AlphaFactorQueueModel.stage, fn.COUNT().alias('count'))
                       .group_by(AlphaFactorQueueModel.stage))

        stats['by_stage'] = {stat.stage: stat.count for stat in stage_stats}

        # 按数据集统计
        dataset_stats = (AlphaFactorQueueModel
                         .select(AlphaFactorQueueModel.source_dataset, fn.COUNT().alias('count'))
                         .group_by(AlphaFactorQueueModel.source_dataset))

        stats['by_dataset'] = {
            stat.source_dataset: stat.count for stat in dataset_stats}

        # 批次统计
        batch_stats = (AlphaBatchModel
                       .select(AlphaBatchModel.status, fn.COUNT().alias('count'))
                       .group_by(AlphaBatchModel.status))

        stats['by_batch_status'] = {
            stat.status: stat.count for stat in batch_stats}

        # 计算处理速度
        hour_ago = datetime.now() - timedelta(hours=1)
        completed_last_hour = (AlphaFactorQueueModel
                               .select()
                               .where(
                                   AlphaFactorQueueModel.status == 'completed',
                                   AlphaFactorQueueModel.completed_at >= hour_ago
                               )
                               .count())

        stats['processing_speed'] = {
            'factors_per_hour': completed_last_hour,
            'last_updated': datetime.now().isoformat()
        }

        return stats

    def cleanup_old_records(self, days: int = 7):
        """清理旧的已完成记录"""

        cutoff_date = datetime.now() - timedelta(days=days)

        # 删除旧的已完成记录
        deleted_count = (AlphaFactorQueueModel
                         .delete()
                         .where(
                             AlphaFactorQueueModel.status == 'completed',
                             AlphaFactorQueueModel.completed_at < cutoff_date
                         )
                         .execute())

        logger.info(f"清理了 {deleted_count} 条旧记录")
        return deleted_count

    def get_factors_by_batch(self, batch_id: str) -> List[AlphaFactorQueueModel]:
        """根据批次ID获取因子列表"""
        return list(AlphaFactorQueueModel
                    .select()
                    .where(AlphaFactorQueueModel.batch_id == batch_id)
                    .order_by(AlphaFactorQueueModel.priority.desc()))

    def get_batch_info(self, batch_id: str) -> Optional[AlphaBatchModel]:
        """获取批次信息"""
        try:
            return AlphaBatchModel.get(AlphaBatchModel.batch_id == batch_id)
        except AlphaBatchModel.DoesNotExist:
            return None

    def cancel_batch(self, batch_id: str) -> int:
        """取消批次 - 将pending状态的因子标记为cancelled"""
        cancelled_count = (AlphaFactorQueueModel
                           .update(status='cancelled')
                           .where(
                               AlphaFactorQueueModel.batch_id == batch_id,
                               AlphaFactorQueueModel.status == 'pending'
                           )
                           .execute())

        # 更新批次状态
        batch = self.get_batch_info(batch_id)
        if batch:
            batch.status = 'cancelled'
            batch.save()

        logger.info(f"取消了批次 {batch_id} 中的 {cancelled_count} 个待处理因子")
        return cancelled_count

    def retry_failed_factors(self, stage: str = None, limit: int = 100) -> int:
        """重试失败的因子"""
        query = (AlphaFactorQueueModel
                 .select()
                 .where(
                     AlphaFactorQueueModel.status == 'failed',
                     AlphaFactorQueueModel.retry_count < AlphaFactorQueueModel.max_retries
                 ))

        if stage:
            query = query.where(AlphaFactorQueueModel.stage == stage)

        failed_factors = list(query.limit(limit))

        retry_count = 0
        for factor in failed_factors:
            factor.status = 'pending'
            factor.retry_count += 1
            factor.last_error = None
            factor.save()
            retry_count += 1

        logger.info(f"重新排队了 {retry_count} 个失败的因子")
        return retry_count

    def get_processing_summary(self, hours: int = 24) -> Dict:
        """获取处理摘要"""
        since = datetime.now() - timedelta(hours=hours)

        # 统计各状态的因子数量
        summary = {
            'period_hours': hours,
            'period_start': since.isoformat(),
            'completed': 0,
            'failed': 0,
            'processing': 0,
            'pending': 0,
            'success_rate': 0.0,
            'avg_processing_time': 0.0,
            'top_datasets': [],
            'error_distribution': {}
        }

        # 基础统计
        for status in ['completed', 'failed', 'processing', 'pending']:
            count = (AlphaFactorQueueModel
                     .select()
                     .where(
                         AlphaFactorQueueModel.status == status,
                         AlphaFactorQueueModel.created_at >= since
                     )
                     .count())
            summary[status] = count

        # 成功率
        total_processed = summary['completed'] + summary['failed']
        if total_processed > 0:
            summary['success_rate'] = summary['completed'] / total_processed

        # 平均处理时间
        completed_factors = (AlphaFactorQueueModel
                             .select()
                             .where(
                                 AlphaFactorQueueModel.status == 'completed',
                                 AlphaFactorQueueModel.completed_at >= since,
                                 AlphaFactorQueueModel.started_at.is_null(
                                     False)
                             ))

        processing_times = []
        for factor in completed_factors:
            if factor.started_at and factor.completed_at:
                duration = (factor.completed_at -
                            factor.started_at).total_seconds()
                processing_times.append(duration)

        if processing_times:
            summary['avg_processing_time'] = sum(
                processing_times) / len(processing_times)

        # 数据集排名
        dataset_stats = defaultdict(int)
        for factor in AlphaFactorQueueModel.select().where(
            AlphaFactorQueueModel.created_at >= since
        ):
            dataset_stats[factor.source_dataset] += 1

        summary['top_datasets'] = sorted(
            dataset_stats.items(),
            key=lambda x: x[1],
            reverse=True
        )[:5]

        # 错误分布
        error_stats = defaultdict(int)
        for factor in AlphaFactorQueueModel.select().where(
            AlphaFactorQueueModel.status == 'failed',
            AlphaFactorQueueModel.created_at >= since,
            AlphaFactorQueueModel.error_type.is_null(False)
        ):
            error_stats[factor.error_type] += 1

        summary['error_distribution'] = dict(error_stats)

        return summary

    def _update_batch_stats(self, batch_id: str):
        """更新批次统计"""
        if not batch_id:
            return

        # 重新计算批次统计
        stats = (AlphaFactorQueueModel
                 .select(AlphaFactorQueueModel.status, fn.COUNT().alias('count'))
                 .where(AlphaFactorQueueModel.batch_id == batch_id)
                 .group_by(AlphaFactorQueueModel.status))

        status_counts = {stat.status: stat.count for stat in stats}

        try:
            # 更新批次记录
            batch = AlphaBatchModel.get(AlphaBatchModel.batch_id == batch_id)
            batch.pending_count = status_counts.get('pending', 0)
            batch.processing_count = status_counts.get('processing', 0)
            batch.completed_count = status_counts.get('completed', 0)
            batch.failed_count = status_counts.get('failed', 0)

            # 更新批次状态
            if batch.completed_count + batch.failed_count == batch.total_factors:
                batch.status = 'completed'
                batch.completed_at = datetime.now()
            elif batch.processing_count > 0:
                batch.status = 'running'
            elif batch.pending_count > 0:
                batch.status = 'pending'

            batch.save()

        except AlphaBatchModel.DoesNotExist:
            logger.warning(f"批次 {batch_id} 不存在")

    def _log_processing(self, factor: AlphaFactorQueueModel, status: str, error: str = None):
        """记录处理日志"""
        try:
            AlphaProcessingLogModel.create(
                factor_queue=factor,
                operation=factor.stage,
                status=status,
                error_message=error,
                duration=self._calculate_duration(factor)
            )
        except Exception as e:
            logger.error(f"记录处理日志失败: {e}")

    def _calculate_duration(self, factor: AlphaFactorQueueModel) -> Optional[float]:
        """计算处理耗时"""
        if factor.started_at and factor.completed_at:
            return (factor.completed_at - factor.started_at).total_seconds()
        return None

    def _classify_error(self, error: str) -> str:
        """错误类型分类"""
        error_lower = error.lower()

        if 'timeout' in error_lower:
            return 'timeout'
        elif 'rate limit' in error_lower or 'rate_limit' in error_lower:
            return 'rate_limit'
        elif 'session' in error_lower:
            return 'session_error'
        elif 'network' in error_lower or 'connection' in error_lower:
            return 'network_error'
        elif 'api' in error_lower:
            return 'api_error'
        elif 'permission' in error_lower or 'forbidden' in error_lower:
            return 'permission_error'
        else:
            return 'unknown'


class QueueMonitor:
    """队列监控器 - 提供队列状态监控功能"""

    def __init__(self, queue: AlphaQueue):
        self.queue = queue
        self.logger = get_logger("QueueMonitor")

    def check_queue_health(self) -> Dict:
        """检查队列健康状态"""
        health_info = {
            'healthy': True,
            'issues': [],
            'recommendations': [],
            'timestamp': datetime.now().isoformat()
        }

        stats = self.queue.get_queue_stats()

        # 检查是否有长时间处理中的任务
        hour_ago = datetime.now() - timedelta(hours=2)
        stale_processing = (AlphaFactorQueueModel
                            .select()
                            .where(
                                AlphaFactorQueueModel.status == 'processing',
                                AlphaFactorQueueModel.started_at < hour_ago
                            )
                            .count())

        if stale_processing > 0:
            health_info['healthy'] = False
            health_info['issues'].append(f"有 {stale_processing} 个因子处理时间超过2小时")
            health_info['recommendations'].append("检查处理器是否正常运行")

        # 检查失败率
        total_processed = stats['by_status'].get(
            'completed', 0) + stats['by_status'].get('failed', 0)
        if total_processed > 10:
            failure_rate = stats['by_status'].get(
                'failed', 0) / total_processed
            if failure_rate > 0.2:  # 失败率超过20%
                health_info['healthy'] = False
                health_info['issues'].append(f"失败率过高: {failure_rate:.1%}")
                health_info['recommendations'].append("检查API连接和账户状态")

        # 检查队列积压
        pending_count = stats['by_status'].get('pending', 0)
        if pending_count > 1000:
            health_info['issues'].append(f"队列积压严重: {pending_count} 个待处理任务")
            health_info['recommendations'].append("考虑增加处理器或优化处理速度")

        return health_info

    def get_performance_metrics(self) -> Dict:
        """获取性能指标"""
        return self.queue.get_processing_summary(24)
