"""
统一日志配置模块

使用 rich 组件提供美观的日志输出，为整个项目提供统一的日志配置。
"""

import logging
import sys
import json
import os
from pathlib import Path
from typing import Optional, Dict, Any
from rich.console import Console
from rich.logging import RichHandler
from rich.traceback import install
from rich.theme import Theme

# 安装 rich 的异常追踪美化
install(show_locals=True)


class ProjectLogger:
    """项目日志管理器"""

    _loggers = {}
    _configured = False
    _config = None

    @classmethod
    def get_logger(cls, name: str = None) -> logging.Logger:
        """
        获取日志记录器

        Args:
            name: 日志记录器名称，默认使用调用模块的名称

        Returns:
            配置好的日志记录器
        """
        if name is None:
            # 自动获取调用模块的名称
            import inspect
            frame = inspect.currentframe().f_back
            module = inspect.getmodule(frame)
            if module:
                name = module.__name__
            else:
                name = "unknown"

        # 如果已存在，直接返回
        if name in cls._loggers:
            return cls._loggers[name]

        # 配置全局日志设置（只配置一次）
        if not cls._configured:
            cls._configure_logging()
            cls._configured = True

        # 创建新的日志记录器
        logger = logging.getLogger(name)
        cls._loggers[name] = logger

        return logger

    @classmethod
    def _load_config(cls) -> Dict[str, Any]:
        """加载日志配置"""
        if cls._config is not None:
            return cls._config

        # 默认配置
        default_config = {
            "level": "INFO",
            "console": {
                "enabled": True,
                "show_time": True,
                "show_level": True,
                "show_path": True,
                "rich_tracebacks": True
            },
            "file": {
                "enabled": False,
                "path": "logs/app.log",
                "level": "INFO"
            },
            "theme": {
                "info": "cyan",
                "warning": "yellow",
                "error": "red bold",
                "critical": "red bold reverse",
                "debug": "dim"
            }
        }

        # 尝试从配置文件加载
        try:
            current_dir = os.path.dirname(os.path.abspath(__file__))
            config_file = os.path.join(os.path.dirname(
                current_dir), 'config', 'logging.json')

            if os.path.exists(config_file):
                with open(config_file, 'r', encoding='utf-8') as f:
                    config_data = json.load(f)
                    logging_config = config_data.get('logging', {})
                    # 合并配置
                    default_config.update(logging_config)
        except Exception:
            # 配置文件加载失败时使用默认配置
            pass

        cls._config = default_config
        return cls._config

    @classmethod
    def _configure_logging(cls):
        """配置全局日志设置"""
        config = cls._load_config()

        # 创建主题
        theme_config = config.get('theme', {})
        theme = Theme(theme_config)

        # 创建 console
        console = Console(theme=theme, stderr=True)

        # 配置根日志记录器
        root_logger = logging.getLogger()
        level = config.get('level', 'INFO')
        root_logger.setLevel(getattr(logging, level.upper(), logging.INFO))

        # 清除现有的处理器
        root_logger.handlers.clear()

        # 配置控制台处理器
        console_config = config.get('console', {})
        if console_config.get('enabled', True):
            # 获取时间格式配置
            time_format = console_config.get('time_format', '%H:%M:%S')

            rich_handler = RichHandler(
                console=console,
                show_time=console_config.get('show_time', True),
                show_level=console_config.get('show_level', True),
                show_path=console_config.get('show_path', True),
                markup=True,
                rich_tracebacks=console_config.get('rich_tracebacks', True),
                tracebacks_show_locals=True,
                omit_repeated_times=False,  # 确保每条日志都显示时间
                log_time_format=f"[{time_format}]",  # 设置时间格式
                enable_link_path=True,  # 启用文件路径链接
                locals_max_length=10,  # 限制本地变量显示长度
                locals_max_string=80,   # 限制字符串显示长度
                keywords=[]  # 可以添加关键词高亮
            )

            # 设置包含文件名和行号的格式化器
            # Rich会自动处理文件路径的显示，我们保持简单的格式
            rich_handler.setFormatter(
                logging.Formatter(
                    fmt="%(message)s",
                    datefmt=time_format
                )
            )

            root_logger.addHandler(rich_handler)

        # 配置文件处理器
        file_config = config.get('file', {})
        if file_config.get('enabled', False):
            log_path = file_config.get('path', 'logs/app.log')
            # 如果路径不是绝对路径，则相对于项目根目录
            if not os.path.isabs(log_path):
                # 获取项目根目录（src的上级目录）
                current_dir = os.path.dirname(os.path.abspath(__file__))
                project_root = os.path.dirname(os.path.dirname(current_dir))
                log_path = os.path.join(project_root, log_path)
            log_file = Path(log_path)
            file_level = file_config.get('level', 'INFO')
            cls.add_file_handler(log_file, file_level)

        # 防止日志重复
        root_logger.propagate = False

    @classmethod
    def set_level(cls, level: str):
        """
        设置日志级别

        Args:
            level: 日志级别 ("DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL")
        """
        numeric_level = getattr(logging, level.upper(), logging.INFO)
        logging.getLogger().setLevel(numeric_level)

    @classmethod
    def add_file_handler(cls, log_file: Path, level: str = "INFO"):
        """
        添加文件日志处理器

        Args:
            log_file: 日志文件路径
            level: 日志级别
        """
        # 确保日志目录存在
        log_file.parent.mkdir(parents=True, exist_ok=True)

        # 创建文件处理器
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(getattr(logging, level.upper(), logging.INFO))

        # 设置文件日志格式（包含文件名和行号）
        # 从配置中获取文件时间格式
        config = cls._load_config()
        file_config = config.get('file', {})
        file_time_format = file_config.get('time_format', '%Y-%m-%d %H:%M:%S')

        # 格式化器包含文件名和行号信息
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s',
            datefmt=file_time_format
        )
        file_handler.setFormatter(formatter)

        # 添加到根日志记录器
        logging.getLogger().addHandler(file_handler)

    @classmethod
    def reload_config(cls):
        """重新加载配置"""
        cls._config = None
        cls._configured = False
        # 下次获取logger时会重新配置


# 便捷函数
def get_logger(name: str = None) -> logging.Logger:
    """
    获取日志记录器的便捷函数

    Args:
        name: 日志记录器名称，默认使用调用模块的名称

    Returns:
        配置好的日志记录器
    """
    return ProjectLogger.get_logger(name)


def set_log_level(level: str):
    """
    设置日志级别的便捷函数

    Args:
        level: 日志级别 ("DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL")
    """
    ProjectLogger.set_level(level)


def add_file_logging(log_file: str, level: str = "INFO"):
    """
    添加文件日志的便捷函数

    Args:
        log_file: 日志文件路径（相对于项目根目录）
        level: 日志级别
    """
    # 如果路径不是绝对路径，则相对于项目根目录
    if not os.path.isabs(log_file):
        # 获取项目根目录（src的上级目录）
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(current_dir))
        log_file = os.path.join(project_root, log_file)

    ProjectLogger.add_file_handler(Path(log_file), level)


def reload_logging_config():
    """
    重新加载日志配置的便捷函数
    """
    ProjectLogger.reload_config()


# 为向后兼容，创建默认的模块级别日志记录器
logger = get_logger(__name__)


class UserInterface:
    """用户界面输出类 - 统一管理用户交互输出"""

    def __init__(self, logger_name: str = None):
        self.logger = get_logger(logger_name)
        self.console = Console()

    def info(self, message: str, log_to_file: bool = True):
        """显示信息消息"""
        self.console.print(message)
        if log_to_file:
            self.logger.info(message)

    def success(self, message: str, log_to_file: bool = True):
        """显示成功消息"""
        self.console.print(f"✅ {message}", style="green")
        if log_to_file:
            self.logger.info(message)

    def warning(self, message: str, log_to_file: bool = True):
        """显示警告消息"""
        self.console.print(f"⚠️ {message}", style="yellow")
        if log_to_file:
            self.logger.warning(message)

    def error(self, message: str, log_to_file: bool = True):
        """显示错误消息"""
        self.console.print(f"❌ {message}", style="red")
        if log_to_file:
            self.logger.error(message)

    def progress(self, message: str, log_to_file: bool = False):
        """显示进度消息"""
        self.console.print(f"🔄 {message}", style="blue")
        if log_to_file:
            self.logger.info(message)

    def section_header(self, title: str, width: int = 60):
        """显示区块标题"""
        header = f"\n{'=' * width}\n{title}\n{'=' * width}"
        self.console.print(header, style="bold cyan")
        self.logger.info(title)

    def section_subheader(self, title: str, width: int = 50):
        """显示子区块标题"""
        header = f"\n{title}\n{'-' * width}"
        self.console.print(header, style="bold")
        self.logger.debug(title)

    def step(self, step_num: int, total_steps: int, description: str):
        """显示步骤信息"""
        message = f"步骤 {step_num}/{total_steps}: {description}"
        self.console.print(f"\n📋 {message}", style="bold blue")
        self.logger.info(message)

    def checklist_item(self, item: str, status: str = "success"):
        """显示检查清单项目"""
        icons = {
            "success": "✓",
            "error": "❌",
            "pending": "⏳",
            "warning": "⚠️"
        }
        styles = {
            "success": "green",
            "error": "red",
            "pending": "yellow",
            "warning": "yellow"
        }
        icon = icons.get(status, "•")
        style = styles.get(status, "white")
        self.console.print(f"  {icon} {item}", style=style)
        self.logger.debug(f"{status.upper()}: {item}")

    def help_section(self, title: str, items: list):
        """显示帮助区块"""
        self.console.print(f"\n📖 {title}", style="bold")
        self.console.print("=" * 50)
        for item in items:
            self.console.print(f"  {item}")
        self.console.print("=" * 50)
        self.logger.debug(f"{title} - {len(items)} items")


def get_ui(name: str = None) -> UserInterface:
    """
    获取用户界面输出实例

    Args:
        name: 日志记录器名称

    Returns:
        UserInterface: 用户界面输出实例
    """
    return UserInterface(name)
