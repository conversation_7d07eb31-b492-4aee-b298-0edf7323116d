"""
智能提交器 - 重构自s7.py的逻辑
支持因子的智能提交、超时管理、重试机制和状态监控
"""

from typing import List, Dict, Any, Optional, Tuple, Callable
import asyncio
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
import time
import json
import os
from concurrent.futures import ThreadPoolExecutor
from .simple_logger import get_logger

logger = get_logger(__name__)


class SmartSubmitter:
    """智能提交器 - 处理因子的智能提交和监控"""

    def __init__(self, api_client=None):
        self.api_client = api_client

        # 提交配置
        self.submit_config = {
            'max_timeout_minutes': 30,
            'max_retries': 5,
            'retry_delay_seconds': 3,
            'status_check_interval': 10,
            'batch_size': 10,
            'concurrent_submissions': 3,
            'progress_report_interval': 300  # 5分钟报告一次进度
        }

        # 状态码映射
        self.status_codes = {
            200: 'success',
            201: 'submitted',
            403: 'compliance_failed',
            408: 'timeout',
            429: 'rate_limited',
            500: 'server_error'
        }

    async def submit_alpha_batch(self,
                                 alpha_data: List[Dict[str, Any]],
                                 priority_sort: bool = True,
                                 progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        批量提交Alpha因子 - 重构自submit_batch逻辑

        Args:
            alpha_data: Alpha数据列表，包含id, self_corr等信息
            priority_sort: 是否按优先级排序
            progress_callback: 进度回调函数

        Returns:
            提交结果统计
        """
        if not alpha_data:
            return {'success': False, 'message': '没有Alpha需要提交'}

        # 数据预处理
        df = pd.DataFrame(alpha_data)
        if priority_sort and 'self_corr' in df.columns:
            df = df.sort_values('self_corr', ascending=True)

        logger.info(f"开始批量提交 {len(df)} 个Alpha因子")

        results = {
            'total_count': len(df),
            'success_count': 0,
            'failed_count': 0,
            'timeout_count': 0,
            'compliance_failed_count': 0,
            'successful_ids': [],
            'failed_details': [],
            'processing_time': 0
        }

        start_time = datetime.now()

        # 分批并发提交
        semaphore = asyncio.Semaphore(
            self.submit_config['concurrent_submissions'])
        tasks = []

        for idx, row in df.iterrows():
            task = self._submit_single_alpha_with_semaphore(
                semaphore, row, idx + 1, len(df), progress_callback
            )
            tasks.append(task)

        # 执行所有提交任务
        submission_results = await asyncio.gather(*tasks, return_exceptions=True)

        # 处理结果
        for idx, result in enumerate(submission_results):
            if isinstance(result, Exception):
                logger.error(f"提交第 {idx + 1} 个Alpha时出现异常: {result}")
                results['failed_count'] += 1
                results['failed_details'].append({
                    'alpha_id': df.iloc[idx]['id'] if 'id' in df.columns else f'alpha_{idx}',
                    'error': str(result),
                    'status': 'exception'
                })
            else:
                # 统计结果
                if result['status'] == 'success':
                    results['success_count'] += 1
                    results['successful_ids'].append(result['alpha_id'])
                elif result['status'] == 'timeout':
                    results['timeout_count'] += 1
                    results['failed_details'].append(result)
                elif result['status'] == 'compliance_failed':
                    results['compliance_failed_count'] += 1
                    results['failed_details'].append(result)
                else:
                    results['failed_count'] += 1
                    results['failed_details'].append(result)

        results['processing_time'] = (
            datetime.now() - start_time).total_seconds()

        # 生成最终报告
        success_rate = results['success_count'] / results['total_count'] * 100
        logger.info(f"批量提交完成: 成功 {results['success_count']}/{results['total_count']} "
                    f"({success_rate:.1f}%), 耗时 {results['processing_time']:.1f}秒")

        return results

    async def _submit_single_alpha_with_semaphore(self,
                                                  semaphore: asyncio.Semaphore,
                                                  alpha_row: pd.Series,
                                                  index: int,
                                                  total: int,
                                                  progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """带信号量控制的单个Alpha提交"""
        async with semaphore:
            return await self._submit_single_alpha(alpha_row, index, total, progress_callback)

    async def _submit_single_alpha(self,
                                   alpha_row: pd.Series,
                                   index: int,
                                   total: int,
                                   progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """
        提交单个Alpha因子 - 重构自submit_alpha逻辑

        Args:
            alpha_row: Alpha数据行
            index: 当前索引
            total: 总数
            progress_callback: 进度回调

        Returns:
            提交结果
        """
        alpha_id = alpha_row['id'] if 'id' in alpha_row else f'alpha_{index}'
        self_corr = alpha_row.get('self_corr', 0)

        logger.info(
            f"🚀 开始提交第 {index} 个 | ID: {alpha_id} | 自相关性: {self_corr:.4f}")

        result = {
            'alpha_id': alpha_id,
            'index': index,
            'self_corr': self_corr,
            'status': 'failed',
            'message': '',
            'submission_time': 0,
            'retry_count': 0
        }

        start_time = datetime.now()

        try:
            # 第一阶段：发起提交
            submission_status = await self._initiate_submission(alpha_id)
            result['retry_count'] = submission_status.get('retry_count', 0)

            if submission_status['status'] not in ['submitted', 'success']:
                result.update(submission_status)
                return result

            # 第二阶段：监控提交状态
            monitoring_result = await self._monitor_submission_status(alpha_id)
            result.update(monitoring_result)

        except Exception as e:
            logger.error(f"提交Alpha {alpha_id} 时发生异常: {e}")
            result.update({
                'status': 'error',
                'message': f'提交过程异常: {str(e)}'
            })

        finally:
            result['submission_time'] = (
                datetime.now() - start_time).total_seconds()

            # 记录结果
            if result['status'] == 'success':
                logger.info(
                    f"✅ 成功！ID: {alpha_id} | 耗时: {result['submission_time']:.1f}秒")
            else:
                logger.warning(
                    f"❌ 失败！ID: {alpha_id} | 状态: {result['status']} | 原因: {result['message']}")

            # 调用进度回调
            if progress_callback:
                try:
                    progress_callback(index, total, result)
                except Exception as e:
                    logger.warning(f"进度回调执行失败: {e}")

        return result

    async def _initiate_submission(self, alpha_id: str) -> Dict[str, Any]:
        """发起Alpha提交"""
        submit_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}/submit"
        retry_count = 0

        for attempt in range(1, self.submit_config['max_retries'] + 1):
            try:
                if not self.api_client:
                    logger.error("API客户端未初始化，无法提交")
                    raise ConnectionError("API client is not available.")

                # 使用API客户端
                response = await self.api_client.post(submit_url)
                status_code = response.status

                retry_count = attempt - 1

                if status_code == 201:
                    return {
                        'status': 'submitted',
                        'message': '提交请求已发起',
                        'retry_count': retry_count
                    }
                elif status_code == 403:
                    return {
                        'status': 'compliance_failed',
                        'message': '提交被永久拒绝（合规性问题）',
                        'retry_count': retry_count
                    }
                else:
                    logger.warning(f"提交尝试 {attempt}: 状态码 {status_code}")
                    if attempt < self.submit_config['max_retries']:
                        await asyncio.sleep(self.submit_config['retry_delay_seconds'])
                        continue
                    else:
                        return {
                            'status': 'failed',
                            'message': f'提交失败，最终状态码: {status_code}',
                            'retry_count': retry_count
                        }

            except Exception as e:
                logger.warning(f"提交尝试 {attempt} 异常: {e}")
                if attempt < self.submit_config['max_retries']:
                    await asyncio.sleep(self.submit_config['retry_delay_seconds'])
                    continue
                else:
                    return {
                        'status': 'error',
                        'message': f'提交过程异常: {str(e)}',
                        'retry_count': retry_count
                    }

        return {
            'status': 'failed',
            'message': '超出最大重试次数',
            'retry_count': retry_count
        }

    async def _monitor_submission_status(self, alpha_id: str) -> Dict[str, Any]:
        """监控提交状态"""
        submit_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}/submit"
        timeout_time = datetime.now(
        ) + timedelta(minutes=self.submit_config['max_timeout_minutes'])
        last_status_time = datetime.now()

        while datetime.now() < timeout_time:
            try:
                elapsed = datetime.now() - last_status_time

                # 定期显示等待状态
                if elapsed.total_seconds() >= self.submit_config['progress_report_interval']:
                    elapsed_total = datetime.now() - (timeout_time -
                                                      timedelta(minutes=self.submit_config['max_timeout_minutes']))
                    logger.info(
                        f"⏳ Alpha {alpha_id} 持续提交中 | 已等待 {elapsed_total.total_seconds() // 60:.0f} 分钟")
                    last_status_time = datetime.now()

                if self.api_client:
                    # 使用API客户端检查状态
                    response = await self.api_client.get(submit_url)
                    status_code = response.status

                    if 'Retry-After' in response.headers:
                        retry_after = float(response.headers['Retry-After'])
                        await asyncio.sleep(retry_after)
                        continue
                else:
                    # 模拟状态检查
                    await asyncio.sleep(1)
                    status_code = 200  # 模拟成功

                if status_code == 200:
                    return {
                        'status': 'success',
                        'message': '提交成功'
                    }
                elif status_code == 403:
                    return {
                        'status': 'compliance_failed',
                        'message': '合规检查未通过'
                    }
                else:
                    # 其他状态码，继续等待
                    await asyncio.sleep(self.submit_config['status_check_interval'])

            except Exception as e:
                logger.warning(f"状态检查异常: {e}")
                await asyncio.sleep(self.submit_config['status_check_interval'])

        return {
            'status': 'timeout',
            'message': f'超时终止（{self.submit_config["max_timeout_minutes"]}分钟未完成）'
        }

    def create_submission_plan(self,
                               alpha_data: List[Dict[str, Any]],
                               strategy: str = 'quality_first') -> Dict[str, Any]:
        """
        创建提交计划

        Args:
            alpha_data: Alpha数据列表
            strategy: 提交策略 ('quality_first', 'balanced', 'fast')

        Returns:
            提交计划
        """
        df = pd.DataFrame(alpha_data)

        plan = {
            'strategy': strategy,
            'total_alphas': len(df),
            'estimated_time_minutes': 0,
            'batches': [],
            'priority_weights': {},
            'submission_order': []
        }

        # 根据策略调整参数
        if strategy == 'quality_first':
            # 质量优先：按自相关性排序，小批次，高并发
            if 'self_corr' in df.columns:
                df = df.sort_values('self_corr', ascending=True)
            batch_size = 5
            concurrent = 2
            plan['priority_weights'] = {
                'self_corr': 0.4, 'sharpe_ratio': 0.3, 'returns': 0.3}

        elif strategy == 'balanced':
            # 平衡策略：中等批次和并发
            batch_size = 10
            concurrent = 3
            plan['priority_weights'] = {
                'self_corr': 0.3, 'submission_time': 0.3, 'complexity': 0.4}

        elif strategy == 'fast':
            # 快速策略：大批次，高并发，简单因子优先
            if 'complexity' in df.columns:
                df = df.sort_values('complexity', ascending=True)
            batch_size = 20
            concurrent = 5
            plan['priority_weights'] = {
                'complexity': 0.5, 'submission_time': 0.5}

        # 创建批次
        for i in range(0, len(df), batch_size):
            batch_df = df.iloc[i:i + batch_size]
            batch = {
                'batch_id': f'batch_{i // batch_size + 1}',
                'alpha_ids': batch_df['id'].tolist() if 'id' in batch_df.columns else [f'alpha_{j}' for j in range(i, i + len(batch_df))],
                'size': len(batch_df),
                # 估算每个Alpha 1.5分钟
                'estimated_time_minutes': len(batch_df) * 1.5,
                'concurrent_limit': concurrent
            }
            plan['batches'].append(batch)

        plan['estimated_time_minutes'] = sum(
            batch['estimated_time_minutes'] for batch in plan['batches'])
        plan['submission_order'] = [alpha_id for batch in plan['batches']
                                    for alpha_id in batch['alpha_ids']]

        logger.info(
            f"创建提交计划: 策略={strategy}, 批次={len(plan['batches'])}, 预估时间={plan['estimated_time_minutes']:.1f}分钟")

        return plan

    async def execute_submission_plan(self,
                                      plan: Dict[str, Any],
                                      alpha_data: List[Dict[str, Any]],
                                      progress_callback: Optional[Callable] = None) -> Dict[str, Any]:
        """执行提交计划"""
        logger.info(f"开始执行提交计划: {plan['strategy']} 策略")

        overall_results = {
            'plan_strategy': plan['strategy'],
            'total_batches': len(plan['batches']),
            'batch_results': [],
            'overall_statistics': {
                'total_count': plan['total_alphas'],
                'success_count': 0,
                'failed_count': 0,
                'total_time': 0
            }
        }

        start_time = datetime.now()

        # 按批次执行
        for batch_idx, batch in enumerate(plan['batches']):
            logger.info(
                f"执行批次 {batch_idx + 1}/{len(plan['batches'])}: {batch['batch_id']}")

            # 准备批次数据
            batch_alpha_data = [
                alpha for alpha in alpha_data
                if alpha.get('id', f'alpha_{alpha_data.index(alpha)}') in batch['alpha_ids']
            ]

            # 临时调整并发配置
            original_concurrent = self.submit_config['concurrent_submissions']
            self.submit_config['concurrent_submissions'] = batch['concurrent_limit']

            try:
                # 执行批次提交
                batch_result = await self.submit_alpha_batch(
                    batch_alpha_data,
                    priority_sort=False,  # 已在计划中排序
                    progress_callback=progress_callback
                )

                batch_result['batch_id'] = batch['batch_id']
                batch_result['batch_index'] = batch_idx + 1
                overall_results['batch_results'].append(batch_result)

                # 更新整体统计
                overall_results['overall_statistics']['success_count'] += batch_result['success_count']
                overall_results['overall_statistics']['failed_count'] += batch_result['failed_count']

                logger.info(
                    f"批次 {batch['batch_id']} 完成: 成功 {batch_result['success_count']}/{batch_result['total_count']}")

            except Exception as e:
                logger.error(f"批次 {batch['batch_id']} 执行失败: {e}")
                batch_result = {
                    'batch_id': batch['batch_id'],
                    'batch_index': batch_idx + 1,
                    'success': False,
                    'error': str(e)
                }
                overall_results['batch_results'].append(batch_result)
                overall_results['overall_statistics']['failed_count'] += len(
                    batch['alpha_ids'])

            finally:
                # 恢复原始并发配置
                self.submit_config['concurrent_submissions'] = original_concurrent

                # 批次间间隔
                if batch_idx < len(plan['batches']) - 1:
                    await asyncio.sleep(5)

        overall_results['overall_statistics']['total_time'] = (
            datetime.now() - start_time).total_seconds()

        # 生成执行报告
        success_rate = (overall_results['overall_statistics']['success_count'] /
                        overall_results['overall_statistics']['total_count'] * 100)

        logger.info(f"提交计划执行完成: 总成功率 {success_rate:.1f}%, "
                    f"总耗时 {overall_results['overall_statistics']['total_time']:.1f}秒")

        return overall_results

    def generate_submission_report(self,
                                   results: Dict[str, Any],
                                   output_path: Optional[str] = None) -> str:
        """生成提交报告"""
        report = {
            'submission_summary': {
                'timestamp': datetime.now().isoformat(),
                'strategy': results.get('plan_strategy', 'unknown'),
                'total_alphas': results['overall_statistics']['total_count'],
                'success_count': results['overall_statistics']['success_count'],
                'failed_count': results['overall_statistics']['failed_count'],
                'success_rate': results['overall_statistics']['success_count'] / results['overall_statistics']['total_count'] * 100,
                'total_time_minutes': results['overall_statistics']['total_time'] / 60
            },
            'batch_details': [],
            'recommendations': []
        }

        # 处理批次详情
        if 'batch_results' in results:
            for batch_result in results['batch_results']:
                batch_detail = {
                    'batch_id': batch_result.get('batch_id', 'unknown'),
                    'success_count': batch_result.get('success_count', 0),
                    'total_count': batch_result.get('total_count', 0),
                    'processing_time_minutes': batch_result.get('processing_time', 0) / 60,
                    'success_rate': (batch_result.get('success_count', 0) /
                                     batch_result.get('total_count', 1) * 100)
                }
                report['batch_details'].append(batch_detail)

        # 生成建议
        success_rate = report['submission_summary']['success_rate']
        if success_rate < 50:
            report['recommendations'].append("成功率较低，建议检查因子质量和网络连接")
        if success_rate >= 80:
            report['recommendations'].append("提交表现良好，可以考虑增加批次大小")

        # 格式化报告文本
        report_text = f"""
=== Alpha因子提交报告 ===
生成时间: {report['submission_summary']['timestamp']}
提交策略: {report['submission_summary']['strategy']}

总体结果:
- 总Alpha数量: {report['submission_summary']['total_alphas']}
- 成功提交: {report['submission_summary']['success_count']}
- 提交失败: {report['submission_summary']['failed_count']}
- 成功率: {report['submission_summary']['success_rate']:.1f}%
- 总耗时: {report['submission_summary']['total_time_minutes']:.1f} 分钟

批次详情:
"""

        for batch in report['batch_details']:
            report_text += f"- {batch['batch_id']}: {batch['success_count']}/{batch['total_count']} " \
                f"({batch['success_rate']:.1f}%) 耗时 {batch['processing_time_minutes']:.1f}分钟\n"

        if report['recommendations']:
            report_text += f"\n建议事项:\n"
            for rec in report['recommendations']:
                report_text += f"- {rec}\n"

        # 保存到文件
        if output_path:
            try:
                with open(output_path, 'w', encoding='utf-8') as f:
                    f.write(report_text)
                logger.info(f"提交报告已保存到: {output_path}")
            except Exception as e:
                logger.error(f"保存报告失败: {e}")

        return report_text

    def get_submission_statistics(self,
                                  results: Dict[str, Any]) -> Dict[str, Any]:
        """获取提交统计信息"""
        stats = {
            'performance_metrics': {},
            'error_analysis': {},
            'time_analysis': {},
            'recommendation_scores': {}
        }

        # 性能指标
        total = results['overall_statistics']['total_count']
        success = results['overall_statistics']['success_count']
        failed = results['overall_statistics']['failed_count']

        stats['performance_metrics'] = {
            'success_rate': success / total * 100 if total > 0 else 0,
            'failure_rate': failed / total * 100 if total > 0 else 0,
            'average_processing_time': results['overall_statistics']['total_time'] / total if total > 0 else 0
        }

        # 错误分析
        if 'batch_results' in results:
            error_types = {}
            for batch_result in results['batch_results']:
                if 'failed_details' in batch_result:
                    for failed_item in batch_result['failed_details']:
                        error_type = failed_item.get('status', 'unknown')
                        error_types[error_type] = error_types.get(
                            error_type, 0) + 1

            stats['error_analysis'] = error_types

        # 时间分析
        if 'batch_results' in results:
            processing_times = [
                batch.get('processing_time', 0)
                for batch in results['batch_results']
                if 'processing_time' in batch
            ]

            if processing_times:
                stats['time_analysis'] = {
                    'min_batch_time': min(processing_times),
                    'max_batch_time': max(processing_times),
                    'avg_batch_time': sum(processing_times) / len(processing_times)
                }

        return stats
