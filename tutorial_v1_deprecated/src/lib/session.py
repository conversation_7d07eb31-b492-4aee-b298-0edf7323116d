"""
会话管理模块

提供智能的会话管理功能，包括：
- 基于token失效时间的智能缓存
- 会话有效性检查
- 自动重连
- 混合策略：结合token时间和定期检查
"""

import requests
import time
from typing import Optional, Dict
from .db_config_reader import get_config_reader
from .logger import get_logger

# 获取当前模块的日志记录器
logger = get_logger()


class SessionManager:
    """智能会话管理器 - 混合策略实现"""

    def __init__(self, db_path: str = None):
        """
        初始化会话管理器

        Args:
            db_path: 数据库文件路径
        """
        self.db_path = db_path
        self.config_reader = get_config_reader(db_path)
        self._session: Optional[requests.Session] = None
        self._token_info: Dict = {}
        self._last_check_time = 0
        self._check_interval = 300  # 5分钟检查间隔（备用保障）
        self._buffer_time = 60      # 提前60秒刷新token
        self._fallback_mode = False  # 是否降级到定期检查模式

    def _create_new_session(self) -> tuple[requests.Session, Dict]:
        """
        创建新的认证会话

        Returns:
            tuple[requests.Session, Dict]: 已认证的会话对象和token信息
        """
        from .login import login  # 避免循环导入

        logger.info("创建新的认证会话...")
        session, token_info = login(self.db_path)
        logger.info("新会话创建成功")

        # 重置降级模式
        self._fallback_mode = False

        return session, token_info

    def _is_token_near_expiry(self) -> bool:
        """
        检查token是否即将失效

        Returns:
            bool: True如果token即将失效或无token信息
        """
        if not self._token_info or 'expiry_time' not in self._token_info:
            return True  # 无token信息，保守处理

        current_time = time.time()
        time_until_expiry = self._token_info['expiry_time'] - current_time

        if time_until_expiry <= self._buffer_time:
            logger.debug(f"Token将在 {time_until_expiry:.1f} 秒后失效，即将刷新")
            return True

        logger.debug(f"Token还有 {time_until_expiry:.1f} 秒有效")
        return False

    def _should_check_session_fallback(self) -> bool:
        """
        判断是否需要进行定期检查（降级模式）

        Returns:
            bool: 是否需要检查
        """
        current_time = time.time()
        return (current_time - self._last_check_time) > self._check_interval

    def _check_session_validity(self, session: requests.Session) -> bool:
        """
        检查会话是否仍然有效

        Args:
            session: 要检查的会话对象

        Returns:
            bool: 会话是否有效
        """
        try:
            # 使用轻量级的 API 接口检查连通性
            check_url = self.config_reader.get_url('alpha_user')

            logger.debug("检查会话有效性...")
            response = session.get(check_url, timeout=10)

            # 检查响应状态
            if response.status_code == 200:
                logger.debug("会话仍然有效")
                return True
            elif response.status_code == 401:
                logger.warning("会话已失效（401 未授权）")
                # token提前失效，启用降级模式
                self._fallback_mode = True
                return False
            elif "API rate limit exceeded" in response.text:
                logger.warning("API 速率限制，但会话有效")
                return True  # 速率限制不代表会话失效
            else:
                logger.warning(f"会话检查返回状态码: {response.status_code}")
                return False

        except requests.RequestException as e:
            logger.error(f"会话有效性检查失败: {e}")
            return False

    def get_session(self) -> requests.Session:
        """
        获取有效的会话对象 - 智能混合策略

        Returns:
            requests.Session: 有效的会话对象
        """
        current_time = time.time()

        # 如果没有缓存的会话，创建新的
        if self._session is None:
            logger.info("没有缓存的会话，创建新会话")
            self._session, self._token_info = self._create_new_session()
            self._last_check_time = current_time
            return self._session

        # 智能混合策略决策
        need_refresh = False
        reason = ""

        # 策略1：基于token失效时间（主要策略）
        if not self._fallback_mode and self._token_info:
            if self._is_token_near_expiry():
                need_refresh = True
                reason = "token即将失效"

        # 策略2：降级到定期检查（备用策略）
        elif self._fallback_mode or not self._token_info:
            if self._should_check_session_fallback():
                logger.debug("使用降级模式：定期检查会话有效性")
                if not self._check_session_validity(self._session):
                    need_refresh = True
                    reason = "定期检查发现会话失效"
                else:
                    self._last_check_time = current_time
                    return self._session

        # 如果需要刷新会话
        if need_refresh:
            logger.info(f"刷新会话：{reason}")
            self._session, self._token_info = self._create_new_session()
            self._last_check_time = current_time
            return self._session

        # 会话仍然有效，直接使用
        logger.debug("使用缓存的有效会话")
        return self._session

    def invalidate_session(self):
        """
        手动失效当前会话，强制下次获取时重新登录
        """
        logger.info("手动失效当前会话")
        self._session = None
        self._token_info = {}
        self._last_check_time = 0
        self._fallback_mode = False

    def set_check_interval(self, seconds: int):
        """
        设置会话检查间隔

        Args:
            seconds: 检查间隔（秒）
        """
        self._check_interval = seconds
        logger.info(f"会话检查间隔设置为 {seconds} 秒")

    def set_buffer_time(self, seconds: int):
        """
        设置token刷新缓冲时间

        Args:
            seconds: 缓冲时间（秒）
        """
        self._buffer_time = seconds
        logger.info(f"Token刷新缓冲时间设置为 {seconds} 秒")

    def get_session_info(self) -> Dict:
        """
        获取当前会话信息

        Returns:
            Dict: 会话状态信息
        """
        info = {
            'has_session': self._session is not None,
            'has_token_info': bool(self._token_info),
            'fallback_mode': self._fallback_mode,
            'last_check_time': self._last_check_time
        }

        if self._token_info and 'expiry_time' in self._token_info:
            current_time = time.time()
            info['token_expires_in'] = max(
                0, self._token_info['expiry_time'] - current_time)
            info['token_near_expiry'] = self._is_token_near_expiry()

        return info


# 全局会话管理器实例
_global_session_manager: Optional[SessionManager] = None


def get_session_manager(db_path: str = None) -> SessionManager:
    """
    获取全局会话管理器实例

    Args:
        db_path: 数据库文件路径

    Returns:
        SessionManager: 会话管理器实例
    """
    global _global_session_manager

    if _global_session_manager is None:
        _global_session_manager = SessionManager(db_path)
        logger.debug("创建全局会话管理器")

    return _global_session_manager


def get_cached_session(db_path: str = None) -> requests.Session:
    """
    获取缓存的会话对象（智能混合策略）

    Args:
        db_path: 数据库文件路径

    Returns:
        requests.Session: 有效的会话对象
    """
    manager = get_session_manager(db_path)
    return manager.get_session()


def invalidate_cached_session():
    """
    失效缓存的会话，强制下次重新登录
    """
    global _global_session_manager
    if _global_session_manager:
        _global_session_manager.invalidate_session()


def get_session_info() -> Dict:
    """
    获取当前会话状态信息

    Returns:
        Dict: 会话状态信息
    """
    global _global_session_manager
    if _global_session_manager:
        return _global_session_manager.get_session_info()
    return {'has_session': False}
