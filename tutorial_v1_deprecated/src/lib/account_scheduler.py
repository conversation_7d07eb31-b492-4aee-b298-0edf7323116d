"""
多账户调度器 - 实现负载均衡和健康检查
支持账户轮换、故障恢复、4维度健康评分（登录/错误率/响应时间/限流）
"""

import asyncio
from typing import Dict, List, Optional, Any, Tuple, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import statistics
import random
import json
from collections import deque, defaultdict
from .logger import get_logger
from .db_config_reader import get_config_reader
from .api_client import RobustAPIClient

logger = get_logger(__name__)


class AccountStatus(Enum):
    """账户状态枚举"""
    HEALTHY = "healthy"
    WARNING = "warning"
    DEGRADED = "degraded"
    FAILED = "failed"
    DISABLED = "disabled"
    RECOVERING = "recovering"


class LoadBalancingStrategy(Enum):
    """负载均衡策略"""
    ROUND_ROBIN = "round_robin"
    WEIGHTED_ROUND_ROBIN = "weighted_round_robin"
    LEAST_CONNECTIONS = "least_connections"
    HEALTH_BASED = "health_based"
    RANDOM = "random"


@dataclass
class HealthMetrics:
    """账户健康指标"""
    # 4维度评分
    login_score: float = 100.0      # 登录成功率
    error_rate_score: float = 100.0  # 错误率评分
    response_time_score: float = 100.0  # 响应时间评分
    rate_limit_score: float = 100.0     # 限流状态评分

    # 综合评分
    overall_score: float = 100.0

    # 统计数据
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    login_attempts: int = 0
    successful_logins: int = 0

    # 响应时间统计
    response_times: deque = field(default_factory=lambda: deque(maxlen=100))
    average_response_time: float = 0.0

    # 限流统计
    rate_limit_hits: int = 0
    last_rate_limit_time: Optional[datetime] = None

    # 时间戳
    last_updated: datetime = field(default_factory=datetime.now)
    last_success_time: Optional[datetime] = None
    last_failure_time: Optional[datetime] = None


@dataclass
class AccountInfo:
    """账户信息"""
    account_id: str
    email: str
    password: str
    status: AccountStatus = AccountStatus.HEALTHY
    health_metrics: HealthMetrics = field(default_factory=HealthMetrics)

    # 使用统计
    active_connections: int = 0
    total_tasks_assigned: int = 0
    last_assigned_time: Optional[datetime] = None

    # 恢复控制
    failure_count: int = 0
    last_failure_time: Optional[datetime] = None
    recovery_cooldown_until: Optional[datetime] = None

    # API客户端
    api_client: Optional[RobustAPIClient] = None

    # 配置
    weight: float = 1.0  # 权重，用于加权轮询
    max_concurrent_tasks: int = 10
    priority: int = 0  # 优先级，数值越高优先级越高


class AccountScheduler:
    """多账户调度器"""

    def __init__(self, db_path: Optional[str] = None):
        self.config_reader = get_config_reader(db_path)
        self.accounts: Dict[str, AccountInfo] = {}
        self.strategy = LoadBalancingStrategy.HEALTH_BASED

        # 轮询索引
        self._round_robin_index = 0
        self._weighted_round_robin_weights = []

        # 健康检查配置 - 使用默认值，因为数据库配置侧重于URL和认证
        self.health_check_interval = 5 * 60  # 5分钟
        self.failure_threshold = 3  # 3次失败阈值
        self.recovery_cooldown = 15 * 60  # 15分钟恢复冷却

        # 任务分配跟踪
        self.account_assignments: Dict[str, List[str]] = defaultdict(
            list)  # account_id -> task_ids

        # 监控数据
        self.scheduler_stats = {
            'total_assignments': 0,
            'assignment_failures': 0,
            'account_rotations': 0,
            'health_check_runs': 0,
            'last_health_check': None
        }

        # 停止标志
        self._stop_event = asyncio.Event()

        # 加载配置
        self._load_scheduler_config()

    def _load_scheduler_config(self):
        """加载调度器配置"""
        scheduling_config = self.config.get_section('scheduling')

        strategy_name = scheduling_config.get(
            'load_balancing_strategy', 'health_based')
        try:
            self.strategy = LoadBalancingStrategy(strategy_name)
        except ValueError:
            logger.warning(f"未知的负载均衡策略: {strategy_name}, 使用默认策略")
            self.strategy = LoadBalancingStrategy.HEALTH_BASED

    async def start(self):
        """启动调度器"""
        logger.info("启动多账户调度器")

        # 加载账户
        await self._load_accounts()

        # 启动健康检查循环
        asyncio.create_task(self._health_check_loop())

        logger.info(f"调度器启动完成，加载了 {len(self.accounts)} 个账户")

    async def stop(self):
        """停止调度器"""
        logger.info("停止多账户调度器")
        self._stop_event.set()

        # 关闭所有API客户端
        for account in self.accounts.values():
            if account.api_client:
                await account.api_client.close()

    async def _load_accounts(self):
        """加载账户配置"""
        # 这里应该从数据库或配置文件加载账户信息
        # 暂时使用模拟数据

        # 从配置获取主账户
        try:
            credentials = self.config.get_credentials()
            main_account = AccountInfo(
                account_id="main_account",
                email=credentials['email'],
                password=credentials['password'],
                priority=10,
                weight=2.0
            )

            # 创建API客户端
            main_account.api_client = RobustAPIClient(
                email=main_account.email,
                password=main_account.password
            )
            await main_account.api_client.initialize()

            self.accounts[main_account.account_id] = main_account
            logger.info(f"主账户加载成功: {main_account.email}")

        except Exception as e:
            logger.error(f"加载主账户失败: {e}")

        # 这里可以加载更多账户
        # await self._load_additional_accounts()

    async def assign_account(self, task_id: str, task_requirements: Optional[Dict] = None) -> Optional[AccountInfo]:
        """
        为任务分配账户

        Args:
            task_id: 任务ID
            task_requirements: 任务要求（可选）

        Returns:
            分配的账户信息，如果无可用账户则返回None
        """
        try:
            # 获取可用账户
            available_accounts = self._get_available_accounts(
                task_requirements)

            if not available_accounts:
                logger.warning("没有可用账户进行任务分配")
                self.scheduler_stats['assignment_failures'] += 1
                return None

            # 根据策略选择账户
            selected_account = self._select_account_by_strategy(
                available_accounts)

            if selected_account:
                # 更新账户使用统计
                selected_account.active_connections += 1
                selected_account.total_tasks_assigned += 1
                selected_account.last_assigned_time = datetime.now()

                # 记录分配
                self.account_assignments[selected_account.account_id].append(
                    task_id)
                self.scheduler_stats['total_assignments'] += 1

                logger.debug(
                    f"任务 {task_id} 分配给账户 {selected_account.account_id}")

            return selected_account

        except Exception as e:
            logger.error(f"账户分配失败: {e}")
            self.scheduler_stats['assignment_failures'] += 1
            return None

    async def release_account(self, account_id: str, task_id: str,
                              success: bool, response_time: Optional[float] = None):
        """
        释放账户并更新统计信息

        Args:
            account_id: 账户ID
            task_id: 任务ID
            success: 任务是否成功
            response_time: 响应时间（秒）
        """
        if account_id not in self.accounts:
            logger.warning(f"尝试释放未知账户: {account_id}")
            return

        account = self.accounts[account_id]

        # 更新连接数
        account.active_connections = max(0, account.active_connections - 1)

        # 移除任务分配记录
        if task_id in self.account_assignments[account_id]:
            self.account_assignments[account_id].remove(task_id)

        # 更新健康指标
        await self._update_health_metrics(account, success, response_time)

        logger.debug(
            f"账户 {account_id} 释放，任务 {task_id} 状态: {'成功' if success else '失败'}")

    def _get_available_accounts(self, requirements: Optional[Dict] = None) -> List[AccountInfo]:
        """获取可用账户列表"""
        available = []

        for account in self.accounts.values():
            # 基础可用性检查
            if not self._is_account_available(account):
                continue

            # 检查任务要求
            if requirements and not self._meets_requirements(account, requirements):
                continue

            available.append(account)

        return available

    def _is_account_available(self, account: AccountInfo) -> bool:
        """检查账户是否可用"""
        # 状态检查
        if account.status in [AccountStatus.FAILED, AccountStatus.DISABLED]:
            return False

        # 恢复冷却检查
        if (account.recovery_cooldown_until and
                datetime.now() < account.recovery_cooldown_until):
            return False

        # 连接数检查
        if account.active_connections >= account.max_concurrent_tasks:
            return False

        # 健康分数检查
        if account.health_metrics.overall_score < 30:  # 过低的健康分数
            return False

        return True

    def _meets_requirements(self, account: AccountInfo, requirements: Dict) -> bool:
        """检查账户是否满足任务要求"""
        # 优先级要求
        min_priority = requirements.get('min_priority', 0)
        if account.priority < min_priority:
            return False

        # 健康分数要求
        min_health_score = requirements.get('min_health_score', 50)
        if account.health_metrics.overall_score < min_health_score:
            return False

        # 最大响应时间要求
        max_response_time = requirements.get('max_response_time')
        if (max_response_time and
                account.health_metrics.average_response_time > max_response_time):
            return False

        return True

    def _select_account_by_strategy(self, accounts: List[AccountInfo]) -> Optional[AccountInfo]:
        """根据策略选择账户"""
        if not accounts:
            return None

        if self.strategy == LoadBalancingStrategy.ROUND_ROBIN:
            return self._round_robin_selection(accounts)

        elif self.strategy == LoadBalancingStrategy.WEIGHTED_ROUND_ROBIN:
            return self._weighted_round_robin_selection(accounts)

        elif self.strategy == LoadBalancingStrategy.LEAST_CONNECTIONS:
            return self._least_connections_selection(accounts)

        elif self.strategy == LoadBalancingStrategy.HEALTH_BASED:
            return self._health_based_selection(accounts)

        elif self.strategy == LoadBalancingStrategy.RANDOM:
            return random.choice(accounts)

        else:
            # 默认使用健康度选择
            return self._health_based_selection(accounts)

    def _round_robin_selection(self, accounts: List[AccountInfo]) -> AccountInfo:
        """轮询选择"""
        account = accounts[self._round_robin_index % len(accounts)]
        self._round_robin_index += 1
        return account

    def _weighted_round_robin_selection(self, accounts: List[AccountInfo]) -> AccountInfo:
        """加权轮询选择"""
        # 构建权重列表
        if not self._weighted_round_robin_weights:
            for account in accounts:
                self._weighted_round_robin_weights.extend(
                    [account] * int(account.weight * 10))

        if self._weighted_round_robin_weights:
            account = self._weighted_round_robin_weights[self._round_robin_index % len(
                self._weighted_round_robin_weights)]
            self._round_robin_index += 1
            return account

        return accounts[0]

    def _least_connections_selection(self, accounts: List[AccountInfo]) -> AccountInfo:
        """最少连接选择"""
        return min(accounts, key=lambda a: a.active_connections)

    def _health_based_selection(self, accounts: List[AccountInfo]) -> AccountInfo:
        """基于健康度选择"""
        # 按健康分数和优先级排序
        sorted_accounts = sorted(
            accounts,
            key=lambda a: (a.health_metrics.overall_score, a.priority),
            reverse=True
        )

        # 选择最健康的账户，如果分数相近则随机选择
        best_score = sorted_accounts[0].health_metrics.overall_score
        best_accounts = [a for a in sorted_accounts
                         if abs(a.health_metrics.overall_score - best_score) < 5]

        return random.choice(best_accounts)

    async def _update_health_metrics(self, account: AccountInfo,
                                     success: bool, response_time: Optional[float] = None):
        """更新账户健康指标"""
        metrics = account.health_metrics
        now = datetime.now()

        # 更新基础统计
        metrics.total_requests += 1
        if success:
            metrics.successful_requests += 1
            metrics.last_success_time = now
        else:
            metrics.failed_requests += 1
            metrics.last_failure_time = now
            account.failure_count += 1
            account.last_failure_time = now

        # 更新响应时间
        if response_time is not None:
            metrics.response_times.append(response_time)
            if metrics.response_times:
                metrics.average_response_time = statistics.mean(
                    metrics.response_times)

        # 重新计算4维度分数
        await self._calculate_health_scores(account)

        # 更新账户状态
        self._update_account_status(account)

        metrics.last_updated = now

    async def _calculate_health_scores(self, account: AccountInfo):
        """计算4维度健康分数"""
        metrics = account.health_metrics

        # 1. 登录成功率评分
        if metrics.login_attempts > 0:
            login_success_rate = metrics.successful_logins / metrics.login_attempts
            metrics.login_score = login_success_rate * 100
        else:
            metrics.login_score = 100.0

        # 2. 错误率评分
        if metrics.total_requests > 0:
            error_rate = metrics.failed_requests / metrics.total_requests
            metrics.error_rate_score = max(0, 100 - (error_rate * 100))
        else:
            metrics.error_rate_score = 100.0

        # 3. 响应时间评分
        if metrics.average_response_time > 0:
            # 响应时间越短分数越高，以5秒为基准
            time_score = max(
                0, 100 - (metrics.average_response_time / 5.0 * 100))
            metrics.response_time_score = min(100, time_score)
        else:
            metrics.response_time_score = 100.0

        # 4. 限流状态评分
        if metrics.last_rate_limit_time:
            # 距离上次限流的时间越长分数越高
            time_since_limit = datetime.now() - metrics.last_rate_limit_time
            hours_since = time_since_limit.total_seconds() / 3600
            metrics.rate_limit_score = min(100, hours_since * 20)  # 5小时后满分
        else:
            metrics.rate_limit_score = 100.0

        # 计算综合评分（加权平均）
        weights = {
            'login': 0.3,
            'error_rate': 0.3,
            'response_time': 0.2,
            'rate_limit': 0.2
        }

        metrics.overall_score = (
            metrics.login_score * weights['login'] +
            metrics.error_rate_score * weights['error_rate'] +
            metrics.response_time_score * weights['response_time'] +
            metrics.rate_limit_score * weights['rate_limit']
        )

    def _update_account_status(self, account: AccountInfo):
        """根据健康指标更新账户状态"""
        score = account.health_metrics.overall_score
        failure_count = account.failure_count

        if score >= 80 and failure_count < 2:
            account.status = AccountStatus.HEALTHY
        elif score >= 60 and failure_count < self.failure_threshold:
            account.status = AccountStatus.WARNING
        elif score >= 40 or failure_count < self.failure_threshold:
            account.status = AccountStatus.DEGRADED
        else:
            account.status = AccountStatus.FAILED
            # 设置恢复冷却时间
            account.recovery_cooldown_until = datetime.now(
            ) + timedelta(seconds=self.recovery_cooldown)

    async def _health_check_loop(self):
        """健康检查循环"""
        while not self._stop_event.is_set():
            try:
                await self._perform_health_check()
                await asyncio.sleep(self.health_check_interval)
            except Exception as e:
                logger.error(f"健康检查异常: {e}")
                await asyncio.sleep(60)  # 异常时等待1分钟

    async def _perform_health_check(self):
        """执行健康检查"""
        logger.debug("开始执行账户健康检查")

        for account in self.accounts.values():
            try:
                await self._check_account_health(account)
            except Exception as e:
                logger.error(f"账户 {account.account_id} 健康检查失败: {e}")

        self.scheduler_stats['health_check_runs'] += 1
        self.scheduler_stats['last_health_check'] = datetime.now()

        logger.debug("账户健康检查完成")

    async def _check_account_health(self, account: AccountInfo):
        """检查单个账户健康状态"""
        if not account.api_client:
            return

        start_time = datetime.now()

        try:
            # 执行健康检查API调用
            health_result = await account.api_client.health_check()
            response_time = (datetime.now() - start_time).total_seconds()

            success = health_result.get('health_score', 0) > 50

            # 更新健康指标
            await self._update_health_metrics(account, success, response_time)

            # 重置失败计数（如果检查成功）
            if success and account.status == AccountStatus.FAILED:
                account.status = AccountStatus.RECOVERING
                account.failure_count = 0
                account.recovery_cooldown_until = None
                logger.info(f"账户 {account.account_id} 开始恢复")

        except Exception as e:
            # 健康检查失败
            response_time = (datetime.now() - start_time).total_seconds()
            await self._update_health_metrics(account, False, response_time)

            logger.warning(f"账户 {account.account_id} 健康检查失败: {e}")

    def get_scheduler_stats(self) -> Dict[str, Any]:
        """获取调度器统计信息"""
        stats = self.scheduler_stats.copy()

        # 添加账户统计
        account_stats = {
            'total_accounts': len(self.accounts),
            'healthy_accounts': sum(1 for a in self.accounts.values() if a.status == AccountStatus.HEALTHY),
            'warning_accounts': sum(1 for a in self.accounts.values() if a.status == AccountStatus.WARNING),
            'degraded_accounts': sum(1 for a in self.accounts.values() if a.status == AccountStatus.DEGRADED),
            'failed_accounts': sum(1 for a in self.accounts.values() if a.status == AccountStatus.FAILED),
            'total_active_connections': sum(a.active_connections for a in self.accounts.values())
        }

        stats.update(account_stats)
        return stats

    def get_account_health_report(self) -> Dict[str, Any]:
        """获取账户健康报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'strategy': self.strategy.value,
            'accounts': {}
        }

        for account_id, account in self.accounts.items():
            metrics = account.health_metrics
            report['accounts'][account_id] = {
                'status': account.status.value,
                'health_scores': {
                    'overall': round(metrics.overall_score, 2),
                    'login': round(metrics.login_score, 2),
                    'error_rate': round(metrics.error_rate_score, 2),
                    'response_time': round(metrics.response_time_score, 2),
                    'rate_limit': round(metrics.rate_limit_score, 2)
                },
                'statistics': {
                    'total_requests': metrics.total_requests,
                    'success_rate': (metrics.successful_requests / metrics.total_requests * 100
                                     if metrics.total_requests > 0 else 0),
                    'average_response_time': round(metrics.average_response_time, 3),
                    'active_connections': account.active_connections,
                    'total_tasks_assigned': account.total_tasks_assigned
                },
                'last_activity': {
                    'last_assigned': account.last_assigned_time.isoformat() if account.last_assigned_time else None,
                    'last_success': metrics.last_success_time.isoformat() if metrics.last_success_time else None,
                    'last_failure': metrics.last_failure_time.isoformat() if metrics.last_failure_time else None
                }
            }

        return report

    async def rotate_account(self, old_account_id: str, reason: str = "manual"):
        """手动轮换账户"""
        if old_account_id not in self.accounts:
            logger.warning(f"尝试轮换未知账户: {old_account_id}")
            return False

        old_account = self.accounts[old_account_id]

        # 标记为需要恢复
        old_account.status = AccountStatus.RECOVERING
        old_account.recovery_cooldown_until = datetime.now(
        ) + timedelta(seconds=self.recovery_cooldown)

        self.scheduler_stats['account_rotations'] += 1
        logger.info(f"账户轮换: {old_account_id}, 原因: {reason}")

        return True

    def set_load_balancing_strategy(self, strategy: LoadBalancingStrategy):
        """设置负载均衡策略"""
        self.strategy = strategy
        # 重置轮询状态
        self._round_robin_index = 0
        self._weighted_round_robin_weights = []
        logger.info(f"负载均衡策略已更新为: {strategy.value}")

    async def add_account(self, account_info: AccountInfo) -> bool:
        """添加新账户"""
        try:
            # 创建API客户端
            account_info.api_client = RobustAPIClient(
                email=account_info.email,
                password=account_info.password
            )
            await account_info.api_client.initialize()

            # 添加到账户列表
            self.accounts[account_info.account_id] = account_info

            # 重置轮询权重（如果使用加权轮询）
            self._weighted_round_robin_weights = []

            logger.info(f"账户添加成功: {account_info.account_id}")
            return True

        except Exception as e:
            logger.error(f"添加账户失败: {account_info.account_id}, 错误: {e}")
            return False

    async def remove_account(self, account_id: str) -> bool:
        """移除账户"""
        if account_id not in self.accounts:
            logger.warning(f"尝试移除未知账户: {account_id}")
            return False

        account = self.accounts[account_id]

        # 关闭API客户端
        if account.api_client:
            await account.api_client.close()

        # 移除账户
        del self.accounts[account_id]

        # 清理分配记录
        if account_id in self.account_assignments:
            del self.account_assignments[account_id]

        # 重置轮询权重
        self._weighted_round_robin_weights = []

        logger.info(f"账户移除成功: {account_id}")
        return True
