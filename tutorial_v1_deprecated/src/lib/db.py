"""
数据库管理器 - 基于Peewee ORM和SQLite
专注于数据库操作，提供数据持久化功能
"""

import os
from typing import Dict, Any, List, Optional
from datetime import datetime, date
from peewee import fn
from .models import (
    db, ALL_MODELS,
    UserAccountModel, DatasetModel, AccountDatasetUsageModel,
    AccountAlphaFactorModel, AlphaDiggingProcessModel,
    RegionModel, UrlConfigModel
)
from .logger import get_logger
import sqlite3

logger = get_logger(__name__)

# 全局数据库管理器实例缓存
_db_manager_cache = {}


def _model_to_dict(model_instance, exclude_fields=None):
    """
    将模型实例转换为字典的通用方法

    Args:
        model_instance: 模型实例
        exclude_fields: 要排除的字段列表

    Returns:
        dict: 字典格式的数据
    """
    exclude_fields = exclude_fields or []
    data = {}
    for field_name in model_instance._meta.fields:
        if field_name not in exclude_fields:
            value = getattr(model_instance, field_name)
            data[field_name] = value
    return data


class DatabaseManager:
    """数据库管理器 - 专注于文件数据库操作"""

    def __init__(self, db_path: Optional[str] = None):
        """
        初始化数据库管理器

        Args:
            db_path: 数据库文件路径，如果为None则使用默认路径
        """
        self.db_path = db_path or self._get_default_db_path()
        self._initialized = False

    def _get_default_db_path(self) -> str:
        """获取默认数据库路径"""
        project_root = os.path.dirname(
            os.path.dirname(os.path.dirname(__file__)))
        return os.path.join(project_root, 'data', 'wq.db')

    def initialize_database(self, force_reinit: bool = False) -> None:
        """
        初始化数据库连接和表结构

        Args:
            force_reinit: 是否强制重新初始化，默认False
        """
        try:
            if not force_reinit and self._initialized:
                logger.debug(f"数据库已初始化，跳过重复初始化: {self.db_path}")
                return

            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)
            db.init(self.db_path)
            logger.info(f"已初始化文件数据库: {self.db_path}")

            # 检查数据库是否已存在表，但不再自动创建
            if self._database_has_tables():
                logger.info("检测到已存在的数据库表，初始化完成")
            else:
                logger.warning("数据库文件存在，但未检测到表结构。请使用初始化脚本创建。")

            self._initialized = True

        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise

    def _database_has_tables(self) -> bool:
        """检查数据库是否已有表"""
        try:
            # 尝试查询一个关键表来检查数据库是否已初始化
            cursor = db.execute_sql(
                "SELECT name FROM sqlite_master WHERE type='table' AND name='regions';")
            return cursor.fetchone() is not None
        except Exception:
            return False

    def create_tables(self) -> None:
        """创建数据库表（此方法现在仅供手动调用或测试使用）"""
        with db.atomic():
            db.create_tables(ALL_MODELS, safe=True)

    # ===========================================
    # 用户账户相关方法
    # ===========================================

    def create_user_account(self, email: str, password: str, name: str) -> UserAccountModel:
        """创建用户账户"""
        return UserAccountModel.create(email=email, password=password, name=name)

    def get_user_account(self, email: str) -> Optional[Dict[str, Any]]:
        """根据邮箱获取用户账户信息"""
        try:
            account = UserAccountModel.get(UserAccountModel.email == email)
            return _model_to_dict(account)
        except UserAccountModel.DoesNotExist:
            return None

    def get_user_account_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """根据姓名获取用户账户信息"""
        try:
            account = UserAccountModel.get(UserAccountModel.name == name)
            return _model_to_dict(account)
        except UserAccountModel.DoesNotExist:
            return None

    def get_all_user_accounts(self) -> List[Dict[str, Any]]:
        """
        获取所有用户账户信息

        Returns:
            List[Dict]: 所有用户账户信息的列表
        """
        accounts = UserAccountModel.select()
        return [_model_to_dict(account) for account in accounts]

    def update_user_account(self, email: str, **kwargs) -> bool:
        """更新用户账户信息"""
        try:
            account = UserAccountModel.get(UserAccountModel.email == email)
            for field, value in kwargs.items():
                if hasattr(account, field):
                    setattr(account, field, value)
            account.save()
            return True
        except UserAccountModel.DoesNotExist:
            return False

    def delete_user_account(self, email: str) -> bool:
        """删除用户账户"""
        try:
            account = UserAccountModel.get(UserAccountModel.email == email)
            account.delete_instance()
            return True
        except UserAccountModel.DoesNotExist:
            return False

    # ===========================================
    # 数据集相关方法
    # ===========================================

    def create_dataset(self, name: str, description: str = None) -> DatasetModel:
        """创建数据集"""
        return DatasetModel.create(name=name, description=description)

    def get_dataset(self, name: str) -> Optional[Dict[str, Any]]:
        """根据名称获取数据集信息"""
        try:
            dataset = DatasetModel.get(DatasetModel.name == name)
            return _model_to_dict(dataset, exclude_fields=['updated_at'])
        except DatasetModel.DoesNotExist:
            return None

    def get_all_datasets(self) -> List[Dict[str, Any]]:
        """获取所有数据集信息"""
        return [_model_to_dict(dataset, exclude_fields=['updated_at'])
                for dataset in DatasetModel.select()]

    # ===========================================
    # Alpha因子挖掘相关方法
    # ===========================================

    def create_alpha_digging_process(self, account_email: str, digging_time: datetime,
                                     digging_status: str = 'in_progress', **kwargs) -> AlphaDiggingProcessModel:
        """创建因子挖掘过程记录"""
        account = UserAccountModel.get(UserAccountModel.email == account_email)
        return AlphaDiggingProcessModel.create(
            account=account,
            digging_time=digging_time,
            digging_status=digging_status,
            **kwargs
        )

    def get_alpha_digging_process(self, account_email: str = None, digging_status: str = None,
                                  date_from: datetime = None, date_to: datetime = None) -> List[Dict[str, Any]]:
        """获取因子挖掘过程记录"""
        query = AlphaDiggingProcessModel.select().join(UserAccountModel)

        if account_email:
            query = query.where(UserAccountModel.email == account_email)
        if digging_status:
            query = query.where(
                AlphaDiggingProcessModel.digging_status == digging_status)
        if date_from:
            query = query.where(
                AlphaDiggingProcessModel.digging_time >= date_from)
        if date_to:
            query = query.where(
                AlphaDiggingProcessModel.digging_time <= date_to)

        query = query.order_by(AlphaDiggingProcessModel.digging_time.desc())

        results = []
        for record in query:
            data = _model_to_dict(record)
            data['account_email'] = record.account.email
            data['account_name'] = record.account.name
            results.append(data)
        return results

    def update_alpha_digging_process(self, digging_id: int, **kwargs) -> bool:
        """更新因子挖掘过程记录"""
        try:
            record = AlphaDiggingProcessModel.get_by_id(digging_id)
            for field, value in kwargs.items():
                if hasattr(record, field):
                    setattr(record, field, value)
            record.save()
            return True
        except AlphaDiggingProcessModel.DoesNotExist:
            return False

    def get_alpha_digging_stats(self, account_email: str = None) -> List[Dict[str, Any]]:
        """获取因子挖掘统计信息"""
        if account_email:
            account = UserAccountModel.get(
                UserAccountModel.email == account_email)
            query = (AlphaDiggingProcessModel
                     .select(AlphaDiggingProcessModel.digging_status,
                             fn.COUNT().alias('count'),
                             fn.AVG(AlphaDiggingProcessModel.fitness).alias(
                                 'avg_fitness'),
                             fn.AVG(AlphaDiggingProcessModel.sharpe).alias(
                                 'avg_sharpe'),
                             fn.MAX(AlphaDiggingProcessModel.digging_time).alias('latest_digging_time'))
                     .where(AlphaDiggingProcessModel.account == account)
                     .group_by(AlphaDiggingProcessModel.digging_status))

            return [{
                'account_name': account.name,
                'digging_status': stat.digging_status,
                'count': stat.count,
                'avg_fitness': stat.avg_fitness,
                'avg_sharpe': stat.avg_sharpe,
                'latest_digging_time': stat.latest_digging_time
            } for stat in query]
        else:
            query = (AlphaDiggingProcessModel
                     .select(UserAccountModel.name,
                             AlphaDiggingProcessModel.digging_status,
                             fn.COUNT().alias('count'),
                             fn.AVG(AlphaDiggingProcessModel.fitness).alias(
                                 'avg_fitness'),
                             fn.AVG(AlphaDiggingProcessModel.sharpe).alias(
                                 'avg_sharpe'),
                             fn.MAX(AlphaDiggingProcessModel.digging_time).alias('latest_digging_time'))
                     .join(UserAccountModel)
                     .group_by(UserAccountModel.name, AlphaDiggingProcessModel.digging_status)
                     .order_by(UserAccountModel.name, AlphaDiggingProcessModel.digging_status))

            return [{
                'account_name': stat.account.name,
                'digging_status': stat.digging_status,
                'count': stat.count,
                'avg_fitness': stat.avg_fitness,
                'avg_sharpe': stat.avg_sharpe,
                'latest_digging_time': stat.latest_digging_time
            } for stat in query]

    # ===========================================
    # Alpha因子记录相关方法
    # ===========================================

    def create_account_alpha_factor(self, account_name: str, date: date,
                                    dataset_name: str, success_alpha_id: str) -> AccountAlphaFactorModel:
        """创建账户Alpha因子记录"""
        return AccountAlphaFactorModel.create(
            account_name=account_name,
            date=date,
            dataset_name=dataset_name,
            success_alpha_id=success_alpha_id
        )

    def get_account_alpha_factors(self, account_name: str = None, date_from: date = None,
                                  date_to: date = None, dataset_name: str = None) -> List[Dict[str, Any]]:
        """获取账户Alpha因子记录"""
        query = AccountAlphaFactorModel.select()

        if account_name:
            query = query.where(
                AccountAlphaFactorModel.account_name == account_name)
        if date_from:
            query = query.where(AccountAlphaFactorModel.date >= date_from)
        if date_to:
            query = query.where(AccountAlphaFactorModel.date <= date_to)
        if dataset_name:
            query = query.where(
                AccountAlphaFactorModel.dataset_name == dataset_name)

        query = query.order_by(AccountAlphaFactorModel.date.desc())
        return [_model_to_dict(factor, exclude_fields=['updated_at']) for factor in query]

    # ===========================================
    # 地区配置相关方法
    # ===========================================

    def create_region(self, region_code: str, region_name: str, instrument_type: str, description: str = None) -> RegionModel:
        """创建地区配置"""
        return RegionModel.create(
            region_code=region_code,
            region_name=region_name,
            instrument_type=instrument_type,
            description=description
        )

    def get_region(self, region_code: str) -> Optional[Dict[str, Any]]:
        """获取地区信息"""
        try:
            region = RegionModel.get(RegionModel.region_code == region_code)
            return _model_to_dict(region)
        except RegionModel.DoesNotExist:
            return None

    def get_all_regions(self) -> List[Dict[str, Any]]:
        """获取所有地区信息"""
        return [_model_to_dict(region) for region in RegionModel.select()]

    # ===========================================
    # URL配置相关方法
    # ===========================================

    def create_url_config(self, url_key: str, url_path: str, description: str = None, is_active: bool = True) -> UrlConfigModel:
        """创建URL配置"""
        return UrlConfigModel.create(
            url_key=url_key,
            url_path=url_path,
            description=description,
            is_active=is_active
        )

    def get_url(self, url_key: str) -> Optional[str]:
        """获取URL路径"""
        try:
            url_config = UrlConfigModel.get(UrlConfigModel.url_key == url_key)
            return url_config.url_path
        except UrlConfigModel.DoesNotExist:
            return None

    def get_all_url_configs(self, active_only: bool = True) -> List[Dict[str, Any]]:
        """获取所有URL配置"""
        query = UrlConfigModel.select()
        if active_only:
            query = query.where(UrlConfigModel.is_active == True)

        return [_model_to_dict(config) for config in query]

    # ===========================================
    # 批量操作方法
    # ===========================================

    def batch_create(self, model_class, records: List[Dict[str, Any]]) -> None:
        """批量创建记录的通用方法"""
        with db.atomic():
            for record in records:
                model_class.create(**record)

    # ===========================================
    # 通用方法
    # ===========================================

    def clear_all_data(self) -> None:
        """清空所有数据表"""
        with db.atomic():
            for model in reversed(ALL_MODELS):  # 反向删除以避免外键约束问题
                model.delete().execute()
        logger.info("已清空所有数据")

    def is_initialized(self) -> bool:
        """检查数据库是否已初始化"""
        return self._initialized

    def close(self) -> None:
        """关闭数据库连接"""
        if not db.is_closed():
            db.close()
            logger.info("数据库连接已关闭")
        self._initialized = False


def create_database_manager(db_path: Optional[str] = None) -> DatabaseManager:
    """
    创建数据库管理器的便捷函数（支持实例缓存）

    Args:
        db_path: 数据库文件路径，如果为None则使用默认路径

    Returns:
        DatabaseManager: 初始化完成的数据库管理器实例
    """
    if db_path is None:
        temp_manager = DatabaseManager()
        actual_db_path = temp_manager.db_path
    else:
        actual_db_path = db_path

    cache_key = os.path.abspath(actual_db_path)

    if cache_key in _db_manager_cache:
        cached_manager = _db_manager_cache[cache_key]
        logger.debug(f"使用缓存的数据库管理器实例: {cache_key}")
        return cached_manager

    manager = DatabaseManager(db_path)
    manager.initialize_database()
    _db_manager_cache[cache_key] = manager
    logger.debug(f"创建并缓存新的数据库管理器实例: {cache_key}")

    return manager


def clear_database_manager_cache():
    """清理数据库管理器缓存"""
    global _db_manager_cache
    for manager in _db_manager_cache.values():
        try:
            manager.close()
        except Exception as e:
            logger.warning(f"关闭缓存的数据库连接时出错: {e}")
    _db_manager_cache.clear()
    logger.debug("数据库管理器缓存已清理")


def run_sql_script(db_path: str, script_path: str):
    """
    使用原生的sqlite3执行SQL脚本，可以处理多语句脚本。

    Args:
        db_path: 数据库文件路径。
        script_path: SQL脚本文件路径。
    """
    logger.info(
        f"Connecting to database {db_path} to run script {script_path}")
    try:
        if not os.path.exists(script_path):
            raise FileNotFoundError(
                f"SQL script file not found: {script_path}")

        with open(script_path, 'r', encoding='utf-8') as f:
            script = f.read()

        # 使用原生sqlite3连接，因为executescript可以更好地处理多语句脚本
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.executescript(script)
        conn.commit()
        conn.close()
        logger.info(f"Successfully executed SQL script: {script_path}")
    except Exception as e:
        logger.error(f"Failed to execute SQL script {script_path}: {e}")
        raise


def reset_database(force: bool = False):
    """
    重置数据库：删除旧数据库（如果force=True），并使用init_database.sql重新创建。

    Args:
        force: 是否强制删除现有数据库。
    """
    manager = DatabaseManager()
    db_path = manager.db_path

    # 在操作文件前，确保所有缓存的连接都已关闭
    clear_database_manager_cache()

    if force and os.path.exists(db_path):
        logger.info(
            f"Force option used. Deleting existing database: {db_path}")
        try:
            os.remove(db_path)
            logger.info(f"Database file {db_path} deleted.")
        except OSError as e:
            logger.error(f"Error deleting database file {db_path}: {e}")
            raise

    # 定义SQL脚本的路径
    project_root = os.path.dirname(os.path.dirname(
        os.path.dirname(os.path.abspath(__file__))))
    sql_script_path = os.path.join(project_root, 'sql', 'init_database.sql')

    logger.info("Running database initialization script...")
    run_sql_script(db_path, sql_script_path)
    logger.info("Database has been reset and initialized successfully.")


if __name__ == '__main__':
    import argparse
    import sys

    # 这个脚本被设计为从项目根目录作为模块运行，以正确处理相对导入。
    # 示例: python3 -m src.lib.db --init --force

    parser = argparse.ArgumentParser(
        description="数据库初始化工具 (db.py)",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python3 -m src.lib.db --init
  python3 -m src.lib.db --init --force
"""
    )
    parser.add_argument(
        '--init',
        action='store_true',
        help="执行数据库初始化。如果数据库已存在，此操作将不会执行，除非使用--force。"
    )
    parser.add_argument(
        '--force',
        action='store_true',
        help="强制执行初始化。如果数据库文件已存在，将删除旧文件并重新创建。"
    )

    args = parser.parse_args()

    if args.init:
        logger.info("通过 `python -m src.lib.db` 开始数据库初始化流程...")
        try:
            reset_database(force=args.force)
            logger.info("数据库初始化成功完成！")
        except Exception as e:
            logger.error(f"数据库初始化过程中发生错误: {e}", exc_info=True)
            sys.exit(1)
    else:
        logger.info("请使用 --init 参数来启动数据库初始化。")
        parser.print_help()
