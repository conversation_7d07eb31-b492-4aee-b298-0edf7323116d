#!/usr/bin/env python3
"""
基础配置设置模块

处理系统级别的通用配置，所有用户共享：
- 地区市场信息配置
- API URLs 和端点配置
- 数据集基础信息配置
"""

from lib.db_config_reader import get_config_reader
from lib.db import create_database_manager
from lib.logger import get_logger, get_ui
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Callable

# 添加src目录到Python路径
current_dir = Path(__file__).parent.parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

logger = get_logger(__name__)
ui = get_ui(__name__)


def check_base_config_initialized() -> bool:
    """检查基础配置是否已经初始化"""
    try:
        config_reader = get_config_reader()
        api_base = config_reader.get_url('api_base')

        db_manager = create_database_manager()
        regions = db_manager.get_all_regions()

        return bool(api_base and regions)
    except Exception as e:
        logger.error(f"检查基础配置状态时出错: {e}")
        return False


def get_default_configs() -> Dict[str, Dict]:
    """获取所有默认配置"""
    return {
        'regions': {
            'data': [
                ('usa', 'USA', 'TOP3000', '美国市场 - 流动性最好的发达市场'),
                ('chn', 'CHN', 'TOP2000U', '中国市场 - A股主要上市公司'),
                ('jpn', 'JPN', 'TOP1000', '日本市场 - 亚洲重要发达市场'),
                ('eur', 'EUR', 'TOP1200', '欧洲市场 - 欧洲联合交易市场'),
                ('gbr', 'GBR', 'TOP500', '英国市场 - 伦敦证券交易所'),
                ('kor', 'KOR', 'TOP200', '韩国市场 - 韩国证券交易所'),
                ('aus', 'AUS', 'TOP300', '澳洲市场 - 澳大利亚证券交易所'),
                ('can', 'CAN', 'TOP400', '加拿大市场 - 多伦多证券交易所')
            ],
            'insert_func': 'create_region',
            'display_func': lambda x: f"{x[0]} - {x[1]} ({x[2]}): {x[3]}",
            'success_func': lambda x: f"{x[0]} - {x[1]}"
        },
        'datasets': {
            'data': [
                ('news18', '新闻数据集18 - 新闻情感分析数据，适合初学者练习'),
                ('equity', '股票数据集 - 全球股票市场基础数据，经典数据集'),
                ('futures', '期货数据集 - 期货合约交易数据，适合高级用户'),
                ('crypto', '加密货币数据集 - 数字货币市场数据，新兴资产'),
                ('options', '期权数据集 - 期权合约数据，专业衍生品交易'),
                ('analyst4', '分析师数据集4 - 专业分析师推荐数据'),
                ('macro', '宏观经济数据集 - 宏观经济指标数据'),
                ('sentiment', '情绪数据集 - 市场情绪和舆情分析数据')
            ],
            'insert_func': 'create_dataset',
            'display_func': lambda x: f"{x[0]}: {x[1]}",
            'success_func': lambda x: f"{x[0]}"
        },
        'urls': {
            'data': [
                ('api_base', 'https://api.worldquantbrain.com',
                 'WorldQuant Brain API基础URL'),
                ('auth', '/authentication', '用户认证接口'),
                ('alpha_user', '/users/self/alphas', '用户Alpha因子接口'),
                ('alpha_submit', '/alphas', 'Alpha因子提交接口'),
                ('alpha_simulation', '/simulate', 'Alpha因子模拟接口'),
                ('datasets', '/datasets', '数据集信息接口'),
                ('regions', '/regions', '地区信息接口'),
                ('user_profile', '/users/self', '用户个人资料接口'),
                ('user_stats', '/users/self/stats', '用户统计信息接口'),
                ('alpha_status', '/alphas/{alpha_id}/status', 'Alpha状态查询接口')
            ],
            'insert_func': 'create_url_config',
            'display_func': lambda x: f"{x[0]}: {x[1]} - {x[2]}",
            'success_func': lambda x: f"{x[0]}"
        }
    }


def _setup_config_type(config_type: str, config_info: Dict, confirm_each: bool = False) -> bool:
    """通用配置设置函数"""
    try:
        icons = {'regions': '🌍', 'datasets': '📊', 'urls': '🔗'}
        names = {'regions': '地区', 'datasets': '数据集', 'urls': 'URL'}

        ui.section_subheader(
            f"{icons[config_type]} 基础{names[config_type]}配置设置")

        db_manager = create_database_manager()
        data = config_info['data']

        if confirm_each:
            ui.info(f"请确认要添加的{names[config_type]}配置：")
            for item in data:
                ui.info(f"  {config_info['display_func'](item)}")

            confirm = input(
                f"\n确认添加以上 {len(data)} 个{names[config_type]}配置吗？(y/n): ").strip().lower()
            if confirm not in ['y', 'yes', '是']:
                ui.error(f"{names[config_type]}配置设置取消")
                return False

        ui.progress(f"正在设置{names[config_type]}配置...")
        success_count = 0
        insert_method = getattr(db_manager, config_info['insert_func'])

        for item in data:
            try:
                insert_method(*item)
                ui.success(config_info['success_func'](item))
                success_count += 1
            except Exception as e:
                if "UNIQUE constraint failed" in str(e):
                    ui.success(f"{config_info['success_func'](item)} (已存在)")
                    success_count += 1
                else:
                    ui.error(f"{config_info['success_func'](item)}: {e}")

        ui.success(
            f"{names[config_type]}配置完成：成功设置 {success_count}/{len(data)} 个{names[config_type]}")
        return success_count > 0

    except Exception as e:
        logger.error(f"设置{names.get(config_type, config_type)}配置时出错: {e}")
        ui.error(f"{names.get(config_type, config_type)}配置设置失败: {e}")
        return False


def setup_configuration(config_types: List[str] = None, interactive: bool = True,
                        datasets_interactive: bool = None) -> bool:
    """
    统一配置设置函数

    Args:
        config_types: 要设置的配置类型列表，None表示所有类型
        interactive: 是否启用交互式确认
        datasets_interactive: 数据集是否单独使用交互式确认，None表示使用interactive设置

    Returns:
        bool: 是否配置成功
    """
    if config_types is None:
        config_types = ['regions', 'datasets', 'urls']

    if datasets_interactive is None:
        datasets_interactive = interactive

    logger.info("=== 基础系统配置设置向导 ===")
    ui.section_header("⚙️ 基础系统配置设置")

    if len(config_types) == 1 and config_types[0] == 'datasets':
        ui.info("正在设置数据集配置，其他配置将使用默认值")
    else:
        ui.info("这里配置所有用户共享的基础信息")

    try:
        configs = get_default_configs()
        success_count = 0
        total_steps = len(config_types)

        for i, config_type in enumerate(config_types, 1):
            # 确定是否使用交互模式
            use_interactive = datasets_interactive if config_type == 'datasets' else False

            config_names = {'regions': '地区市场',
                            'datasets': '数据集', 'urls': 'API URL'}
            ui.step(i, total_steps, f"设置{config_names[config_type]}配置")

            if _setup_config_type(config_type, configs[config_type], confirm_each=use_interactive):
                success_count += 1

        # 总结结果
        ui.section_subheader("📋 配置设置完成")
        ui.info(f"完成步骤: {success_count}/{total_steps}")

        if success_count == total_steps:
            ui.success("所有配置设置成功！")
            logger.info("配置设置完成")
            return True
        elif success_count > 0:
            ui.warning("部分配置设置成功，系统可以正常使用")
            logger.warning(f"部分配置设置成功: {success_count}/{total_steps}")
            return True
        else:
            ui.error("配置设置失败")
            logger.error("配置设置失败")
            return False

    except Exception as e:
        logger.error(f"配置设置过程中出错: {e}")
        ui.error(f"配置设置失败: {e}")
        return False


def show_current_base_config():
    """显示当前基础配置信息"""
    try:
        ui.section_header("⚙️ 当前基础配置信息")

        db_manager = create_database_manager()

        # 显示地区配置
        regions = db_manager.get_all_regions()
        ui.info(f"🌍 地区配置 ({len(regions)} 个):")
        for region in regions[:5]:
            ui.checklist_item(
                f"{region['region_code']} - {region['region_name']} ({region['market_code']})")
        if len(regions) > 5:
            ui.info(f"  ... 还有 {len(regions) - 5} 个地区")

        # 显示数据集配置
        datasets = db_manager.get_all_datasets()
        ui.info(f"📊 数据集配置 ({len(datasets)} 个):")
        for dataset in datasets[:5]:
            ui.checklist_item(f"{dataset['name']}")
        if len(datasets) > 5:
            ui.info(f"  ... 还有 {len(datasets) - 5} 个数据集")

        # 显示URL配置
        config_reader = get_config_reader()
        urls = config_reader.get_url()
        ui.info(f"🔗 URL配置 ({len(urls)} 个):")
        for key in ['api_base', 'auth', 'alpha_user', 'alpha_submit']:
            if key in urls:
                ui.checklist_item(f"{key}: {urls[key]}")

        ui.success("基础配置已就绪")

    except Exception as e:
        logger.warning(f"显示基础配置信息时出错: {e}")
        ui.error(f"无法显示基础配置信息: {e}")


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--datasets-only":
        # 只设置数据集配置模式
        ui.info("启动数据集配置模式...")
        setup_configuration(config_types=['regions', 'datasets', 'urls'],
                            interactive=False, datasets_interactive=True)
    else:
        # 原有的完整配置模式
        if not check_base_config_initialized():
            ui.info("检测到基础配置未初始化，开始设置...")
            setup_configuration()
        else:
            ui.info("基础配置已存在，显示当前配置：")
            show_current_base_config()
