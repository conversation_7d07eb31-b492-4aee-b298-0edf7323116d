#!/usr/bin/env python3
"""
用户配置设置模块

专门处理个人用户的账户信息和个性化配置：
- WorldQuant Brain 账户信息（邮箱、密码、用户名）
- 个人偏好数据集选择
- 个人交易策略设置
"""

from lib.db_config_reader import get_config_reader
from lib.db import create_database_manager
from lib.logger import get_logger, get_ui
import sys
from pathlib import Path
from typing import Dict, Optional, Tuple

# 添加src目录到Python路径
current_dir = Path(__file__).parent.parent  # 指向 src 目录
project_root = current_dir.parent  # 指向项目根目录

# 确保能够导入src下的模块
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

# 如果项目根目录也需要，添加它
if str(project_root) not in sys.path:
    sys.path.insert(0, str(project_root))


logger = get_logger(__name__)
ui = get_ui(__name__)


def check_user_configured() -> bool:
    """
    检查用户是否已经配置了个人账户信息

    Returns:
        bool: True表示已配置，False表示需要配置
    """
    try:
        config_reader = get_config_reader()
        all_accounts = config_reader.get_all_accounts()

        # 检查是否有任何用户账户信息
        if all_accounts:
            logger.debug(f"检测到 {len(all_accounts)} 个已配置的用户账户")
            return True
        else:
            logger.debug("未检测到任何用户配置，需要初始化")
            return False
    except Exception as e:
        logger.error(f"检查用户配置时发生严重错误: {e}")
        # 抛出异常，确保配置问题能被立即发现
        raise


def get_user_account_input() -> Tuple[str, str, str]:
    """
    获取用户账户信息输入

    Returns:
        Tuple[str, str, str]: (email, password, name)
    """
    ui.section_subheader("📧 用户账户配置")
    ui.info("请输入您的 WorldQuant Brain 账户信息：")

    while True:
        email = input("邮箱地址: ").strip()
        if email and '@' in email:
            break
        ui.error("请输入有效的邮箱地址")

    while True:
        password = input("密码: ").strip()
        if password:
            break
        ui.error("密码不能为空")

    name = input("用户名称 (可选，默认为邮箱前缀): ").strip()
    if not name:
        name = email.split('@')[0]

    return email, password, name


def get_user_dataset_preference() -> str:
    """
    获取用户偏好的数据集选择

    Returns:
        str: 选择的数据集名称
    """
    ui.section_subheader("📊 个人数据集偏好")

    # 个人常用数据集选项
    available_datasets = [
        ('news18', '新闻数据集18 - 新闻情感分析数据（推荐新手）'),
        ('equity', '股票数据集 - 股票市场数据（经典选择）'),
        ('futures', '期货数据集 - 期货合约数据（高级用户）'),
        ('crypto', '加密货币数据集 - 数字货币数据（新兴市场）'),
        ('options', '期权数据集 - 期权合约数据（专业用户）'),
        ('custom', '自定义数据集 - 手动输入数据集名称')
    ]

    ui.info("请选择您偏好的数据集（可后续在程序中修改）:")
    for i, (dataset_id, description) in enumerate(available_datasets, 1):
        ui.info(f"  {i}. {dataset_id} - {description}")

    while True:
        try:
            choice = input(
                f"\n请选择数据集 (1-{len(available_datasets)}) 或直接输入数据集名称: ").strip()

            # 如果输入的是数字选择
            if choice.isdigit():
                choice_num = int(choice)
                if 1 <= choice_num <= len(available_datasets):
                    if choice_num == len(available_datasets):  # 自定义选项
                        custom_dataset = input("请输入自定义数据集名称: ").strip()
                        if custom_dataset:
                            return custom_dataset
                        else:
                            ui.error("数据集名称不能为空")
                            continue
                    else:
                        return available_datasets[choice_num - 1][0]
                else:
                    ui.error(f"请输入 1-{len(available_datasets)} 之间的数字")
            # 如果直接输入数据集名称
            else:
                if choice:
                    return choice
                else:
                    ui.error("数据集名称不能为空")
        except ValueError:
            ui.error("输入无效，请重新输入")


def get_user_trading_preferences() -> Dict[str, str]:
    """
    获取用户交易偏好设置

    Returns:
        Dict[str, str]: 交易偏好配置
    """
    ui.section_subheader("⚙️ 个人交易偏好 (可选配置)")

    preferences = {}

    # 默认地区偏好
    ui.info("默认交易地区偏好（影响因子挖掘的默认设置）:")
    regions = [
        ('usa', 'USA - 美国市场（流动性最好）'),
        ('chn', 'CHN - 中国市场（A股市场）'),
        ('jpn', 'JPN - 日本市场（亚洲发达市场）'),
        ('eur', 'EUR - 欧洲市场（欧洲联合市场）'),
        ('global', '全球市场（不设偏好）')
    ]

    for i, (region_code, description) in enumerate(regions, 1):
        ui.info(f"  {i}. {description}")

    while True:
        try:
            choice = input(f"请选择默认地区偏好 (1-{len(regions)}, 默认为1-USA): ").strip()
            if not choice:
                preferences['default_region'] = 'usa'
                break
            elif choice.isdigit():
                choice_num = int(choice)
                if 1 <= choice_num <= len(regions):
                    preferences['default_region'] = regions[choice_num - 1][0]
                    break
                else:
                    ui.error(f"请输入 1-{len(regions)} 之间的数字")
            else:
                ui.error("请输入数字")
        except ValueError:
            ui.error("输入无效")

    # 风险偏好设置
    ui.info("风险偏好等级:")
    risk_levels = [
        ('conservative', '保守型 - 偏好稳定收益'),
        ('moderate', '稳健型 - 平衡风险收益'),
        ('aggressive', '激进型 - 追求高收益')
    ]

    for i, (level, description) in enumerate(risk_levels, 1):
        ui.info(f"  {i}. {description}")

    while True:
        try:
            choice = input(
                f"请选择风险偏好 (1-{len(risk_levels)}, 默认为2-稳健型): ").strip()
            if not choice:
                preferences['risk_level'] = 'moderate'
                break
            elif choice.isdigit():
                choice_num = int(choice)
                if 1 <= choice_num <= len(risk_levels):
                    preferences['risk_level'] = risk_levels[choice_num - 1][0]
                    break
                else:
                    ui.error(f"请输入 1-{len(risk_levels)} 之间的数字")
            else:
                ui.error("请输入数字")
        except ValueError:
            ui.error("输入无效")

    return preferences


def save_user_configuration(email: str, password: str, name: str,
                            preferred_dataset: str, trading_prefs: Dict[str, str]) -> bool:
    """
    保存用户配置到数据库

    Args:
        email: 用户邮箱
        password: 用户密码  
        name: 用户名称
        preferred_dataset: 偏好数据集
        trading_prefs: 交易偏好设置

    Returns:
        bool: 是否保存成功
    """
    try:
        db_manager = create_database_manager()

        # 保存用户账户基本信息
        logger.info("保存用户账户信息...")
        try:
            db_manager.insert_user_account(email, password, name)
            ui.success("用户账户信息已保存")
        except Exception as e:
            if "UNIQUE constraint failed" in str(e):
                # 用户已存在，更新信息
                db_manager.update_user_account(email, password, name)
                ui.success("用户账户信息已更新")
            else:
                raise e

        # 保存个人偏好数据集
        logger.info("保存用户数据集偏好...")
        try:
            db_manager.insert_dataset(preferred_dataset, f"用户 {name} 的偏好数据集")
            ui.success("数据集偏好已保存")
        except Exception as e:
            if "UNIQUE constraint failed" in str(e):
                ui.success("数据集已存在")
            else:
                ui.warning(f"数据集保存警告: {e}")

        # 保存用户交易偏好（可以扩展为用户配置表）
        logger.info("用户配置保存完成")
        # 不关闭数据库连接，使用缓存机制管理连接

        return True

    except Exception as e:
        logger.error(f"保存用户配置时出错: {e}")
        ui.error(f"用户配置保存失败: {e}")
        return False


def setup_user_configuration(interactive: bool = True) -> bool:
    """
    用户配置设置向导主流程

    Args:
        interactive: 是否使用交互式模式，False时返回失败提示用户手动配置

    Returns:
        bool: 是否配置成功
    """
    logger.info("=== 用户个人配置设置向导 ===")
    ui.section_header("👤 用户个人配置设置")

    # 非交互式模式下，提示用户手动配置
    if not interactive:
        ui.info("检测到用户信息尚未配置，请手动运行以下命令进行用户配置:")
        ui.info("  python3 src/process/user_setup.py 或 python3 src/setup.py")
        logger.warning("非交互式模式下无法自动配置用户信息")
        return False

    ui.info("这里配置您的个人账户信息和偏好设置")

    try:
        # 1. 获取用户账户信息
        email, password, name = get_user_account_input()

        # 2. 获取数据集偏好
        preferred_dataset = get_user_dataset_preference()

        # 3. 获取交易偏好
        trading_prefs = get_user_trading_preferences()

        # 4. 确认配置信息
        ui.section_subheader("📋 用户配置确认")
        ui.info(f"用户名称: {name}")
        ui.info(f"邮箱地址: {email}")
        ui.info(f"偏好数据集: {preferred_dataset}")
        ui.info(f"默认地区: {trading_prefs.get('default_region', 'usa')}")
        ui.info(f"风险偏好: {trading_prefs.get('risk_level', 'moderate')}")

        while True:
            confirm = input("\n确认保存用户配置吗？(y/n): ").strip().lower()
            if confirm in ['y', 'yes', '是']:
                break
            elif confirm in ['n', 'no', '否']:
                ui.error("用户配置取消")
                return False
            else:
                ui.info("请输入 y 或 n")

        # 5. 保存用户配置
        ui.progress("正在保存用户配置...")
        success = save_user_configuration(
            email, password, name, preferred_dataset, trading_prefs)

        if success:
            ui.success("用户配置保存成功!")
            ui.info("="*60)
            ui.checklist_item(f"用户: {name} ({email})")
            ui.checklist_item(f"偏好数据集: {preferred_dataset}")
            ui.checklist_item(
                f"默认地区: {trading_prefs.get('default_region', 'usa')}")
            ui.checklist_item(
                f"风险偏好: {trading_prefs.get('risk_level', 'moderate')}")
            ui.info("="*60)
            logger.info("用户配置设置完成")
            return True
        else:
            return False

    except Exception as e:
        logger.error(f"用户配置设置过程中出错: {e}")
        ui.error(f"用户配置设置失败: {e}")
        return False


def get_current_user_info() -> Optional[Dict[str, str]]:
    """
    获取当前用户信息

    Returns:
        Optional[Dict[str, str]]: 用户信息字典，如果未配置返回None
    """
    try:
        config_reader = get_config_reader()
        account_info = config_reader.get_account()
        # 不需要关闭单例实例
        return account_info
    except Exception as e:
        logger.warning(f"获取用户信息时出错: {e}")
        return None


def show_current_user_info():
    """显示当前用户信息"""
    user_info = get_current_user_info()

    if user_info:
        ui.section_header("👤 当前登录用户")
        ui.info(f"用户邮箱: {user_info.get('email', '未知')}")
        ui.success("登录状态: 已配置")
    else:
        ui.error("未找到用户配置信息")


if __name__ == "__main__":
    # 测试用户配置模块
    if not check_user_configured():
        setup_user_configuration()
    else:
        ui.info("用户已配置，显示当前信息：")
        show_current_user_info()
