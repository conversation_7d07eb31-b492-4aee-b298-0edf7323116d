#!/usr/bin/env python3
"""
WQ项目因子挖掘主应用程序

统一的因子挖掘系统，整合1-2-3阶段挖掘流程
支持多账户管理、智能调度、实时监控等功能

基于数据库驱动的队列管理系统，替代原有的多脚本分散架构
"""

from lib.factor_generator import FactorGenerator
from lib.quality_checker import QualityChecker
from lib.smart_submitter import SmartSubmitter
from workers.ml_enhanced_step3_worker import MLEnhancedStep3Worker
from workers.ml_enhanced_step2_worker import MLEnhancedStep2Worker
from workers.ml_enhanced_step1_worker import MLEnhancedStep1Worker
from lib.notification_system import NotificationSystem, NotificationLevel
from lib.schedulers import FactorDiggingScheduler
from lib.queue_manager import AlphaQueue
from lib.db import create_database_manager
from lib.models import UserAccountModel, AlphaFactorQueueModel, AlphaBatchModel
from lib.session import get_cached_session
from lib.logger import get_logger, get_ui
from process.user_setup import check_user_configured
from process.config_setup import check_base_config_initialized
from setup import quick_setup, show_current_status
import asyncio
import sys
import argparse
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import signal

# 添加src目录到Python路径
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))


# 导入核心组件

logger = get_logger(__name__)
ui = get_ui(__name__)


class FactorMiningApplication:
    """统一的因子挖掘应用程序"""

    def __init__(self):
        self.db = create_database_manager()
        self.running = False
        self.workers = {}
        self.scheduler = None
        self.notification_manager = None
        self.queue_manager = None

        # 工作器配置
        self.worker_config = {
            'max_concurrent_tasks': 10,
            'batch_size': 50,
            'quiet_mode': False
        }

    async def initialize(self) -> bool:
        """初始化应用程序"""
        try:
            ui.section_header("🔧 初始化因子挖掘系统")

            # 初始化核心组件
            self.queue_manager = AlphaQueue()
            self.scheduler = FactorDiggingScheduler()
            self.notification_manager = NotificationSystem()

            # 初始化工作器
            await self._initialize_workers()

            # 启动通知系统
            await self.notification_manager.start()

            ui.success("系统初始化完成")
            return True

        except Exception as e:
            logger.error(f"系统初始化失败: {e}")
            ui.error(f"初始化失败: {e}")
            return False

    async def _initialize_workers(self):
        """初始化各阶段工作器"""
        ui.info("初始化工作器...")

        # 创建工作器实例
        self.workers = {
            'step1': MLEnhancedStep1Worker(quiet=self.worker_config['quiet_mode']),
            'step2': MLEnhancedStep2Worker(quiet=self.worker_config['quiet_mode']),
            'step3': MLEnhancedStep3Worker(quiet=self.worker_config['quiet_mode']),
            'check': QualityChecker(),
            'submit': SmartSubmitter()
        }

        for stage, worker in self.workers.items():
            worker.max_concurrent = self.worker_config['max_concurrent_tasks']

        ui.checklist_item("工作器初始化完成")

    async def run_continuous_mining(self, duration_hours: Optional[int] = None):
        """运行持续挖掘模式"""
        self.running = True

        ui.section_header("🚀 启动持续因子挖掘")
        ui.info(
            f"挖掘时长: {'无限制' if duration_hours is None else f'{duration_hours} 小时'}")

        # 设置停止时间
        stop_time = None
        if duration_hours:
            stop_time = datetime.now() + timedelta(hours=duration_hours)
            ui.info(f"预计停止时间: {stop_time.strftime('%Y-%m-%d %H:%M:%S')}")

        try:
            # 主处理循环
            while self.running:
                # 检查是否到达停止时间
                if stop_time and datetime.now() >= stop_time:
                    ui.info("已达到设定的运行时长，准备停止")
                    break

                # 执行一轮处理
                await self._process_mining_cycle()

                # 显示状态和统计
                await self._show_cycle_status()

                # 休息间隔
                await asyncio.sleep(30)  # 30秒间隔

        except KeyboardInterrupt:
            ui.warning("收到停止信号，正在安全关闭...")
        except Exception as e:
            logger.error(f"持续挖掘过程中发生错误: {e}")
            ui.error(f"挖掘过程异常: {e}")
        finally:
            await self._graceful_shutdown()

    async def _process_mining_cycle(self):
        """处理一轮挖掘周期"""
        # 获取可用账户
        available_accounts = await self._get_available_accounts()

        if not available_accounts:
            ui.warning("暂无可用账户，跳过本轮处理")
            return

        # 并行处理各个阶段
        processing_tasks = []

        for stage, worker in self.workers.items():
            task = self._process_stage(stage, worker, available_accounts)
            processing_tasks.append(task)

        # 等待所有阶段完成
        await asyncio.gather(*processing_tasks, return_exceptions=True)

    async def _process_stage(self, stage: str, worker, accounts: List[UserAccountModel]):
        """处理特定阶段"""
        try:
            # 获取该阶段的待处理因子
            pending_factors = self.queue_manager.get_pending_factors(
                stage=stage,
                limit=self.worker_config['batch_size']
            )

            if not pending_factors:
                return  # 该阶段无待处理因子

            logger.info(f"阶段 {stage}: 开始处理 {len(pending_factors)} 个因子")

            # 分配账户并处理
            await worker.process_batch(pending_factors, accounts)

        except Exception as e:
            logger.error(f"阶段 {stage} 处理失败: {e}")

    async def _get_available_accounts(self) -> List[UserAccountModel]:
        """获取可用账户"""
        try:
            accounts = list(UserAccountModel.select().where(
                UserAccountModel.status == 'active'
            ))
            return accounts
        except Exception as e:
            logger.error(f"获取账户失败: {e}")
            return []

    async def _show_cycle_status(self):
        """显示周期状态"""
        try:
            # 获取队列统计
            stats = self.queue_manager.get_queue_stats()

            ui.info(f"📊 队列状态:")
            for stage in ['stage1', 'stage2', 'stage3', 'check', 'submit']:
                pending = stats['by_stage'].get(stage, 0)
                if pending > 0:
                    ui.info(f"  {stage.upper()}: {pending} 待处理")

            completed_today = stats['by_status'].get('completed', 0)
            ui.info(f"✅ 今日已完成: {completed_today}")

        except Exception as e:
            logger.error(f"显示状态失败: {e}")

    async def run_batch_mining(self, dataset: str, stage: str, count: int):
        """运行批量挖掘模式"""
        ui.section_header(f"📦 批量挖掘模式: {dataset} - {stage}")

        try:
            # 生成因子表达式（简化实现）
            expressions = await self._generate_factor_expressions(dataset, count)

            # 添加到队列
            batch_id = self.queue_manager.add_factors_batch(
                expressions=expressions,
                source_dataset=dataset,
                stage=stage,
                tags=[f'batch_{datetime.now().strftime("%Y%m%d_%H%M")}']
            )

            ui.success(f"已创建批次 {batch_id}，包含 {len(expressions)} 个因子")

            # 处理该批次
            await self._process_specific_batch(batch_id)

        except Exception as e:
            logger.error(f"批量挖掘失败: {e}")
            ui.error(f"批量挖掘失败: {e}")

    async def _generate_factor_expressions(self, dataset: str, count: int) -> List[str]:
        """使用真实的因子生成器生成表达式"""
        try:
            ui.info(f"正在为数据集 '{dataset}' 生成 {count} 个因子...")
            generator = FactorGenerator(dataset=dataset)
            expressions = await generator.generate_expressions(count=count)
            ui.success(f"成功生成 {len(expressions)} 个因子表达式")
            return expressions
        except Exception as e:
            logger.error(f"为数据集 {dataset} 生成因子表达式失败: {e}")
            ui.error(f"因子生成失败: {e}")
            # 返回空列表或重新引发异常，以表示失败
            raise

    async def _process_specific_batch(self, batch_id: str):
        """处理特定批次"""
        ui.info(f"开始处理批次: {batch_id}")

        while True:
            # 检查批次状态
            batch = AlphaBatchModel.get(AlphaBatchModel.batch_id == batch_id)

            if batch.status == 'completed':
                ui.success(f"批次 {batch_id} 处理完成")
                break

            # 继续处理
            await self._process_mining_cycle()
            await asyncio.sleep(10)

    async def show_system_status(self):
        """显示系统状态"""
        ui.section_header("📊 系统状态")

        try:
            # 队列状态
            stats = self.queue_manager.get_queue_stats() if self.queue_manager else {}

            if stats:
                ui.info("📋 队列统计:")
                for status, count in stats.get('by_status', {}).items():
                    ui.info(f"  {status}: {count}")

                ui.info("🎯 阶段分布:")
                for stage, count in stats.get('by_stage', {}).items():
                    ui.info(f"  {stage}: {count}")

            # 账户状态
            accounts = list(UserAccountModel.select())
            active_accounts = [
                acc for acc in accounts if acc.status == 'active']

            ui.info(f"👥 账户状态: {len(active_accounts)}/{len(accounts)} 活跃")

            # 今日统计
            today = datetime.now().date()
            today_completed = AlphaFactorQueueModel.select().where(
                AlphaFactorQueueModel.status == 'completed',
                AlphaFactorQueueModel.completed_at.is_null(False)
            ).count()

            ui.info(f"✅ 今日完成: {today_completed}")

        except Exception as e:
            logger.error(f"获取状态失败: {e}")
            ui.error(f"状态获取失败: {e}")

    async def manage_queue(self, action: str, **kwargs):
        """队列管理操作"""
        ui.section_header(f"🔧 队列管理: {action}")

        try:
            if action == 'cleanup':
                # 清理旧记录
                days = kwargs.get('days', 7)
                cleaned = self.queue_manager.cleanup_old_records(days)
                ui.success(f"清理了 {cleaned} 条旧记录")

            elif action == 'reset':
                # 重置失败的因子
                stage = kwargs.get('stage')
                reset_count = await self._reset_failed_factors(stage)
                ui.success(f"重置了 {reset_count} 个失败因子")

            elif action == 'stats':
                # 显示详细统计
                await self._show_detailed_stats()

        except Exception as e:
            logger.error(f"队列管理操作失败: {e}")
            ui.error(f"操作失败: {e}")

    async def _reset_failed_factors(self, stage: Optional[str]) -> int:
        """重置失败的因子"""
        query = AlphaFactorQueueModel.update(
            status='pending',
            retry_count=0,
            last_error=None
        ).where(AlphaFactorQueueModel.status == 'failed')

        if stage:
            query = query.where(AlphaFactorQueueModel.stage == stage)

        return query.execute()

    async def _show_detailed_stats(self):
        """显示详细统计"""
        ui.info("📈 详细统计信息:")

        # 按数据集统计
        dataset_stats = {}
        factors = AlphaFactorQueueModel.select()

        for factor in factors:
            dataset = factor.source_dataset
            if dataset not in dataset_stats:
                dataset_stats[dataset] = {'total': 0, 'completed': 0}

            dataset_stats[dataset]['total'] += 1
            if factor.status == 'completed':
                dataset_stats[dataset]['completed'] += 1

        for dataset, stats in dataset_stats.items():
            rate = stats['completed'] / \
                stats['total'] if stats['total'] > 0 else 0
            ui.info(
                f"  {dataset}: {stats['completed']}/{stats['total']} ({rate:.1%})")

    async def _graceful_shutdown(self):
        """优雅关闭"""
        ui.section_header("🛑 系统关闭")

        self.running = False

        # 等待正在进行的任务完成
        ui.info("等待正在进行的任务完成...")

        # 关闭通知系统
        if self.notification_manager:
            try:
                # 发送关闭通知
                await self.notification_manager.send_event(
                    event_type='system_shutdown',
                    title='系统关闭',
                    message='WQ因子挖掘系统已安全关闭。',
                    level=NotificationLevel.INFO
                )
                await self.notification_manager.stop()
            except:
                pass

        # 关闭数据库连接
        if self.db:
            self.db.close()

        ui.success("系统已安全关闭")

    def setup_signal_handlers(self):
        """设置信号处理器"""
        def signal_handler(signum, frame):
            ui.warning(f"收到信号 {signum}，准备关闭...")
            self.running = False

        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)


def ensure_system_ready() -> bool:
    """确保系统已准备就绪"""
    if not check_base_config_initialized() or not check_user_configured():
        ui.warning("系统配置不完整，正在进行快速设置...")
        return quick_setup()
    return True


def show_help():
    """显示帮助信息"""
    ui.section_header("📖 WQ 因子挖掘系统帮助")

    ui.info("🔧 基本用法:")
    ui.info("  python app.py                      # 交互式模式")
    ui.info("  python app.py --continuous        # 持续挖掘模式")
    ui.info("  python app.py --batch DATASET COUNT  # 批量挖掘")
    ui.info("  python app.py --status            # 查看系统状态")
    ui.info("")

    ui.info("⚙️ 高级选项:")
    ui.info("  --duration HOURS                  # 设置运行时长（小时）")
    ui.info("  --stage STAGE                     # 指定处理阶段")
    ui.info("  --quiet                           # 静默模式")
    ui.info("  --workers N                       # 设置工作器数量")
    ui.info("")

    ui.info("🔧 队列管理:")
    ui.info("  --queue-cleanup [DAYS]            # 清理旧记录")
    ui.info("  --queue-reset [STAGE]             # 重置失败因子")
    ui.info("  --queue-stats                     # 显示队列统计")
    ui.info("")

    ui.info("📋 系统特性:")
    ui.checklist_item("多阶段因子挖掘流程 (1-2-3-检查-提交)")
    ui.checklist_item("多账户智能调度")
    ui.checklist_item("实时监控和统计")
    ui.checklist_item("容错和自动恢复")
    ui.checklist_item("基于数据库的队列管理")


async def interactive_mode():
    """交互式模式"""
    ui.section_header("🎮 交互式因子挖掘模式")

    app = FactorMiningApplication()

    # 初始化系统
    if not await app.initialize():
        ui.error("系统初始化失败")
        return

    while True:
        ui.info("\n选择操作:")
        ui.info("1. 持续挖掘")
        ui.info("2. 批量挖掘")
        ui.info("3. 查看状态")
        ui.info("4. 队列管理")
        ui.info("5. 退出")

        try:
            choice = input("\n请输入选择 (1-5): ").strip()

            if choice == '1':
                duration = input("运行时长(小时，回车=无限制): ").strip()
                duration_hours = int(duration) if duration else None
                await app.run_continuous_mining(duration_hours)

            elif choice == '2':
                dataset = input("数据集名称: ").strip()
                count = int(input("因子数量: ").strip())
                stage = input(
                    "目标阶段 (stage1/stage2/stage3): ").strip() or 'stage1'
                await app.run_batch_mining(dataset, stage, count)

            elif choice == '3':
                await app.show_system_status()

            elif choice == '4':
                action = input("队列操作 (cleanup/reset/stats): ").strip()
                await app.manage_queue(action)

            elif choice == '5':
                break

            else:
                ui.warning("无效选择，请重试")

        except KeyboardInterrupt:
            ui.warning("操作被中断")
            break
        except Exception as e:
            ui.error(f"操作失败: {e}")

    await app._graceful_shutdown()


async def main():
    """主函数"""
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='WQ因子挖掘系统')
    parser.add_argument('--continuous', action='store_true', help='持续挖掘模式')
    parser.add_argument('--batch', nargs=2,
                        metavar=('DATASET', 'COUNT'), help='批量挖掘')
    parser.add_argument('--status', action='store_true', help='显示系统状态')
    parser.add_argument('--duration', type=int, help='运行时长（小时）')
    parser.add_argument(
        '--stage', choices=['stage1', 'stage2', 'stage3', 'check', 'submit'], help='处理阶段')
    parser.add_argument('--quiet', action='store_true', help='静默模式')
    parser.add_argument('--workers', type=int, default=10, help='工作器数量')
    parser.add_argument('--queue-cleanup', nargs='?',
                        const=7, type=int, help='清理队列旧记录')
    parser.add_argument('--queue-reset', nargs='?', const=None, help='重置失败因子')
    parser.add_argument('--queue-stats', action='store_true', help='显示队列统计')
    parser.add_argument('--help-full', action='store_true', help='显示完整帮助')

    args = parser.parse_args()

    # 显示帮助
    if args.help_full:
        show_help()
        return

    try:
        logger.info("启动WQ因子挖掘系统...")
        ui.section_header("🎯 WQ 因子挖掘系统")

        # 确保系统就绪
        if not ensure_system_ready():
            logger.error("系统未就绪，程序退出")
            ui.error("系统配置失败，请检查配置后重试")
            sys.exit(1)

        # 验证身份认证
        ui.progress("验证身份认证...")
        try:
            session = get_cached_session()
            ui.success("身份认证成功")
        except Exception as e:
            logger.error(f"身份认证失败: {e}")
            ui.error(f"身份认证失败: {e}")
            sys.exit(1)

        # 创建应用实例
        app = FactorMiningApplication()
        app.setup_signal_handlers()

        # 配置工作器
        app.worker_config.update({
            'max_concurrent_tasks': args.workers,
            'quiet_mode': args.quiet
        })

        # 根据参数执行相应操作
        if args.status:
            await app.show_system_status()

        elif args.queue_cleanup is not None:
            await app.manage_queue('cleanup', days=args.queue_cleanup)

        elif args.queue_reset is not None:
            await app.manage_queue('reset', stage=args.queue_reset)

        elif args.queue_stats:
            await app.manage_queue('stats')

        elif args.batch:
            dataset, count = args.batch
            stage = args.stage or 'stage1'
            if not await app.initialize():
                ui.error("系统初始化失败")
                return
            await app.run_batch_mining(dataset, stage, int(count))

        elif args.continuous:
            if not await app.initialize():
                ui.error("系统初始化失败")
                return
            await app.run_continuous_mining(args.duration)

        else:
            # 默认交互式模式
            await interactive_mode()

        ui.success("程序执行完成")

    except KeyboardInterrupt:
        ui.warning("程序被用户中断")
    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        ui.error(f"程序执行失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # 运行主程序
    asyncio.run(main())
