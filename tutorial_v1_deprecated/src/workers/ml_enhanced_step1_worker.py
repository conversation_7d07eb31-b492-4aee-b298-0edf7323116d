"""
ML增强的第一阶段因子处理器
基于原有digging_1step.py的逻辑，集成机器学习功能
"""

import asyncio
import random
import numpy as np
from typing import Dict, List, Tuple
from datetime import datetime
from lib.logger import get_logger
from lib.models import AlphaFactorQueueModel, UserAccountModel
from lib.base_worker import BaseWorker
from lib.ml import FactorIntelligenceEngine, GeneticFactorEvolution, FactorQualityPredictor

logger = get_logger(__name__)


class MLEnhancedStep1Worker(BaseWorker):
    """ML增强的第一阶段因子处理器"""

    def __init__(self, worker_name: str = "MLStep1Worker", quiet: bool = False):
        super().__init__(worker_name, quiet)

        # ML组件初始化
        self.ml_engine = FactorIntelligenceEngine()
        self.genetic_evolver = GeneticFactorEvolution()
        self.quality_predictor = FactorQualityPredictor()

        # 配置参数
        self.ml_enabled = True
        self.quality_threshold = 0.6  # 质量预筛选阈值
        self.intelligent_generation_ratio = 0.3  # 30%使用智能生成，70%传统方法

        # 性能统计
        self.ml_stats = {
            'total_processed': 0,
            'ml_generated': 0,
            'ml_filtered': 0,
            'ml_optimized': 0,
            'quality_improvements': []
        }

    async def process_batch(self, factors: List[AlphaFactorQueueModel],
                            accounts: List[UserAccountModel]):
        """ML增强的批量处理流程"""

        self.logger.info(f"🤖 ML增强模式处理 {len(factors)} 个因子")

        if not self.ml_enabled:
            # ML未启用时使用传统方法
            await super().process_batch(factors, accounts)
            return

        # === 第一步：智能因子生成 ===
        if self._should_generate_new_factors():
            await self._intelligent_factor_generation()

        # === 第二步：ML质量预筛选 ===
        qualified_factors = await self._ml_quality_prefilter(factors)

        # === 第三步：智能参数优化 ===
        optimized_factors = await self._ml_parameter_optimization(qualified_factors)

        # === 第四步：智能账户分配 ===
        smart_assignments = await self._ml_smart_assignment(optimized_factors, accounts)

        # === 第五步：执行处理与学习 ===
        await self._execute_with_ml_feedback(smart_assignments)

        # === 第六步：更新ML模型 ===
        await self._update_ml_models()

        # 输出ML统计信息
        self._log_ml_performance_stats()

    async def _intelligent_factor_generation(self):
        """智能因子生成 - 基于遗传算法和模式学习"""

        self.logger.info("🧬 开始智能因子生成...")

        # 1. 分析当前队列需求
        queue_analysis = await self._analyze_queue_needs()

        # 2. 基于历史成功模式生成因子
        successful_patterns = await self._extract_successful_patterns()

        # 3. 遗传算法进化生成
        for dataset, need_count in queue_analysis.items():
            if need_count <= 0:
                continue

            self.logger.info(f"🎯 为数据集 {dataset} 生成 {need_count} 个智能因子")

            # 设置进化目标
            evolution_targets = {
                'sharpe_threshold': 1.5,
                'fitness_threshold': 0.3,
                'max_turnover': 0.4,
                'diversity_factor': 0.8  # 确保多样性
            }

            # 遗传算法生成
            evolved_factors = await self.genetic_evolver.evolve_factors(
                base_dataset=dataset,
                target_metrics=evolution_targets,
                population_size=50,
                generations=20,
                target_count=need_count
            )

            # 质量预评估
            quality_scores = await self.quality_predictor.predict_batch(evolved_factors)

            # 选择高质量因子
            high_quality_factors = [
                factor for factor, score in zip(evolved_factors, quality_scores)
                if score >= self.quality_threshold
            ]

            if high_quality_factors:
                # 添加到队列
                batch_id = self.queue.add_factors_batch(
                    expressions=high_quality_factors,
                    source_dataset=dataset,
                    stage='stage1',
                    tags=['ml_generated', 'genetic_evolution'],
                    priority=75  # 高优先级
                )

                self.ml_stats['ml_generated'] += len(high_quality_factors)
                self.logger.info(f"✅ 成功生成 {len(high_quality_factors)} 个高质量因子")

    async def _ml_quality_prefilter(self, factors: List[AlphaFactorQueueModel]) -> List[AlphaFactorQueueModel]:
        """ML质量预筛选 - 过滤低质量因子避免无效计算"""

        if not factors:
            return factors

        self.logger.info(f"🔍 ML质量预筛选 {len(factors)} 个因子...")

        # 提取因子表达式
        expressions = [f.factor_expression for f in factors]

        # 批量质量预测
        quality_scores = await self.quality_predictor.predict_batch(expressions)

        # 筛选高质量因子
        qualified_factors = []
        filtered_count = 0

        for factor, score in zip(factors, quality_scores):
            if score >= self.quality_threshold:
                # 记录质量分数
                factor.ml_quality_score = score
                qualified_factors.append(factor)
            else:
                # 更新状态为ML筛选过滤
                self.queue.update_factor_status(
                    factor.id,
                    'ml_filtered',
                    error=f"ML质量预筛选：分数 {score:.3f} 低于阈值 {self.quality_threshold}"
                )
                filtered_count += 1

        self.ml_stats['ml_filtered'] += filtered_count
        filter_ratio = filtered_count / len(factors) if factors else 0

        self.logger.info(
            f"📊 ML预筛选完成：保留 {len(qualified_factors)}/{len(factors)} ({1-filter_ratio:.1%})")

        return qualified_factors

    async def _ml_parameter_optimization(self, factors: List[AlphaFactorQueueModel]) -> List[AlphaFactorQueueModel]:
        """ML参数优化 - 为每个因子寻找最优参数"""

        if not factors:
            return factors

        self.logger.info(f"⚙️ ML参数优化 {len(factors)} 个因子...")

        optimized_factors = []

        # 并行优化控制
        semaphore = asyncio.Semaphore(5)  # 限制并发优化数量

        async def optimize_single_factor(factor: AlphaFactorQueueModel) -> AlphaFactorQueueModel:
            async with semaphore:
                try:
                    # 使用贝叶斯优化寻找最优参数
                    optimal_params = await self.ml_engine.bayesian_optimize_parameters(
                        factor.factor_expression,
                        optimization_budget=20,  # 限制评估次数
                        target_metrics=['sharpe', 'fitness', 'turnover']
                    )

                    # 更新因子参数
                    original_params = {
                        'delay': factor.delay,
                        'decay': factor.decay,
                        'universe': factor.universe
                    }

                    factor.delay = optimal_params['delay']
                    factor.decay = optimal_params['decay']
                    factor.universe = optimal_params['universe']
                    factor.neutralization = optimal_params.get(
                        'neutralization', factor.neutralization)

                    # 保存原始参数用于对比
                    factor.original_params = original_params
                    factor.optimized_params = optimal_params
                    factor.ml_optimized = True

                    factor.save()

                    self.logger.info(f"🎯 因子 #{factor.id} 参数优化完成")
                    return factor

                except Exception as e:
                    self.logger.error(f"参数优化失败 #{factor.id}: {e}")
                    # 保持原参数
                    factor.ml_optimized = False
                    return factor

        # 并行执行参数优化
        optimization_tasks = [optimize_single_factor(
            factor) for factor in factors]
        optimized_factors = await asyncio.gather(*optimization_tasks)

        optimized_count = sum(
            1 for f in optimized_factors if getattr(f, 'ml_optimized', False))
        self.ml_stats['ml_optimized'] += optimized_count

        self.logger.info(f"✅ 参数优化完成：{optimized_count}/{len(factors)} 个因子成功优化")

        return optimized_factors

    async def _ml_smart_assignment(self, factors: List[AlphaFactorQueueModel],
                                   accounts: List[UserAccountModel]) -> Dict[int, List[AlphaFactorQueueModel]]:
        """ML智能账户分配 - 基于因子特征和账户历史表现"""

        if not factors or not accounts:
            return {}

        self.logger.info(f"🎯 ML智能分配 {len(factors)} 个因子到 {len(accounts)} 个账户")

        # 使用ML预测每个账户处理每个因子的成功概率
        assignment_matrix = await self._predict_assignment_matrix(factors, accounts)

        # 使用匈牙利算法求解最优分配
        optimal_assignment = await self._solve_optimal_assignment(
            factors, accounts, assignment_matrix
        )

        # 记录分配决策用于后续学习
        await self._record_assignment_decisions(optimal_assignment, assignment_matrix)

        return optimal_assignment

    async def _predict_assignment_matrix(self, factors: List[AlphaFactorQueueModel],
                                         accounts: List[UserAccountModel]) -> np.ndarray:
        """预测分配矩阵 - 每个账户处理每个因子的预期成功率"""

        matrix = np.zeros((len(factors), len(accounts)))

        for i, factor in enumerate(factors):
            for j, account in enumerate(accounts):
                # 提取特征
                factor_features = self._extract_factor_features(factor)
                account_features = await self._extract_account_features(account)

                # 预测成功率
                success_prob = await self.ml_engine.predict_processing_success(
                    factor_features, account_features
                )

                matrix[i][j] = success_prob

        return matrix

    async def _execute_with_ml_feedback(self, assignments: Dict[int, List[AlphaFactorQueueModel]]):
        """执行处理并收集ML反馈数据"""

        execution_results = []

        for account_id, assigned_factors in assignments.items():
            account = UserAccountModel.get_by_id(account_id)

            self.logger.info(
                f"🚀 账户 {account.name} 处理 {len(assigned_factors)} 个因子")

            # 预测批次性能
            batch_prediction = await self._predict_batch_performance(assigned_factors, account)

            # 执行处理
            batch_results = await self._process_account_batch(assigned_factors, account)

            # 记录执行结果用于模型训练
            for factor, result in zip(assigned_factors, batch_results):
                execution_results.append({
                    'factor_id': factor.id,
                    'factor_expression': factor.factor_expression,
                    'account_id': account.id,
                    'ml_quality_score': getattr(factor, 'ml_quality_score', 0),
                    'ml_optimized': getattr(factor, 'ml_optimized', False),
                    'predicted_success': batch_prediction.get('expected_success_rate', 0.5),
                    'actual_result': result,
                    'processing_time': result.get('duration', 0),
                    'success': result.get('success', False)
                })

        # 存储执行结果用于模型更新
        self.execution_feedback = execution_results

    async def _update_ml_models(self):
        """基于执行反馈更新ML模型"""

        if not hasattr(self, 'execution_feedback') or not self.execution_feedback:
            return

        self.logger.info("🔄 更新ML模型...")

        # 更新质量预测模型
        await self._update_quality_predictor()

        # 更新参数优化模型
        await self._update_parameter_optimizer()

        # 更新账户分配模型
        await self._update_assignment_predictor()

        # 计算模型改进指标
        improvement_metrics = await self._calculate_model_improvements()
        self.ml_stats['quality_improvements'].append(improvement_metrics)

        self.logger.info("✅ ML模型更新完成")

    def _log_ml_performance_stats(self):
        """输出ML性能统计"""

        stats = self.ml_stats

        if not self.quiet:
            self.logger.info("📊 ML性能统计:")
            self.logger.info(f"  - 总处理因子: {stats['total_processed']}")
            self.logger.info(f"  - ML生成因子: {stats['ml_generated']}")
            self.logger.info(f"  - ML过滤因子: {stats['ml_filtered']}")
            self.logger.info(f"  - ML优化因子: {stats['ml_optimized']}")

            if stats['quality_improvements']:
                avg_improvement = np.mean(
                    [imp['overall_score'] for imp in stats['quality_improvements']])
                self.logger.info(f"  - 平均质量提升: {avg_improvement:.2%}")

    # === 传统方法兼容性 ===

    async def execute_factor_processing(self, factor: AlphaFactorQueueModel,
                                        account: UserAccountModel) -> Dict:
        """执行因子处理 - 兼容原有接口"""

        # 保持与原有BaseWorker接口兼容
        try:
            # 构建请求参数
            request_data = {
                'expression': factor.factor_expression,
                'universe': factor.universe or 'TOP3000',
                'region': factor.region or 'CN',
                'delay': factor.delay or 1,
                'decay': factor.decay or 9,
                'neutralization': factor.neutralization or 'SUBINDUSTRY'
            }

            # 调用API
            response = await self.api_client.robust_request(
                '/alpha/simulation',
                request_data,
                account
            )

            # 解析结果
            result = self.parse_simulation_result(response)

            # ML增强：记录处理结果
            if self.ml_enabled:
                await self._record_processing_result(factor, account, result)

            # 判断是否晋级到第二阶段
            if self.meets_stage2_criteria(result):
                await self.promote_to_stage2(factor, result)

            return result

        except Exception as e:
            self.logger.error(f"因子处理失败: {e}")
            raise

    def meets_stage2_criteria(self, result: Dict) -> bool:
        """判断是否符合第二阶段条件 - 可ML增强"""

        # 基础条件（保持兼容）
        basic_criteria = (
            result.get('sharpe', 0) > 1.5 and
            result.get('turnover', 1) < 0.3
        )

        # ML增强判断
        if self.ml_enabled and hasattr(self, 'ml_engine'):
            ml_score = self.ml_engine.predict_stage2_potential(result)
            return basic_criteria and ml_score > 0.7

        return basic_criteria

    # === 辅助方法 ===

    def _should_generate_new_factors(self) -> bool:
        """判断是否需要生成新因子"""
        # 检查队列深度，如果pending因子少于阈值则生成
        queue_depth = self.queue.get_queue_depth('stage1')
        return queue_depth < 50  # 阈值可配置

    async def _analyze_queue_needs(self) -> Dict[str, int]:
        """分析队列需求"""
        # 分析各数据集的队列深度，确定需要生成的因子数量
        datasets = ['analyst4', 'news18', 'fundamental']
        needs = {}

        for dataset in datasets:
            current_count = self.queue.get_dataset_queue_depth(
                dataset, 'stage1')
            target_count = 100  # 目标数量
            need_count = max(0, target_count - current_count)
            needs[dataset] = need_count

        return needs

    async def _extract_successful_patterns(self) -> List[Dict]:
        """提取历史成功因子的模式"""
        # 查询历史高质量因子
        successful_factors = (AlphaFactorQueueModel
                              .select()
                              .where(
                                  AlphaFactorQueueModel.sharpe > 2.0,
                                  AlphaFactorQueueModel.fitness > 0.5
                              )
                              .limit(100))

        patterns = []
        for factor in successful_factors:
            pattern = {
                'expression': factor.factor_expression,
                'performance': {
                    'sharpe': factor.sharpe,
                    'fitness': factor.fitness,
                    'turnover': factor.turnover
                },
                'parameters': {
                    'delay': factor.delay,
                    'decay': factor.decay,
                    'universe': factor.universe
                }
            }
            patterns.append(pattern)

        return patterns
