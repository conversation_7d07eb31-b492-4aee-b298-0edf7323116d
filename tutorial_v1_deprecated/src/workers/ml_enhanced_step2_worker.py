"""
ML增强的第二阶段因子处理器
基于原有digging_2step.py的逻辑，集成智能因子组合和参数优化
"""

import asyncio
import numpy as np
from typing import Dict, List, Optional
from datetime import datetime
from lib.logger import get_logger
from lib.models import AlphaFactorQueueModel, UserAccountModel
from lib.base_worker import BaseWorker
from lib.ml import IntelligentFactorCombiner, MultiObjectiveOptimizer, PerformancePredictor

logger = get_logger(__name__)


class MLEnhancedStep2Worker(BaseWorker):
    """ML增强的第二阶段因子处理器"""

    def __init__(self, worker_name: str = "MLStep2Worker", quiet: bool = False):
        super().__init__(worker_name, quiet)

        # ML组件
        self.factor_combiner = IntelligentFactorCombiner()
        self.parameter_optimizer = MultiObjectiveOptimizer()
        self.performance_predictor = PerformancePredictor()

        # 配置参数
        self.ml_enabled = True
        self.combination_strategies = ['linear', 'nonlinear', 'conditional']
        self.max_combination_complexity = 3  # 最大组合复杂度

        # 性能统计
        self.ml_stats = {
            'combinations_generated': 0,
            'parameters_optimized': 0,
            'performance_predictions': 0,
            'improvement_rate': 0.0
        }

    async def process_batch(self, factors: List[AlphaFactorQueueModel],
                            accounts: List[UserAccountModel]):
        """ML增强的第二阶段批量处理"""

        self.logger.info(f"🔄 ML增强第二阶段处理 {len(factors)} 个因子")

        if not self.ml_enabled:
            await super().process_batch(factors, accounts)
            return

        # === 步骤1：智能因子选择与分组 ===
        factor_groups = await self._intelligent_factor_grouping(factors)

        # === 步骤2：多策略因子组合生成 ===
        combined_factors = await self._multi_strategy_combination(factor_groups)

        # === 步骤3：ML驱动的参数优化 ===
        optimized_factors = await self._ml_parameter_optimization(combined_factors)

        # === 步骤4：性能预测与筛选 ===
        predicted_factors = await self._performance_prediction_filter(optimized_factors)

        # === 步骤5：智能执行与反馈学习 ===
        await self._execute_with_learning(predicted_factors, accounts)

        self._log_ml_stage2_stats()

    async def _intelligent_factor_grouping(self, factors: List[AlphaFactorQueueModel]) -> Dict[str, List[AlphaFactorQueueModel]]:
        """智能因子分组 - 基于相似性和互补性"""

        self.logger.info("🎯 智能因子分组...")

        # 提取因子特征
        factor_features = []
        for factor in factors:
            features = await self._extract_factor_features(factor)
            factor_features.append(features)

        # 使用ML聚类算法分组
        groups = await self.factor_combiner.intelligent_grouping(
            factors, factor_features,
            max_groups=5,
            similarity_threshold=0.7
        )

        self.logger.info(f"📊 分组完成：{len(groups)} 个因子组")
        return groups

    async def _multi_strategy_combination(self, factor_groups: Dict[str, List[AlphaFactorQueueModel]]) -> List[AlphaFactorQueueModel]:
        """多策略因子组合生成"""

        self.logger.info("🧬 多策略因子组合生成...")

        combined_factors = []

        for group_name, group_factors in factor_groups.items():
            if len(group_factors) < 2:
                continue  # 单个因子无法组合

            self.logger.info(f"🔄 处理分组 {group_name}：{len(group_factors)} 个因子")

            # 为每个组合策略生成因子
            for strategy in self.combination_strategies:
                try:
                    # 生成组合因子
                    strategy_combinations = await self._generate_combinations_by_strategy(
                        group_factors, strategy
                    )

                    combined_factors.extend(strategy_combinations)
                    self.ml_stats['combinations_generated'] += len(
                        strategy_combinations)

                except Exception as e:
                    self.logger.error(f"组合策略 {strategy} 失败: {e}")

        self.logger.info(f"✅ 生成 {len(combined_factors)} 个组合因子")
        return combined_factors

    async def _generate_combinations_by_strategy(self, factors: List[AlphaFactorQueueModel],
                                                 strategy: str) -> List[AlphaFactorQueueModel]:
        """按策略生成组合因子"""

        combinations = []

        if strategy == 'linear':
            # 线性组合：加权平均
            combinations = await self._generate_linear_combinations(factors)

        elif strategy == 'nonlinear':
            # 非线性组合：乘积、比率等
            combinations = await self._generate_nonlinear_combinations(factors)

        elif strategy == 'conditional':
            # 条件组合：基于条件的因子切换
            combinations = await self._generate_conditional_combinations(factors)

        return combinations

    async def _generate_linear_combinations(self, factors: List[AlphaFactorQueueModel]) -> List[AlphaFactorQueueModel]:
        """生成线性组合因子"""

        combinations = []

        # 两两组合
        for i in range(len(factors)):
            for j in range(i + 1, len(factors)):
                factor1, factor2 = factors[i], factors[j]

                # 预测最优权重
                optimal_weights = await self.factor_combiner.predict_optimal_weights(
                    factor1.factor_expression, factor2.factor_expression
                )

                w1, w2 = optimal_weights['weight1'], optimal_weights['weight2']

                # 生成加权组合表达式
                combined_expr = f"({w1:.3f} * ({factor1.factor_expression}) + {w2:.3f} * ({factor2.factor_expression}))"

                # 创建新因子记录
                combined_factor = AlphaFactorQueueModel(
                    factor_expression=combined_expr,
                    source_dataset=factor1.source_dataset,
                    stage='stage2',
                    status='pending',
                    tags=f"{factor1.tags},linear_combination",
                    priority=factor1.priority + 10,  # 提高优先级
                    submitted_at=datetime.now()
                )

                combinations.append(combined_factor)

        return combinations

    async def _generate_nonlinear_combinations(self, factors: List[AlphaFactorQueueModel]) -> List[AlphaFactorQueueModel]:
        """生成非线性组合因子"""

        combinations = []

        # 乘积组合
        for i in range(len(factors)):
            for j in range(i + 1, len(factors)):
                factor1, factor2 = factors[i], factors[j]

                # 乘积组合
                product_expr = f"rank(({factor1.factor_expression}) * ({factor2.factor_expression}))"

                # 比率组合
                ratio_expr = f"rank(({factor1.factor_expression}) / (abs({factor2.factor_expression}) + 0.001))"

                for expr, comb_type in [(product_expr, 'product'), (ratio_expr, 'ratio')]:
                    combined_factor = AlphaFactorQueueModel(
                        factor_expression=expr,
                        source_dataset=factor1.source_dataset,
                        stage='stage2',
                        status='pending',
                        tags=f"{factor1.tags},nonlinear_{comb_type}",
                        priority=factor1.priority + 5,
                        submitted_at=datetime.now()
                    )
                    combinations.append(combined_factor)

        return combinations

    async def _generate_conditional_combinations(self, factors: List[AlphaFactorQueueModel]) -> List[AlphaFactorQueueModel]:
        """生成条件组合因子"""

        combinations = []

        # 条件切换组合
        for i in range(len(factors)):
            for j in range(i + 1, len(factors)):
                factor1, factor2 = factors[i], factors[j]

                # 基于市场条件的因子切换
                conditions = [
                    f"ts_mean(volume, 5) > ts_mean(volume, 20)",  # 高成交量条件
                    # 高波动条件
                    f"ts_std(close, 10) > ts_mean(ts_std(close, 10), 20)",
                    f"close > ts_mean(close, 20)"  # 上涨趋势条件
                ]

                for condition in conditions:
                    conditional_expr = f"if({condition}, {factor1.factor_expression}, {factor2.factor_expression})"

                    combined_factor = AlphaFactorQueueModel(
                        factor_expression=conditional_expr,
                        source_dataset=factor1.source_dataset,
                        stage='stage2',
                        status='pending',
                        tags=f"{factor1.tags},conditional_combination",
                        priority=factor1.priority + 15,  # 条件组合优先级更高
                        submitted_at=datetime.now()
                    )
                    combinations.append(combined_factor)

        return combinations

    async def _ml_parameter_optimization(self, factors: List[AlphaFactorQueueModel]) -> List[AlphaFactorQueueModel]:
        """ML驱动的参数优化"""

        self.logger.info(f"⚙️ ML参数优化 {len(factors)} 个组合因子...")

        optimized_factors = []

        for factor in factors:
            try:
                # 基于turnover的智能decay调整（保持原有逻辑）
                predicted_turnover = await self._predict_factor_turnover(factor)
                optimal_decay = self._calculate_optimal_decay(
                    predicted_turnover)

                # ML多目标参数优化
                optimal_params = await self.parameter_optimizer.optimize_factor_parameters(
                    factor.factor_expression,
                    base_params={
                        'decay': optimal_decay,
                        'delay': factor.delay or 1,
                        'universe': factor.universe or 'TOP3000'
                    },
                    constraints={
                        'max_turnover': 0.3,
                        'min_sharpe': 1.0,
                        'max_drawdown': 0.15
                    }
                )

                # 更新因子参数
                factor.decay = optimal_params['decay']
                factor.delay = optimal_params['delay']
                factor.universe = optimal_params['universe']
                factor.neutralization = optimal_params.get(
                    'neutralization', 'SUBINDUSTRY')

                # 记录优化信息
                factor.ml_optimized = True
                factor.optimization_score = optimal_params.get(
                    'optimization_score', 0)

                optimized_factors.append(factor)
                self.ml_stats['parameters_optimized'] += 1

            except Exception as e:
                self.logger.error(f"参数优化失败: {e}")
                # 使用传统decay计算作为备选
                factor.decay = self._calculate_optimal_decay(0.2)  # 默认turnover
                optimized_factors.append(factor)

        self.logger.info(
            f"✅ 参数优化完成：{self.ml_stats['parameters_optimized']} 个因子")
        return optimized_factors

    async def _performance_prediction_filter(self, factors: List[AlphaFactorQueueModel]) -> List[AlphaFactorQueueModel]:
        """性能预测与筛选"""

        self.logger.info(f"📈 性能预测筛选 {len(factors)} 个因子...")

        # 批量性能预测
        expressions = [f.factor_expression for f in factors]
        predictions = await self.performance_predictor.predict_batch_performance(expressions)

        # 筛选高质量因子
        filtered_factors = []
        for factor, prediction in zip(factors, predictions):
            predicted_score = prediction['composite_score']

            # 设置筛选阈值
            if predicted_score >= 0.65:  # 较高的第二阶段阈值
                factor.predicted_performance = prediction
                filtered_factors.append(factor)
                self.ml_stats['performance_predictions'] += 1
            else:
                # 更新状态为预测筛选过滤
                self.queue.update_factor_status(
                    factor.id,
                    'ml_predicted_low',
                    error=f"ML性能预测分数过低: {predicted_score:.3f}"
                )

        filter_ratio = len(filtered_factors) / len(factors) if factors else 0
        self.logger.info(
            f"📊 性能预测筛选：保留 {len(filtered_factors)}/{len(factors)} ({filter_ratio:.1%})")

        return filtered_factors

    async def _execute_with_learning(self, factors: List[AlphaFactorQueueModel],
                                     accounts: List[UserAccountModel]):
        """执行处理并学习反馈"""

        execution_results = []

        # 智能分配和执行
        assignments = await self._smart_account_assignment(factors, accounts)

        for account_id, assigned_factors in assignments.items():
            account = UserAccountModel.get_by_id(account_id)

            for factor in assigned_factors:
                # 执行处理
                result = await self.execute_factor_processing(factor, account)

                # 记录执行结果
                execution_results.append({
                    'factor': factor,
                    'result': result,
                    'account': account,
                    'predicted_performance': getattr(factor, 'predicted_performance', {}),
                    'optimization_score': getattr(factor, 'optimization_score', 0)
                })

        # 学习和模型更新
        await self._learn_from_execution_results(execution_results)

    async def _learn_from_execution_results(self, execution_results: List[Dict]):
        """从执行结果中学习"""

        self.logger.info("🧠 从执行结果中学习...")

        # 计算预测准确性
        prediction_accuracy = self._calculate_prediction_accuracy(
            execution_results)

        # 更新组合策略权重
        await self._update_combination_strategies(execution_results)

        # 更新参数优化模型
        await self._update_parameter_models(execution_results)

        # 计算改进率
        self.ml_stats['improvement_rate'] = prediction_accuracy

        self.logger.info(f"📊 学习完成，预测准确率: {prediction_accuracy:.1%}")

    # === 传统方法兼容 ===

    async def execute_factor_processing(self, factor: AlphaFactorQueueModel,
                                        account: UserAccountModel) -> Dict:
        """执行因子处理 - 兼容原有接口"""

        try:
            # 构建请求参数
            request_data = {
                'expression': factor.factor_expression,
                'universe': factor.universe or 'TOP3000',
                'region': factor.region or 'CN',
                'delay': factor.delay or 1,
                'decay': factor.decay or 6,
                'neutralization': factor.neutralization or 'SUBINDUSTRY'
            }

            # 调用API
            response = await self.api_client.robust_request(
                '/alpha/simulation',
                request_data,
                account
            )

            # 解析结果
            result = self.parse_simulation_result(response)

            # 判断是否晋级到第三阶段
            if self.meets_stage3_criteria(result):
                await self.promote_to_stage3(factor, result)

            return result

        except Exception as e:
            self.logger.error(f"第二阶段因子处理失败: {e}")
            raise

    def _calculate_optimal_decay(self, turnover: float) -> int:
        """根据turnover计算最优decay - 保持原有逻辑"""
        if turnover < 0.1:
            return 15
        elif turnover < 0.2:
            return 12
        elif turnover < 0.3:
            return 9
        else:
            return 6

    def meets_stage3_criteria(self, result: Dict) -> bool:
        """判断是否符合第三阶段条件"""
        return (result.get('sharpe', 0) > 1.0 and
                result.get('fitness', 0) > 0.75)

    def _log_ml_stage2_stats(self):
        """输出第二阶段ML统计"""

        stats = self.ml_stats

        if not self.quiet:
            self.logger.info("📊 第二阶段ML统计:")
            self.logger.info(f"  - 生成组合: {stats['combinations_generated']}")
            self.logger.info(f"  - 优化参数: {stats['parameters_optimized']}")
            self.logger.info(f"  - 性能预测: {stats['performance_predictions']}")
            self.logger.info(f"  - 改进率: {stats['improvement_rate']:.1%}")
