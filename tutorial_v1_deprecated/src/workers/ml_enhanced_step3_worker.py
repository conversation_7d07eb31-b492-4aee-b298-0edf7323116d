"""
ML增强的第三阶段因子处理器
基于原有digging_3step.py的逻辑，集成智能trade_when因子生成
"""

import asyncio
import numpy as np
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from lib.logger import get_logger
from lib.models import AlphaFactorQueueModel, UserAccountModel
from lib.base_worker import BaseWorker
from lib.ml import IntelligentTradeWhenGenerator, MarketRegimeDetector, FactorTimingOptimizer

logger = get_logger(__name__)


class MLEnhancedStep3Worker(BaseWorker):
    """ML增强的第三阶段因子处理器 - 智能交易条件生成"""

    def __init__(self, worker_name: str = "MLStep3Worker", quiet: bool = False):
        super().__init__(worker_name, quiet)

        # ML组件
        self.trade_when_generator = IntelligentTradeWhenGenerator()
        self.market_regime_detector = MarketRegimeDetector()
        self.timing_optimizer = FactorTimingOptimizer()

        # 配置参数
        self.ml_enabled = True
        self.max_trade_when_variants = 8  # 每个基础因子最大变体数
        self.min_improvement_threshold = 0.1  # 最小改进阈值

        # 性能统计
        self.ml_stats = {
            'trade_when_generated': 0,
            'regime_adaptations': 0,
            'timing_optimizations': 0,
            'avg_improvement': 0.0
        }

    async def process_batch(self, factors: List[AlphaFactorQueueModel],
                            accounts: List[UserAccountModel]):
        """ML增强的第三阶段批量处理"""

        self.logger.info(f"⚡ ML增强第三阶段处理 {len(factors)} 个因子")

        if not self.ml_enabled:
            await super().process_batch(factors, accounts)
            return

        # === 步骤1：市场环境分析与regime识别 ===
        market_regime = await self._analyze_current_market_regime()

        # === 步骤2：智能trade_when条件生成 ===
        trade_when_factors = await self._intelligent_trade_when_generation(factors, market_regime)

        # === 步骤3：时机优化与条件细化 ===
        optimized_factors = await self._timing_optimization(trade_when_factors, market_regime)

        # === 步骤4：多scenario测试与选择 ===
        validated_factors = await self._multi_scenario_validation(optimized_factors)

        # === 步骤5：执行处理与效果学习 ===
        await self._execute_with_regime_adaptation(validated_factors, accounts)

        self._log_ml_stage3_stats()

    async def _analyze_current_market_regime(self) -> Dict:
        """分析当前市场环境"""

        self.logger.info("🔍 分析当前市场环境...")

        # 检测市场regime
        regime_info = await self.market_regime_detector.detect_current_regime()

        regime_summary = {
            # 'low', 'medium', 'high'
            'volatility_regime': regime_info['volatility_level'],
            # 'up', 'down', 'sideways'
            'trend_regime': regime_info['trend_direction'],
            # 'normal', 'high', 'low'
            'volume_regime': regime_info['volume_pattern'],
            # 'low', 'medium', 'high'
            'correlation_regime': regime_info['correlation_level'],
            'market_stress': regime_info['stress_indicator'],      # 0-1 score
            'regime_confidence': regime_info['confidence']         # 预测置信度
        }

        self.logger.info(
            f"📊 市场regime: {regime_summary['volatility_regime']}波动 + {regime_summary['trend_regime']}趋势")
        return regime_summary

    async def _intelligent_trade_when_generation(self, factors: List[AlphaFactorQueueModel],
                                                 market_regime: Dict) -> List[AlphaFactorQueueModel]:
        """智能生成trade_when因子变体"""

        self.logger.info(f"🧬 智能生成trade_when因子变体...")

        all_trade_when_factors = []

        for base_factor in factors:
            try:
                # 基于市场regime选择合适的trade_when策略
                strategies = await self._select_regime_appropriate_strategies(
                    base_factor, market_regime
                )

                # 为每个策略生成trade_when变体
                factor_variants = []
                for strategy in strategies:
                    variants = await self._generate_strategy_variants(
                        base_factor, strategy, market_regime
                    )
                    factor_variants.extend(variants)

                # 限制变体数量
                if len(factor_variants) > self.max_trade_when_variants:
                    # 使用ML模型选择最有潜力的变体
                    factor_variants = await self._select_best_variants(
                        factor_variants, self.max_trade_when_variants
                    )

                all_trade_when_factors.extend(factor_variants)
                self.ml_stats['trade_when_generated'] += len(factor_variants)

                self.logger.info(
                    f"✅ 因子 #{base_factor.id} 生成 {len(factor_variants)} 个变体")

            except Exception as e:
                self.logger.error(f"生成trade_when变体失败: {e}")
                continue

        self.logger.info(f"🎯 总计生成 {len(all_trade_when_factors)} 个trade_when因子")
        return all_trade_when_factors

    async def _select_regime_appropriate_strategies(self, factor: AlphaFactorQueueModel,
                                                    market_regime: Dict) -> List[str]:
        """根据市场regime选择合适的trade_when策略"""

        # 基础策略库
        all_strategies = {
            'momentum': ['momentum_continuation', 'momentum_acceleration'],
            'reversal': ['mean_reversion', 'extreme_reversal'],
            'volatility': ['volatility_breakout', 'volatility_compression'],
            'volume': ['volume_confirmation', 'unusual_volume'],
            'timing': ['technical_timing', 'fundamental_timing'],
            'regime_adaptive': ['regime_switch', 'stability_detection']
        }

        # 根据市场regime筛选策略
        selected_strategies = []

        # 高波动环境偏好反转策略
        if market_regime['volatility_regime'] == 'high':
            selected_strategies.extend(all_strategies['reversal'])
            selected_strategies.extend(all_strategies['volatility'])

        # 低波动环境偏好动量策略
        elif market_regime['volatility_regime'] == 'low':
            selected_strategies.extend(all_strategies['momentum'])
            selected_strategies.extend(all_strategies['timing'])

        # 趋势明确时使用动量策略
        if market_regime['trend_regime'] in ['up', 'down']:
            selected_strategies.extend(all_strategies['momentum'])

        # 横盘市场使用反转策略
        elif market_regime['trend_regime'] == 'sideways':
            selected_strategies.extend(all_strategies['reversal'])

        # 总是包含regime适应性策略
        selected_strategies.extend(all_strategies['regime_adaptive'])

        # 去重并限制数量
        selected_strategies = list(set(selected_strategies))[:6]

        return selected_strategies

    async def _generate_strategy_variants(self, base_factor: AlphaFactorQueueModel,
                                          strategy: str, market_regime: Dict) -> List[AlphaFactorQueueModel]:
        """为特定策略生成因子变体"""

        base_expr = base_factor.factor_expression
        variants = []

        # 根据策略类型生成不同的trade_when条件
        if strategy == 'momentum_continuation':
            variants = self._generate_momentum_variants(base_factor, base_expr)

        elif strategy == 'mean_reversion':
            variants = self._generate_reversal_variants(base_factor, base_expr)

        elif strategy == 'volatility_breakout':
            variants = self._generate_volatility_variants(
                base_factor, base_expr)

        elif strategy == 'volume_confirmation':
            variants = self._generate_volume_variants(base_factor, base_expr)

        elif strategy == 'regime_switch':
            variants = await self._generate_regime_adaptive_variants(base_factor, base_expr, market_regime)

        elif strategy == 'technical_timing':
            variants = self._generate_technical_timing_variants(
                base_factor, base_expr)

        return variants

    def _generate_momentum_variants(self, base_factor: AlphaFactorQueueModel, base_expr: str) -> List[AlphaFactorQueueModel]:
        """生成动量策略变体"""

        variants = []
        momentum_conditions = [
            f"{base_expr} > delay({base_expr}, 1)",  # 因子值上升
            f"{base_expr} > delay({base_expr}, 2)",  # 2日动量
            f"{base_expr} > ts_mean({base_expr}, 5)",  # 高于5日均值
            f"delta({base_expr}, 1) > 0",  # 一阶差分为正
            f"ts_mean({base_expr}, 3) > ts_mean({base_expr}, 10)"  # 短期均值高于长期
        ]

        for i, condition in enumerate(momentum_conditions):
            trade_when_expr = f"trade_when({base_expr}, {condition})"

            variant = AlphaFactorQueueModel(
                factor_expression=trade_when_expr,
                source_dataset=base_factor.source_dataset,
                stage='stage3',
                status='pending',
                tags=f"{base_factor.tags},trade_when,momentum_{i}",
                priority=base_factor.priority + 20,
                submitted_at=datetime.now()
            )
            variants.append(variant)

        return variants

    def _generate_reversal_variants(self, base_factor: AlphaFactorQueueModel, base_expr: str) -> List[AlphaFactorQueueModel]:
        """生成反转策略变体"""

        variants = []
        reversal_conditions = [
            f"abs({base_expr}) > ts_std({base_expr}, 20)",  # 极值反转
            f"{base_expr} < ts_rank({base_expr}, 20) * 0.2",  # 处于底部20%
            f"{base_expr} > ts_rank({base_expr}, 20) * 0.8",  # 处于顶部20%
            # Z-score > 2
            f"({base_expr} - ts_mean({base_expr}, 10)) / ts_std({base_expr}, 10) > 2",
            f"sign({base_expr}) != sign(delay({base_expr}, 1))"  # 符号变化
        ]

        for i, condition in enumerate(reversal_conditions):
            trade_when_expr = f"trade_when({base_expr}, {condition})"

            variant = AlphaFactorQueueModel(
                factor_expression=trade_when_expr,
                source_dataset=base_factor.source_dataset,
                stage='stage3',
                status='pending',
                tags=f"{base_factor.tags},trade_when,reversal_{i}",
                priority=base_factor.priority + 20,
                submitted_at=datetime.now()
            )
            variants.append(variant)

        return variants

    def _generate_volatility_variants(self, base_factor: AlphaFactorQueueModel, base_expr: str) -> List[AlphaFactorQueueModel]:
        """生成波动率策略变体"""

        variants = []
        volatility_conditions = [
            f"ts_std(close, 5) > ts_mean(ts_std(close, 5), 20)",  # 波动率突破
            f"(high - low) / close > ts_rank((high - low) / close, 20) * 0.8",  # 日内波动率高
            f"abs(returns) > ts_std(returns, 20) * 2",  # 收益率异常
            f"ts_std({base_expr}, 5) > ts_std({base_expr}, 20)"  # 因子自身波动增加
        ]

        for i, condition in enumerate(volatility_conditions):
            trade_when_expr = f"trade_when({base_expr}, {condition})"

            variant = AlphaFactorQueueModel(
                factor_expression=trade_when_expr,
                source_dataset=base_factor.source_dataset,
                stage='stage3',
                status='pending',
                tags=f"{base_factor.tags},trade_when,volatility_{i}",
                priority=base_factor.priority + 15,
                submitted_at=datetime.now()
            )
            variants.append(variant)

        return variants

    async def _timing_optimization(self, factors: List[AlphaFactorQueueModel],
                                   market_regime: Dict) -> List[AlphaFactorQueueModel]:
        """时机优化与条件细化"""

        self.logger.info(f"⏰ 时机优化 {len(factors)} 个trade_when因子...")

        optimized_factors = []

        for factor in factors:
            try:
                # ML驱动的时机优化
                optimized_condition = await self.timing_optimizer.optimize_timing_condition(
                    factor.factor_expression,
                    market_regime=market_regime,
                    optimization_horizon=30  # 30天优化窗口
                )

                if optimized_condition['improvement'] > self.min_improvement_threshold:
                    # 更新因子表达式
                    factor.factor_expression = optimized_condition['optimized_expression']
                    factor.timing_score = optimized_condition['timing_score']
                    factor.expected_improvement = optimized_condition['improvement']

                    optimized_factors.append(factor)
                    self.ml_stats['timing_optimizations'] += 1
                else:
                    # 改进不显著，保持原始表达式
                    optimized_factors.append(factor)

            except Exception as e:
                self.logger.error(f"时机优化失败: {e}")
                optimized_factors.append(factor)  # 保持原始因子

        self.logger.info(
            f"⚡ 时机优化完成：{self.ml_stats['timing_optimizations']} 个因子")
        return optimized_factors

    async def _multi_scenario_validation(self, factors: List[AlphaFactorQueueModel]) -> List[AlphaFactorQueueModel]:
        """多scenario测试与选择"""

        self.logger.info(f"🧪 多scenario验证 {len(factors)} 个因子...")

        # 定义测试scenario
        test_scenarios = [
            {'name': 'bull_market', 'conditions': {
                'trend': 'up', 'volatility': 'low'}},
            {'name': 'bear_market', 'conditions': {
                'trend': 'down', 'volatility': 'high'}},
            {'name': 'sideways_market', 'conditions': {
                'trend': 'sideways', 'volatility': 'medium'}},
            {'name': 'crisis_market', 'conditions': {
                'trend': 'down', 'volatility': 'high', 'correlation': 'high'}}
        ]

        validated_factors = []

        for factor in factors:
            try:
                # 在各scenario下评估因子性能
                scenario_scores = []
                for scenario in test_scenarios:
                    score = await self._evaluate_factor_in_scenario(factor, scenario)
                    scenario_scores.append(score)

                # 计算综合评分
                avg_score = np.mean(scenario_scores)
                min_score = np.min(scenario_scores)  # 最差scenario表现
                stability = 1 - np.std(scenario_scores)  # 稳定性

                # 综合评判标准
                composite_score = avg_score * 0.6 + min_score * 0.3 + stability * 0.1

                if composite_score >= 0.65:  # 高标准筛选
                    factor.scenario_score = composite_score
                    factor.scenario_stability = stability
                    validated_factors.append(factor)

            except Exception as e:
                self.logger.error(f"Scenario验证失败: {e}")
                continue

        validation_ratio = len(validated_factors) / \
            len(factors) if factors else 0
        self.logger.info(
            f"✅ Scenario验证：{len(validated_factors)}/{len(factors)} ({validation_ratio:.1%}) 通过")

        return validated_factors

    async def _execute_with_regime_adaptation(self, factors: List[AlphaFactorQueueModel],
                                              accounts: List[UserAccountModel]):
        """执行处理并进行regime适应性学习"""

        execution_results = []

        # 智能分配
        assignments = await self._regime_aware_assignment(factors, accounts)

        for account_id, assigned_factors in assignments.items():
            account = UserAccountModel.get_by_id(account_id)

            for factor in assigned_factors:
                # 执行处理
                result = await self.execute_factor_processing(factor, account)

                # 记录execution结果
                execution_results.append({
                    'factor': factor,
                    'result': result,
                    'account': account,
                    'scenario_score': getattr(factor, 'scenario_score', 0),
                    'timing_score': getattr(factor, 'timing_score', 0)
                })

        # 学习和适应
        await self._learn_regime_patterns(execution_results)

    # === 传统方法兼容 ===

    async def execute_factor_processing(self, factor: AlphaFactorQueueModel,
                                        account: UserAccountModel) -> Dict:
        """执行第三阶段因子处理"""

        try:
            request_data = {
                'expression': factor.factor_expression,
                'universe': factor.universe or 'TOP3000',
                'region': factor.region or 'USA',
                'delay': factor.delay or 1,
                'decay': factor.decay or 6,
                'neutralization': factor.neutralization or 'SUBINDUSTRY'
            }

            # 调用API
            response = await self.api_client.robust_request(
                '/alpha/simulation',
                request_data,
                account
            )

            # 解析结果
            result = self.parse_simulation_result(response)

            # 判断是否晋级到检查阶段
            if self.meets_check_criteria(result):
                await self.promote_to_check(factor, result)

            return result

        except Exception as e:
            self.logger.error(f"第三阶段因子处理失败: {e}")
            raise

    def meets_check_criteria(self, result: Dict) -> bool:
        """判断是否符合检查阶段条件"""
        return (result.get('sharpe', 0) > 2.0 and
                result.get('fitness', 0) > 0.05)

    def _log_ml_stage3_stats(self):
        """输出第三阶段ML统计"""

        stats = self.ml_stats

        if not self.quiet:
            self.logger.info("📊 第三阶段ML统计:")
            self.logger.info(
                f"  - trade_when生成: {stats['trade_when_generated']}")
            self.logger.info(f"  - regime适应: {stats['regime_adaptations']}")
            self.logger.info(f"  - 时机优化: {stats['timing_optimizations']}")
            self.logger.info(f"  - 平均改进: {stats['avg_improvement']:.1%}")
