#!/usr/bin/env python3
"""
WQ项目初始化设置脚本

负责系统初始化，包括基础配置和用户配置的设置
与app.py分离，app.py专注于程序运行，setup.py专注于初始化信息
"""

from process.config_setup import check_base_config_initialized, setup_configuration, show_current_base_config
from process.user_setup import check_user_configured, setup_user_configuration, show_current_user_info
from lib.logger import get_logger, get_ui
import sys
from pathlib import Path

# 添加src目录到Python路径
current_dir = Path(__file__).parent
if str(current_dir) not in sys.path:
    sys.path.insert(0, str(current_dir))

logger = get_logger(__name__)
ui = get_ui(__name__)


def quick_setup() -> bool:
    """
    快速设置模式 - 自动初始化所有配置

    Returns:
        bool: 是否设置成功
    """
    logger.info("=== WQ项目快速设置 ===")
    ui.section_header("🚀 WQ项目快速设置")
    ui.info("将自动配置所有必要的系统和用户设置")

    try:
        success_steps = 0
        total_steps = 2

        # 步骤1：基础配置设置
        ui.step(1, total_steps, "基础系统配置")
        if not check_base_config_initialized():
            ui.progress("正在初始化基础配置...")
            if not setup_configuration(interactive=False):
                ui.error("基础配置设置失败")
                return False
            else:
                ui.success("基础配置设置完成")
                success_steps += 1
        else:
            ui.success("基础配置已存在")
            success_steps += 1

        # 步骤2：用户配置设置
        ui.step(2, total_steps, "用户配置")
        if not check_user_configured():
            ui.progress("正在设置用户配置...")
            if not setup_user_configuration(interactive=False):
                ui.error("用户配置设置失败")
                return False
            else:
                ui.success("用户配置设置完成")
                success_steps += 1
        else:
            ui.success("用户配置已存在")
            success_steps += 1

        # 总结结果
        ui.section_subheader("📋 快速设置完成")
        if success_steps == total_steps:
            ui.success("所有配置设置成功！系统已就绪")
            logger.info("快速设置完成")
            return True
        else:
            ui.error("部分配置设置失败")
            logger.error(f"快速设置失败: {success_steps}/{total_steps}")
            return False

    except Exception as e:
        logger.error(f"快速设置过程中出错: {e}")
        ui.error(f"快速设置失败: {e}")
        return False


def interactive_setup() -> bool:
    """
    交互式设置模式 - 逐步引导用户配置

    Returns:
        bool: 是否设置成功
    """
    logger.info("=== WQ项目交互式设置 ===")
    ui.section_header("🛠️  WQ项目交互式设置")
    ui.info("将逐步引导您完成配置设置")

    try:
        success_steps = 0
        total_steps = 2

        # 步骤1：基础配置设置
        ui.step(1, total_steps, "基础系统配置")
        ui.info("配置地区市场、数据集和API端点信息")

        if not check_base_config_initialized():
            ui.warning("检测到基础配置未初始化")
            if input("\n开始基础配置设置？(y/n): ").strip().lower() in ['y', 'yes', '是']:
                if not setup_configuration(interactive=True):
                    ui.error("基础配置设置失败")
                    return False
                else:
                    success_steps += 1
            else:
                ui.error("用户取消基础配置设置")
                return False
        else:
            ui.success("基础配置已存在")
            success_steps += 1

        # 步骤2：用户配置设置
        ui.step(2, total_steps, "用户配置")
        ui.info("配置用户认证和个人偏好信息")

        if not check_user_configured():
            ui.warning("检测到用户配置未初始化")
            if input("\n开始用户配置设置？(y/n): ").strip().lower() in ['y', 'yes', '是']:
                if not setup_user_configuration(interactive=True):
                    ui.error("用户配置设置失败")
                    return False
                else:
                    success_steps += 1
            else:
                ui.error("用户取消用户配置设置")
                return False
        else:
            ui.success("用户配置已存在")
            success_steps += 1

        # 总结结果
        ui.section_subheader("📋 交互式设置完成")
        if success_steps == total_steps:
            ui.success("所有配置设置成功！系统已就绪")
            logger.info("交互式设置完成")
            return True
        else:
            ui.error("部分配置设置失败")
            logger.error(f"交互式设置失败: {success_steps}/{total_steps}")
            return False

    except Exception as e:
        logger.error(f"交互式设置过程中出错: {e}")
        ui.error(f"交互式设置失败: {e}")
        return False


def show_current_status():
    """显示当前配置状态"""
    try:
        ui.section_header("📊 当前配置状态")

        # 检查基础配置状态
        ui.info("🔧 基础配置状态:")
        if check_base_config_initialized():
            ui.checklist_item("基础配置已初始化")
            show_current_base_config()
        else:
            ui.error("基础配置未初始化")

        ui.info("")  # 空行分隔

        # 检查用户配置状态
        ui.info("👤 用户配置状态:")
        if check_user_configured():
            ui.checklist_item("用户配置已初始化")
            show_current_user_info()
        else:
            ui.error("用户配置未初始化")

    except Exception as e:
        logger.error(f"显示配置状态时出错: {e}")
        ui.error(f"无法显示配置状态: {e}")


def main():
    """主函数 - 根据命令行参数选择设置模式"""
    if len(sys.argv) > 1:
        mode = sys.argv[1]
        if mode == "--quick":
            quick_setup()
        elif mode == "--interactive":
            interactive_setup()
        elif mode == "--status":
            show_current_status()
        else:
            ui.error(f"未知参数: {mode}")
            ui.info("用法: python3 setup.py [选项]")
            ui.info("选项:")
            ui.info("  --quick         快速自动配置")
            ui.info("  --interactive   交互式配置")
            ui.info("  --status        查看配置状态")
    else:
        # 默认交互式模式
        ui.info("启动交互式设置向导...")
        ui.info("如需快速设置，请使用: python3 setup.py --quick")
        ui.info("如需查看状态，请使用: python3 setup.py --status")
        ui.info("")
        interactive_setup()


if __name__ == "__main__":
    main()
