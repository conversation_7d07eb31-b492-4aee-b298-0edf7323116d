# API 参考文档

## 目录

- [machine_lib.py](#machine_libpy---核心功能库)
- [config.py](#configpy---配置管理)
- [fields.py](#fieldspy---数据字段定义)
- [check.py](#checkpy---因子检测)
- [提交模块](#提交模块)

---

## machine_lib.py - 核心功能库

### 认证与会话管理

#### `login() -> requests.Session`

**功能**：同步登录到 WorldQuant Brain 平台

**返回值**：

- `requests.Session`: 已认证的会话对象

**异常**：

- `Exception`: 当用户名密码错误时抛出

**示例**：

```python
s = login()
```

#### `async_login() -> aiohttp.ClientSession`

**功能**：异步登录到 WorldQuant Brain 平台

**返回值**：

- `aiohttp.ClientSession`: 已认证的异步会话对象

**示例**：

```python
session = await async_login()
```

#### `class SessionManager`

**功能**：管理异步会话的生命周期

**构造函数**：

```python
SessionManager(session, start_time, expiry_time)
```

**参数**：

- `session` (aiohttp.ClientSession): 异步会话对象
- `start_time` (float): 会话开始时间
- `expiry_time` (float): 会话过期时间

**方法**：

##### `async refresh_session()`

刷新过期的会话

---

### 数据获取

#### `get_datasets(s, instrument_type='EQUITY', region='USA', delay=1, universe='TOP3000') -> pd.DataFrame`

**功能**：获取可用的数据集列表

**参数**：

- `s` (requests.Session): 会话对象
- `instrument_type` (str): 工具类型，默认 'EQUITY'
- `region` (str): 地区，默认 'USA'
- `delay` (int): 延迟天数，默认 1
- `universe` (str): 股票池，默认 'TOP3000'

**返回值**：

- `pd.DataFrame`: 数据集信息

#### `get_datafields(s, instrument_type='EQUITY', region='USA', delay=1, universe='TOP3000', dataset_id='', search='') -> pd.DataFrame`

**功能**：获取数据字段列表

**参数**：

- `s` (requests.Session): 会话对象
- `instrument_type` (str): 工具类型
- `region` (str): 地区
- `delay` (int): 延迟天数
- `universe` (str): 股票池
- `dataset_id` (str): 数据集 ID
- `search` (str): 搜索关键词

**返回值**：

- `pd.DataFrame`: 数据字段信息

#### `process_datafields(df, data_type) -> List[str]`

**功能**：处理数据字段，应用 winsorize 和 ts_backfill

**参数**：

- `df` (pd.DataFrame): 数据字段 DataFrame
- `data_type` (str): 数据类型，'matrix' 或 'vector'

**返回值**：

- `List[str]`: 处理后的字段列表

---

### 因子生成

#### `first_order_factory(fields, ops_set) -> List[str]`

**功能**：生成一级因子表达式

**参数**：

- `fields` (List[str]): 基础字段列表
- `ops_set` (List[str]): 算子集合

**返回值**：

- `List[str]`: 一级因子表达式列表

#### `get_group_second_order_factory(first_order, group_ops, region) -> List[str]`

**功能**：生成二级分组因子

**参数**：

- `first_order` (List[str]): 一级因子列表
- `group_ops` (List[str]): 分组算子列表
- `region` (str): 地区

**返回值**：

- `List[str]`: 二级分组因子列表

#### `ts_factory(op, field) -> List[str]`

**功能**：生成时间序列因子

**参数**：

- `op` (str): 时间序列算子
- `field` (str): 基础字段

**返回值**：

- `List[str]`: 时间序列因子列表

**时间窗口**：[5, 22, 66, 120, 240]

#### `group_factory(op, field, region) -> List[str]`

**功能**：生成分组因子

**参数**：

- `op` (str): 分组算子
- `field` (str): 基础字段
- `region` (str): 地区

**返回值**：

- `List[str]`: 分组因子列表

#### `trade_when_factory(op, field, region, delay=1) -> List[str]`

**功能**：生成交易条件因子

**参数**：

- `op` (str): 算子（通常为 'trade_when'）
- `field` (str): 基础字段
- `region` (str): 地区
- `delay` (int): 延迟天数

**返回值**：

- `List[str]`: 交易条件因子列表

---

### 模拟与执行

#### `async simulate_single(session_manager, alpha_expression, region_info, name, neut, decay, delay, stone_bag, tags, semaphore)`

**功能**：异步模拟单个 Alpha 因子

**参数**：

- `session_manager` (SessionManager): 会话管理器
- `alpha_expression` (str): Alpha 表达式
- `region_info` (Tuple): 地区和股票池信息
- `name` (str): 因子名称
- `neut` (str): 中性化方式
- `decay` (int): 衰减系数
- `delay` (int): 延迟天数
- `stone_bag` (List): 结果存储列表
- `tags` (List[str]): 标签列表
- `semaphore` (asyncio.Semaphore): 并发控制信号量

#### `async simulate_multiple_alphas(alpha_list, region_list, decay_list, delay_list, name, neut, stone_bag=[], n_jobs=5)`

**功能**：异步批量模拟 Alpha 因子

**参数**：

- `alpha_list` (List[str]): Alpha 表达式列表
- `region_list` (List[Tuple]): 地区信息列表
- `decay_list` (List[int]): 衰减系数列表
- `delay_list` (List[int]): 延迟天数列表
- `name` (str): 批次名称
- `neut` (str): 中性化方式
- `stone_bag` (List): 结果存储列表
- `n_jobs` (int): 并发任务数

---

### Alpha 管理

#### `get_alphas(start_date, end_date, sharpe_th, fitness_th, longCount_th, shortCount_th, region, universe, delay, instrumentType, alpha_num, usage, tag='', color_exclude='', s=None) -> Dict`

**功能**：获取 Alpha 列表

**参数**：

- `start_date` (str): 开始日期 'YYYY-MM-DD'
- `end_date` (str): 结束日期 'YYYY-MM-DD'
- `sharpe_th` (float): Sharpe 比率阈值
- `fitness_th` (float): Fitness 阈值
- `longCount_th` (int): 长仓数量阈值
- `shortCount_th` (int): 短仓数量阈值
- `region` (str): 地区
- `universe` (str): 股票池
- `delay` (int): 延迟天数
- `instrumentType` (str): 工具类型
- `alpha_num` (int): 最大返回数量
- `usage` (str): 用途，'submit' 或 'track'
- `tag` (str): 标签过滤
- `color_exclude` (str): 排除的颜色
- `s` (requests.Session): 会话对象

**返回值**：

- `Dict`: 包含 'next', 'decay', 'check' 键的字典

#### `set_alpha_properties(s, alpha_id, name=None, color=None, selection_desc=None, combo_desc=None, tags=None)`

**功能**：设置 Alpha 属性

**参数**：

- `s` (requests.Session): 会话对象
- `alpha_id` (str): Alpha ID
- `name` (str): 名称
- `color` (str): 颜色 ('RED', 'YELLOW', 'GREEN', 'BLUE', 'PURPLE')
- `selection_desc` (str): 选择描述
- `combo_desc` (str): 组合描述
- `tags` (List[str]): 标签列表

#### `locate_alpha(s, alpha_id) -> List`

**功能**：获取 Alpha 详细信息

**参数**：

- `s` (requests.Session): 会话对象
- `alpha_id` (str): Alpha ID

**返回值**：

- `List`: [alpha_id, expression, sharpe, turnover, fitness, margin, dateCreated, decay]

---

### 工具函数

#### `transform(next_alpha_recs) -> List[Tuple]`

**功能**：转换 Alpha 记录格式

**参数**：

- `next_alpha_recs` (List): Alpha 记录列表

**返回值**：

- `List[Tuple]`: [(expression, decay), ...] 格式的列表

#### `prune(next_alpha_recs, prefix, keep_num) -> List[Tuple]`

**功能**：修剪 Alpha 列表，保留每个字段的最佳因子

**参数**：

- `next_alpha_recs` (List): Alpha 记录列表
- `prefix` (str): 字段前缀
- `keep_num` (int): 每个字段保留的数量

**返回值**：

- `List[Tuple]`: 修剪后的列表

---

## config.py - 配置管理

### 路径配置

```python
ROOT_PATH = os.path.dirname(__file__)
DATA_PATH = os.path.join(ROOT_PATH, 'data')
RECORDS_PATH = os.path.join(ROOT_PATH, 'records')
DATASETS_PATH = os.path.join(DATA_PATH, 'datasets')
FIELDS_PATH = os.path.join(DATA_PATH, 'fields')
```

### 市场配置

#### `REGION_LIST`

支持的地区列表：

```python
['USA', 'GLB', 'EUR', 'ASI', 'CHN', 'KOR', 'TWN', 'JPN', 'HKG', 'AMR']
```

#### `DELAY_LIST`

延迟天数选项：

```python
[1, 0]
```

#### `INSTRUMENT_TYPE_LIST`

工具类型：

```python
['EQUITY', 'CRYPTO']
```

#### `UNIVERSE_DICT`

股票池配置字典，按工具类型和地区组织。

---

## fields.py - 数据字段定义

### 字段集合

#### `recommended_fields_1`

精选推荐字段列表，包含财务比率组合。

#### `doubao_fields_1`

豆包推荐的基础财务比率字段：

- 资产息税前利润率
- 净资产收益率
- 经营活动现金流量对负债保障程度
- 等 100+个财务指标

#### `doubao_fields_2`

豆包推荐的分析师数据组合字段。

#### `doubao_fields_3`

豆包推荐的高级字段组合，包含复杂计算。

---

## check.py - 因子检测

### 主要函数

#### `generate_date_periods(start_date_file='start_date.txt', default_start_date='2024-10-07') -> List[List[str]]`

**功能**：生成日期区间列表

**参数**：

- `start_date_file` (str): 起始日期文件路径
- `default_start_date` (str): 默认起始日期

**返回值**：

- `List[List[str]]`: [[start_date, end_date], ...] 格式的日期列表

#### `get_self_corr(s, alpha_id) -> pd.DataFrame`

**功能**：获取 Alpha 自相关性数据

**参数**：

- `s` (requests.Session): 会话对象
- `alpha_id` (str): Alpha ID

**返回值**：

- `pd.DataFrame`: 自相关性数据

#### `get_prod_corr(s, alpha_id) -> pd.DataFrame`

**功能**：获取 Alpha 生产相关性数据

**参数**：

- `s` (requests.Session): 会话对象
- `alpha_id` (str): Alpha ID

**返回值**：

- `pd.DataFrame`: 生产相关性数据

#### `check_self_corr_test(s, alpha_id, threshold=0.7) -> pd.DataFrame`

**功能**：检查自相关性测试

**参数**：

- `s` (requests.Session): 会话对象
- `alpha_id` (str): Alpha ID
- `threshold` (float): 阈值，默认 0.7

**返回值**：

- `pd.DataFrame`: 测试结果

#### `check_prod_corr_test(s, alpha_id, threshold=0.7) -> pd.DataFrame`

**功能**：检查生产相关性测试

**参数**：

- `s` (requests.Session): 会话对象
- `alpha_id` (str): Alpha ID
- `threshold` (float): 阈值，默认 0.7

**返回值**：

- `pd.DataFrame`: 测试结果

#### `check_alpha_by_self_prod(s, alpha, submitable_alpha_file, mode)`

**功能**：综合检查 Alpha 因子

**参数**：

- `s` (requests.Session): 会话对象
- `alpha` (Dict): Alpha 信息字典
- `submitable_alpha_file` (str): 可提交 Alpha 文件路径
- `mode` (str): 模式，'USER' 或 'CONSULTANT'

---

## 提交模块

### submit_alpha.py

#### `submit_alpha(s, alpha_id) -> int`

**功能**：提交单个 Alpha 因子

**参数**：

- `s` (requests.Session): 会话对象
- `alpha_id` (str): Alpha ID

**返回值**：

- `int`: HTTP 状态码

### s7.py (增强版)

#### `submit_alpha(session, alpha_id) -> Tuple[int, str]`

**功能**：增强版 Alpha 提交（30 分钟超时）

**参数**：

- `session` (requests.Session): 会话对象
- `alpha_id` (str): Alpha ID

**返回值**：

- `Tuple[int, str]`: (状态码, 状态描述)

#### `submit_batch(session, csv_path) -> Dict`

**功能**：批量提交 Alpha 因子

**参数**：

- `session` (requests.Session): 会话对象
- `csv_path` (str): CSV 文件路径

**返回值**：

- `Dict`: 提交结果统计

---

## 算子参考

### 基础算子 (basic_ops)

- `log`: 对数变换
- `sqrt`: 平方根变换
- `reverse`: 反向排序
- `inverse`: 倒数变换
- `rank`: 排名
- `zscore`: Z-score 标准化
- `log_diff`: 对数差分
- `s_log_1p`: log(1+x)变换
- `fraction`: 分数化
- `quantile`: 分位数
- `normalize`: 归一化
- `scale_down`: 缩放

### 时间序列算子 (ts_ops)

- `ts_rank`: 时序排名
- `ts_zscore`: 时序 Z-score
- `ts_delta`: 时序差分
- `ts_sum`: 时序求和
- `ts_product`: 时序连乘
- `ts_ir`: 时序信息比率
- `ts_std_dev`: 时序标准差
- `ts_mean`: 时序均值
- `ts_arg_min/max`: 时序极值位置
- `ts_min/max_diff`: 时序极值差
- `ts_returns`: 时序收益率
- `ts_scale`: 时序缩放
- `ts_skewness`: 时序偏度
- `ts_kurtosis`: 时序峰度
- `ts_quantile`: 时序分位数

### 分组算子 (group_ops)

- `group_neutralize`: 分组中性化
- `group_rank`: 分组排名
- `group_normalize`: 分组归一化
- `group_scale`: 分组缩放
- `group_zscore`: 分组 Z-score

### 向量算子 (vec_ops)

- `vec_avg`: 向量平均
- `vec_sum`: 向量求和
- `vec_ir`: 向量信息比率
- `vec_max`: 向量最大值
- `vec_count`: 向量计数
- `vec_skewness`: 向量偏度
- `vec_stddev`: 向量标准差
- `vec_choose`: 向量选择

---

## 错误处理

### 常见异常

1. **认证失败**

   ```python
   Exception("INVALID_CREDENTIALS")
   ```

2. **网络超时**

   ```python
   # 自动重试机制
   while True:
       try:
           response = session.get(url)
           if "retry-after" in response.headers:
               time.sleep(float(response.headers["Retry-After"]))
           else:
               break
       except Exception:
           time.sleep(60)
   ```

3. **API 限制**
   ```python
   if detail == 'SIMULATION_LIMIT_EXCEEDED':
       await asyncio.sleep(5)
   ```

---

## 性能建议

1. **并发控制**：使用适当的 `n_jobs` 参数
2. **会话重用**：避免频繁创建新会话
3. **内存管理**：及时清理大型数据结构
4. **错误重试**：实现指数退避策略

---

_更多详细信息请参考源代码注释和项目文档。_
