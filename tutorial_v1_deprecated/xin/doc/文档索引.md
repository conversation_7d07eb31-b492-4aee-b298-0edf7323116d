# 📚 WorldQuant Brain Alpha 因子挖掘系统 - 文档索引

## 🎯 快速导航

### 🚀 新手指南

- **[快速入门指南.md](快速入门指南.md)** - 5 分钟快速上手系统
  - 环境配置
  - 基础操作
  - 常见问题

### 📖 详细文档

- **[项目文档.md](项目文档.md)** - 完整系统说明
  - 项目概述
  - 系统架构
  - 功能特性
  - 配置说明
  - 版本历史

### 🔧 技术参考

- **[API 参考文档.md](API参考文档.md)** - 函数和接口详解
  - 核心函数库
  - 配置管理
  - 数据字段
  - 因子检测
  - 提交模块

### 📋 原始文档

- **[README.md](README.md)** - 项目基础信息和更新日志
- **[LICENSE](LICENSE)** - Apache 2.0 许可证

## 📁 按功能分类

### 🔍 因子挖掘

| 文件               | 功能         | 文档位置                            |
| ------------------ | ------------ | ----------------------------------- |
| `digging_1step.py` | 一级因子挖掘 | [项目文档.md](项目文档.md#快速开始) |
| `digging_2step.py` | 二级因子挖掘 | [项目文档.md](项目文档.md#快速开始) |
| `digging_3step.py` | 三级因子挖掘 | [项目文档.md](项目文档.md#快速开始) |

### 📊 因子检测

| 文件       | 功能           | 文档位置                                             |
| ---------- | -------------- | ---------------------------------------------------- |
| `check.py` | 因子合规性检测 | [API 参考文档.md](API参考文档.md#checkpy---因子检测) |

### 📤 因子提交

| 文件              | 功能                      | 文档位置                                   |
| ----------------- | ------------------------- | ------------------------------------------ |
| `submit_alpha.py` | 基础提交功能              | [API 参考文档.md](API参考文档.md#提交模块) |
| `submit4.py`      | 提交变体版本              | [API 参考文档.md](API参考文档.md#提交模块) |
| `s7.py`           | 增强提交版本(30 分钟超时) | [API 参考文档.md](API参考文档.md#提交模块) |

### ⚙️ 系统配置

| 文件             | 功能         | 文档位置                                                     |
| ---------------- | ------------ | ------------------------------------------------------------ |
| `config.py`      | 系统配置管理 | [API 参考文档.md](API参考文档.md#configpy---配置管理)        |
| `fields.py`      | 数据字段定义 | [API 参考文档.md](API参考文档.md#fieldspy---数据字段定义)    |
| `machine_lib.py` | 核心功能库   | [API 参考文档.md](API参考文档.md#machine_libpy---核心功能库) |

### 🔧 用户配置

| 文件               | 功能          | 文档位置                                    |
| ------------------ | ------------- | ------------------------------------------- |
| `user_info.txt`    | 用户账号配置  | [快速入门指南.md](快速入门指南.md#准备工作) |
| `requirements.txt` | Python 依赖包 | [项目文档.md](项目文档.md#安装与配置)       |

## 🎓 学习路径

### 初学者路径

1. **环境搭建** → [快速入门指南.md](快速入门指南.md#准备工作)
2. **基础概念** → [项目文档.md](项目文档.md#项目概述)
3. **第一次运行** → [快速入门指南.md](快速入门指南.md#5分钟快速体验)
4. **常见问题** → [快速入门指南.md](快速入门指南.md#故障排查)

### 进阶用户路径

1. **系统架构** → [项目文档.md](项目文档.md#系统架构)
2. **参数调优** → [项目文档.md](项目文档.md#详细配置说明)
3. **API 使用** → [API 参考文档.md](API参考文档.md)
4. **性能优化** → [项目文档.md](项目文档.md#性能优化)

### 开发者路径

1. **代码结构** → [项目文档.md](项目文档.md#代码结构详解)
2. **函数接口** → [API 参考文档.md](API参考文档.md)
3. **扩展开发** → [快速入门指南.md](快速入门指南.md#进阶技巧)
4. **错误处理** → [API 参考文档.md](API参考文档.md#错误处理)

## 🔧 使用场景

### 日常操作

- **启动挖掘** → [快速入门指南.md](快速入门指南.md#完整工作流程)
- **监控进度** → [快速入门指南.md](快速入门指南.md#常见操作)
- **检查结果** → [快速入门指南.md](快速入门指南.md#结果文件说明)

### 问题解决

- **登录失败** → [快速入门指南.md](快速入门指南.md#故障排查)
- **性能问题** → [快速入门指南.md](快速入门指南.md#性能优化建议)
- **配置错误** → [项目文档.md](项目文档.md#常见问题)

### 高级配置

- **自定义字段** → [项目文档.md](项目文档.md#高级功能)
- **并发控制** → [API 参考文档.md](API参考文档.md#性能建议)
- **批量提交** → [快速入门指南.md](快速入门指南.md#进阶技巧)

## 📚 外部资源

### 官方文档

- [WorldQuant Brain 官方文档](https://platform.worldquantbrain.com/learn)
- [Python 异步编程指南](https://docs.python.org/3/library/asyncio.html)
- [Pandas 数据处理文档](https://pandas.pydata.org/docs/)

### 社区资源

- WorldQuant Brain 用户论坛
- 量化投资交流社区
- Python 量化交易群组

## 🔄 文档更新

### 最新更新

- **2025.05.09**: v1.1.5 版本文档
- **2025.04.08**: v1.1.4 功能更新
- **2025.03.19**: v1.1.3 微信通知功能

### 贡献指南

欢迎提供文档改进建议：

1. 发现错误或不清楚的地方
2. 建议增加新的示例
3. 分享使用经验和技巧

---

## 📌 书签收藏

**常用文档快速链接**：

- 🚀 [快速开始](快速入门指南.md#5分钟快速体验)
- ⚙️ [配置参数](项目文档.md#详细配置说明)
- 🔍 [API 查询](API参考文档.md#machine_libpy---核心功能库)
- 🚨 [故障排查](快速入门指南.md#故障排查)
- 📊 [结果说明](快速入门指南.md#结果文件说明)

---

_本文档索引会随着项目更新而持续维护，建议收藏备用。_
