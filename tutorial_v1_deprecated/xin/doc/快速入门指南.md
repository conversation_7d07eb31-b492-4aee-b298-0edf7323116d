# 快速入门指南

## 📋 准备工作

### 1. 系统要求

- Python 3.7+
- 稳定的网络连接
- WorldQuant Brain 账号

### 2. 安装依赖

```bash
cd xin
pip install -r requirements.txt
```

### 3. 配置账号信息

编辑 `user_info.txt` 文件：

```text
username: '<EMAIL>'
password: 'your_password'
dataset_id: 'analyst4'
```

⚠️ **注意**：请确保账号信息正确，否则无法正常运行。

## 🚀 5 分钟快速体验

### 第一步：测试登录

```bash
python -c "from machine_lib import login; print('登录成功!' if login() else '登录失败')"
```

### 第二步：启动第一轮挖掘

```bash
python digging_1step.py
```

**预期输出**：

```
dataset_id: analyst4
获取数据字段...
生成一级因子表达式...
开始异步模拟...
```

### 第三步：监控进度

程序会自动显示进度信息：

- 因子生成数量
- 模拟完成情况
- 错误和重试信息

### 第四步：检查结果

查看 `records/` 目录下的文件：

- `analyst4_usa_1step_simulated_alpha_expression.txt` - 已完成的因子列表

## 🔄 完整工作流程

### 1. 一级因子挖掘 (3-6 小时)

```bash
python digging_1step.py
```

**目标**：生成基础因子表达式

### 2. 二级因子挖掘 (2-4 小时)

```bash
python digging_2step.py
```

**前提**：第一轮挖掘完成并有足够候选因子

### 3. 因子检测 (1-2 小时)

```bash
python check.py
```

**功能**：

- 自动筛选高质量因子
- 生成 `submitable_alpha.csv`
- 设置因子颜色标记

### 4. 因子提交 (按需)

```bash
python s7.py
```

**用途**：将通过检测的因子提交到平台

## ⚙️ 核心参数配置

### digging_1step.py 关键参数

```python
# 数据集配置
dataset_id = 'analyst4'  # 数据集ID
step1_tag = "analyst4_usa_1step"  # 标签名称

# 市场配置
region = "USA"
universe = "TOP3000"
delay = 1

# 并发配置
n_jobs = 3  # 建议值：1-5
```

### check.py 检测标准

```python
mode = "USER"  # 用户模式
# USER: sharpe_th = 1.25
# CONSULTANT: sharpe_th = 1.58

# 相关性阈值
self_corr_threshold = 0.7
prod_corr_threshold = 0.7
```

## 📊 结果文件说明

### records/ 目录结构

```
records/
├── start_date.txt                    # 检测起始日期
├── submitable_alpha.csv             # 可提交因子列表
├── analyst4_usa_1step_checked_alpha_id.txt      # 已检测因子ID
├── analyst4_usa_1step_simulated_alpha_expression.txt  # 已模拟表达式
└── analyst4_usa_2step_simulated_alpha_expression.txt  # 二级表达式
```

### submitable_alpha.csv 字段说明

- `id`: Alpha 因子 ID
- `code`: 因子表达式
- `sharpe`: 夏普比率
- `fitness`: 适应度
- `turnover`: 换手率
- `self_corr`: 自相关性
- `prod_corr`: 生产相关性

## 🔍 常见操作

### 检查当前进度

```bash
# 查看已完成的一级因子数量
wc -l records/analyst4_usa_1step_simulated_alpha_expression.txt

# 查看可提交因子数量
wc -l records/submitable_alpha.csv
```

### 自定义字段集合

编辑 `digging_1step.py`：

```python
# 使用推荐字段
pc_fields = recommended_fields_1

# 或使用自定义字段
pc_fields = ["your_field_1", "your_field_2"]
```

### 修改挖掘参数

```python
# 调整并发数
n_jobs = 5  # 提高并发

# 修改地区
region_list = [('EUR', 'TOP1200')] * len(alpha_list)

# 调整衰减参数
decay_list = [8] * len(alpha_list)
```

## 🚨 故障排查

### 登录失败

**症状**：`INVALID_CREDENTIALS` 错误
**解决**：

1. 检查 `user_info.txt` 格式
2. 确认账号密码正确
3. 验证网络连接

### 模拟失败

**症状**：频繁超时或错误
**解决**：

1. 降低并发数 `n_jobs = 1`
2. 检查网络稳定性
3. 等待 API 限制解除

### 内存不足

**症状**：程序崩溃或响应缓慢
**解决**：

1. 减少并发任务数
2. 重启 Python 进程
3. 清理临时文件

### 检测无结果

**症状**：`submitable_alpha.csv` 为空
**解决**：

1. 检查因子质量阈值
2. 延长挖掘时间
3. 尝试不同数据集

## 📈 性能优化建议

### 1. 硬件配置

- **CPU**: 4 核以上推荐
- **内存**: 8GB 以上
- **网络**: 稳定宽带连接

### 2. 软件设置

```python
# 合理设置并发数
n_jobs = min(4, cpu_count())

# 启用断点续传
# 程序会自动跳过已完成的因子

# 定期清理日志
# 避免日志文件过大
```

### 3. 运行策略

- **分时段运行**：避开平台繁忙时段
- **监控资源**：观察 CPU 和内存使用
- **备份结果**：定期备份 `records/` 目录

## 🎯 进阶技巧

### 1. 批量提交

使用 `s7.py` 的批量功能：

```python
# 创建提交列表CSV
df = pd.read_csv('records/submitable_alpha.csv')
submit_list = df['id'].head(10).tolist()  # 提交前10个

# 批量提交
python s7.py
```

### 2. 自定义算子组合

在 `machine_lib.py` 中添加新的因子生成逻辑：

```python
def custom_factory(fields, custom_ops):
    # 实现自定义因子生成逻辑
    pass
```

### 3. 多数据集并行

同时运行多个数据集：

```bash
# 终端1
dataset_id='analyst4' python digging_1step.py

# 终端2
dataset_id='fundamental1' python digging_1step.py
```

## 🆘 获取帮助

### 文档资源

- [项目文档.md](项目文档.md) - 完整功能说明
- [API 参考文档.md](API参考文档.md) - 函数接口说明
- [README.md](README.md) - 版本更新日志

### 社区资源

- WorldQuant Brain 官方文档
- 量化交易相关论坛
- Python 异步编程教程

---

## 🏁 下一步

完成快速入门后，建议：

1. **深入学习**：阅读完整项目文档
2. **参数调优**：根据实际情况优化配置
3. **扩展功能**：基于现有代码开发新功能
4. **分享经验**：与其他用户交流使用心得

---
