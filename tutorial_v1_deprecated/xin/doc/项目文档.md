# WorldQuant Brain Alpha 因子挖掘系统

## 项目概述

这是一个基于 WorldQuant Brain 平台的 Alpha 因子挖掘和提交系统，用于自动化地进行量化策略开发。系统通过多步骤挖掘、检测和提交流程，帮助用户高效地发现和部署有效的 Alpha 因子。

## 系统架构

### 核心模块

1. **机器学习库** (`machine_lib.py`) - 核心功能模块
2. **配置管理** (`config.py`) - 系统配置和常量定义
3. **数据字段** (`fields.py`) - 推荐字段和因子定义
4. **多级挖掘** (`digging_1step.py`, `digging_2step.py`, `digging_3step.py`) - 因子挖掘流程
5. **检测模块** (`check.py`) - 因子合规性检测
6. **提交模块** (`submit_alpha.py`, `submit4.py`, `s7.py`) - 因子提交功能

## 功能特性

### 🔍 智能挖掘系统

- **三级挖掘流程**：从基础一级因子到复杂多级组合因子
- **异步并发**：支持多任务并发模拟，提高挖掘效率
- **断点续传**：支持从中断处继续挖掘

### 📊 全面检测机制

- **自相关性检测**：防止过拟合
- **生产相关性检测**：确保因子有效性
- **微信通知**：可配置微信提醒功能

### 🚀 自动提交系统

- **批量提交**：支持 CSV 文件批量提交
- **超时重试**：30 分钟超时机制和自动重试
- **状态监控**：实时监控提交状态

## 安装与配置

### 环境要求

```bash
python >= 3.7
```

### 依赖安装

```bash
pip install -r requirements.txt
```

### 用户配置

1. 编辑 `user_info.txt` 文件：

```text
username: '<EMAIL>'
password: 'your_password'
dataset_id: 'analyst4'
```

2. 创建必要目录：
   - `records/` - 存储运行记录
   - `data/` - 存储数据文件

## 快速开始

### 1. 第一轮因子挖掘

```bash
python digging_1step.py
```

**主要参数配置：**

- `dataset_id`: 数据集 ID（如：'analyst4'）
- `step1_tag`: 第一轮标签（如：'analyst4_usa_1step'）
- `region`: 地区设置（默认：'USA'）
- `universe`: 股票池（默认：'TOP3000'）

### 2. 第二轮因子挖掘

```bash
python digging_2step.py
```

**依赖条件：**

- 需要第一轮挖掘产生足够的候选因子
- 根据第一轮结果进行组合优化

### 3. 因子检测

```bash
python check.py
```

**检测标准：**

- 自相关性 < 0.7
- 生产相关性 < 0.7
- 通过基础合规检查

### 4. 因子提交

```bash
python submit_alpha.py
```

或使用增强版：

```bash
python s7.py
```

## 详细配置说明

### 数据源配置

**支持的地区 (REGION_LIST):**

- `USA` - 美国市场
- `GLB` - 全球市场
- `EUR` - 欧洲市场
- `ASI` - 亚洲市场
- `CHN` - 中国市场
- `KOR` - 韩国市场
- `TWN` - 台湾市场
- `JPN` - 日本市场
- `HKG` - 香港市场
- `AMR` - 美洲市场

**支持的股票池 (UNIVERSE_DICT):**

- `TOP3000`, `TOP1000`, `TOP500`, `TOP200`
- `ILLIQUID_MINVOL1M`, `TOPSP500`
- `MINVOL1M` 等

### 算子配置

**基础算子 (basic_ops):**

```python
["log", "sqrt", "reverse", "inverse", "rank", "zscore",
 "log_diff", "s_log_1p", "fraction", "quantile",
 "normalize", "scale_down"]
```

**时间序列算子 (ts_ops):**

```python
["ts_rank", "ts_zscore", "ts_delta", "ts_sum",
 "ts_product", "ts_ir", "ts_std_dev", "ts_mean",
 "ts_arg_min", "ts_arg_max", "ts_min_diff",
 "ts_max_diff", "ts_returns", "ts_scale",
 "ts_skewness", "ts_kurtosis", "ts_quantile"]
```

**分组算子 (group_ops):**

```python
["group_neutralize", "group_rank", "group_normalize",
 "group_scale", "group_zscore"]
```

## 代码结构详解

### 核心类和函数

#### SessionManager 类

```python
class SessionManager:
    def __init__(self, session, start_time, expiry_time):
        self.session = session
        self.start_time = start_time
        self.expiry_time = expiry_time

    async def refresh_session(self):
        # 会话过期自动刷新
```

#### 核心函数

1. **登录认证**

   ```python
   def login() -> requests.Session
   async def async_login() -> aiohttp.ClientSession
   ```

2. **因子生成**

   ```python
   def first_order_factory(fields, ops_set) -> List[str]
   def get_group_second_order_factory(first_order, group_ops, region) -> List[str]
   ```

3. **模拟执行**

   ```python
   async def simulate_single(session_manager, alpha_expression, region_info, name, neut, decay, delay, stone_bag, tags, semaphore)
   ```

4. **检测功能**
   ```python
   def check_self_corr_test(s, alpha_id, threshold: float = 0.7) -> pd.DataFrame
   def check_prod_corr_test(s, alpha_id, threshold: float = 0.7) -> pd.DataFrame
   ```

### 数据流程

```mermaid
graph TD
    A[用户配置] --> B[登录认证]
    B --> C[获取数据字段]
    C --> D[一级因子生成]
    D --> E[因子模拟]
    E --> F[结果筛选]
    F --> G[二级因子生成]
    G --> H[因子检测]
    H --> I[提交审核]
    I --> J[部署上线]
```

## 高级功能

### 1. 自定义字段生成

系统提供多种预定义字段集合：

- **`recommended_fields_1`**: 精选推荐字段
- **`doubao_fields_1`**: 豆包推荐的基础财务比率
- **`doubao_fields_2`**: 豆包推荐的分析师数据组合
- **`doubao_fields_3`**: 豆包推荐的高级字段组合

### 2. 并发控制

```python
# 控制并发数量
n_jobs = 3  # 同时运行的任务数

# 使用异步信号量
semaphore = asyncio.Semaphore(n_jobs)
```

### 3. 会话管理

```python
# 自动会话刷新
session_expiry_time = 3 * 60 * 60  # 3小时
if time.time() - session_manager.start_time > session_manager.expiry_time:
    await session_manager.refresh_session()
```

### 4. 错误处理和重试

```python
# 带重试的HTTP请求
while True:
    try:
        response = session.get(url)
        if "retry-after" in response.headers:
            time.sleep(float(response.headers["Retry-After"]))
        else:
            break
    except Exception as e:
        logger.info(f"Error: {e}")
        time.sleep(60)
```

## 性能优化

### 1. 异步并发

- 使用 `aiohttp` 进行异步 HTTP 请求
- 支持多任务并发执行
- 信号量控制并发数量

### 2. 内存管理

- 及时清理临时数据
- 使用生成器减少内存占用
- 分批处理大量数据

### 3. 网络优化

- 自动重试机制
- 指数退避策略
- 连接池复用

## 日志和监控

### 日志配置

```python
logger.basicConfig(
    level=logger.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
```

### 监控指标

- 因子生成数量
- 模拟成功率
- 检测通过率
- 提交成功率

## 常见问题

### Q: 如何处理登录失败？

A: 检查 `user_info.txt` 中的用户名和密码是否正确，确保账号有效。

### Q: 因子模拟失败怎么办？

A: 检查网络连接，验证数据字段是否有效，确认 API 限制。

### Q: 如何自定义因子生成规则？

A: 修改 `fields.py` 中的字段定义，或在 `machine_lib.py` 中添加新的算子组合。

### Q: 提交超时如何处理？

A: 使用 `s7.py` 的 30 分钟超时版本，或检查因子复杂度。

## 版本历史

### v1.1.5 (2025.05.09)

- 日志时间改用 logging
- check.py 更加稳健，适应新规则

### v1.1.4 (2025.04.08)

- 添加 loguru 库支持
- 支持日志时间记录
- 重新运行时显示已完成因子数量

### v1.1.3 (2025.03.19)

- 增加微信提醒功能
- 需要配置 server 酱的 secret key

### v1.1.2 (2025.03.04)

- fields.py 增加豆包推荐数据

### v1.1.1 (2025.02.24)

- machine_lib.py 增加 SSL 问题解决方案

### v1.0 (2025.01.02)

- 初始版本发布
- 基础挖掘、检测、提交功能
