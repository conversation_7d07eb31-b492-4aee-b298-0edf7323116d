# 使用指定平台的Python基础镜像，支持多架构
FROM --platform=linux/amd64 python:3.9.13-slim

# 设置工作目录
WORKDIR /app

# 设置环境变量
ENV PYTHONUNBUFFERED=1
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONPATH=/app
ENV LD_LIBRARY_PATH=/app/pytransform:/usr/local/lib/pyarmor_runtime_000000:/usr/lib/x86_64-linux-gnu:/lib/x86_64-linux-gnu

# 更换apt源为国内镜像源（阿里云）
RUN sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list.d/debian.sources || \
    sed -i 's/deb.debian.org/mirrors.aliyun.com/g' /etc/apt/sources.list

# 安装系统依赖
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libc6-dev \
    libffi-dev \
    libssl-dev \
    libc6 \
    libgcc-s1 \
    libstdc++6 \
    && rm -rf /var/lib/apt/lists/*

# 升级pip并换源
RUN pip install --upgrade pip && \
    pip config set global.index-url https://mirrors.aliyun.com/pypi/simple/

# 复制requirements.txt并安装Python依赖
COPY requirements.txt /tmp/requirements.txt
RUN pip install --no-cache-dir -r /tmp/requirements.txt && \
    rm /tmp/requirements.txt

# 创建必要的目录结构
RUN mkdir -p /app/pytransform /app/static /app/templates /app/tasks

# 暴露端口（Flask默认端口5000）
EXPOSE 5000

# 设置容器启动时的默认命令
CMD ["python", "app.py"]