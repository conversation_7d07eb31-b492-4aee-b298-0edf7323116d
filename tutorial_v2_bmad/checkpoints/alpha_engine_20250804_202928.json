{
  "timestamp": "2025-08-04T20:31:22.796292",
  "config": {
    "dataset": "analyst4",
    "region": "USA",
    "universe": "TOP3000",
    "max_step": 3,
    "max_alphas_per_step": 1000,
    "time_windows": [
      5,
      22,
      66,
      120,
      240
    ],
    "concurrent_backtests": 3,
    "enable_quality_check": true,
    "min_quality_score": 0.3,
    "enable_checkpoint": true,
    "checkpoint_interval": 100,
    "max_expression_length": 500,
    "enable_deduplication": true
  },
  "completed_alphas": [
    "ts_zscore(volume, 22)",
    "ts_sum(open)",
    "ts_arg_min(volume)",
    "log_diff(ev_to_ebitda)",
    "ts_ir(volume)",
    "reverse(enterprise_value)",
    "ts_zscore(low, 5)",
    "ts_rank(open, 22)",
    "log_diff(high)",
    "log(vwap)",
    "reverse(earnings_per_share)",
    "zscore(earnings_per_share)",
    "inverse(enterprise_value)",
    "ts_product(high)",
    "ts_mean(low, 120)",
    "ts_zscore(close, 5)",
    "ts_ir(close)",
    "ts_max_diff(low)",
    "scale_down(close)",
    "quantile(price_earnings_ratio)",
    "ts_product(close)",
    "ts_product(low)",
    "scale_down(enterprise_value)",
    "ts_sum(low)",
    "ts_std_dev(low)",
    "inverse(price_earnings_ratio)",
    "zscore(price_earnings_ratio)",
    "log_diff(price_earnings_ratio)",
    "inverse(revenue_per_share)",
    "ts_min_diff(volume)",
    "log(price_to_book)",
    "ts_delta(open, 120)",
    "ts_delta(high, 240)",
    "ts_delta(close, 240)",
    "ts_min_diff(close)",
    "ts_delta(open, 240)",
    "sqrt(open)",
    "fraction(market_cap)",
    "ev_to_ebitda",
    "quantile(low)",
    "s_log_1p(revenue_per_share)",
    "low",
    "sqrt(enterprise_value)",
    "s_log_1p(market_cap)",
    "rank(enterprise_value)",
    "scale_down(earnings_per_share)",
    "reverse(price_to_book)",
    "cash_ratio",
    "rank(current_ratio)",
    "normalize(cash_ratio)",
    "ts_std_dev(open)",
    "ts_min_diff(high)",
    "s_log_1p(vwap)",
    "rank(book_value_per_share)",
    "ts_rank(close, 5)",
    "log(price_earnings_ratio)",
    "ts_zscore(close, 66)",
    "ts_zscore(open, 66)",
    "fraction(volume)",
    "sqrt(revenue_per_share)",
    "ts_rank(high, 5)",
    "log_diff(price_to_book)",
    "reverse(open)",
    "volume",
    "ts_scale(open)",
    "ts_zscore(close, 120)",
    "ts_rank(open, 240)",
    "ts_sum(volume)",
    "ts_mean(volume, 5)",
    "quantile(return_on_equity)",
    "inverse(high)",
    "ts_std_dev(high)",
    "ts_arg_max(open)",
    "reverse(revenue_per_share)",
    "revenue_per_share",
    "inverse(open)",
    "rank(close)",
    "fraction(cash_ratio)",
    "log(enterprise_value)",
    "ts_rank(volume, 120)",
    "ts_product(volume)",
    "high",
    "zscore(price_to_book)",
    "log_diff(revenue_per_share)",
    "inverse(earnings_per_share)",
    "quantile(enterprise_value)",
    "sqrt(cash_ratio)",
    "zscore(ev_to_ebitda)",
    "ts_mean(high, 22)",
    "scale_down(price_to_book)",
    "reverse(market_cap)",
    "rank(ev_to_ebitda)",
    "scale_down(price_earnings_ratio)",
    "reverse(cash_ratio)",
    "rank(open)",
    "log(low)",
    "quantile(book_value_per_share)",
    "quantile(market_cap)",
    "ts_rank(high, 240)",
    "ts_arg_min(high)",
    "log(cash_ratio)",
    "sqrt(earnings_per_share)",
    "ts_mean(open, 120)",
    "ts_rank(low, 120)",
    "scale_down(market_cap)",
    "inverse(current_ratio)",
    "log_diff(earnings_per_share)",
    "reverse(price_earnings_ratio)",
    "ts_delta(low, 22)",
    "inverse(return_on_equity)",
    "log_diff(close)",
    "ts_mean(close, 66)",
    "scale_down(vwap)",
    "fraction(revenue_per_share)",
    "ts_scale(close)",
    "scale_down(cash_ratio)",
    "rank(volume)",
    "normalize(vwap)",
    "normalize(current_ratio)",
    "inverse(ev_to_ebitda)",
    "quantile(return_on_assets)",
    "ts_arg_min(open)",
    "price_earnings_ratio",
    "s_log_1p(low)",
    "vwap",
    "current_ratio",
    "ts_rank(high, 120)",
    "fraction(vwap)",
    "ts_sum(high)",
    "s_log_1p(book_value_per_share)",
    "ts_delta(volume, 120)",
    "fraction(price_earnings_ratio)",
    "zscore(vwap)",
    "ts_mean(low, 66)",
    "ts_arg_max(volume)",
    "book_value_per_share",
    "scale_down(volume)",
    "log_diff(enterprise_value)",
    "ts_mean(volume, 120)",
    "ts_mean(volume, 240)",
    "ts_delta(close, 66)",
    "ts_rank(close, 22)",
    "normalize(return_on_assets)",
    "ts_product(open)",
    "ts_rank(high, 66)",
    "fraction(return_on_equity)",
    "log_diff(cash_ratio)",
    "zscore(revenue_per_share)",
    "normalize(book_value_per_share)",
    "ts_zscore(high, 120)",
    "ts_delta(close, 5)",
    "normalize(ev_to_ebitda)",
    "ts_zscore(low, 22)",
    "ts_mean(open, 22)",
    "ts_rank(low, 240)",
    "reverse(book_value_per_share)",
    "scale_down(high)",
    "log_diff(volume)",
    "log_diff(low)",
    "ts_mean(close, 120)",
    "rank(high)",
    "ts_zscore(close, 22)",
    "sqrt(close)",
    "ts_mean(high, 66)",
    "quantile(vwap)",
    "rank(market_cap)",
    "ts_arg_max(close)",
    "scale_down(ev_to_ebitda)",
    "s_log_1p(volume)",
    "quantile(volume)",
    "ts_min_diff(low)",
    "ts_rank(open, 5)",
    "s_log_1p(price_earnings_ratio)",
    "ts_zscore(open, 120)",
    "fraction(book_value_per_share)",
    "sqrt(ev_to_ebitda)",
    "s_log_1p(high)",
    "ts_delta(low, 66)",
    "inverse(low)",
    "scale_down(current_ratio)",
    "ts_rank(open, 120)",
    "normalize(high)",
    "zscore(current_ratio)",
    "zscore(enterprise_value)",
    "fraction(current_ratio)",
    "zscore(return_on_assets)",
    "ts_scale(high)",
    "sqrt(low)",
    "s_log_1p(enterprise_value)",
    "reverse(current_ratio)",
    "ts_returns(low)",
    "enterprise_value",
    "sqrt(volume)",
    "log(earnings_per_share)",
    "s_log_1p(ev_to_ebitda)",
    "return_on_assets",
    "inverse(volume)",
    "log_diff(book_value_per_share)",
    "log(open)",
    "ts_mean(open, 66)",
    "zscore(cash_ratio)",
    "scale_down(open)",
    "ts_rank(high, 22)",
    "ts_zscore(volume, 66)",
    "ts_delta(volume, 22)",
    "inverse(return_on_assets)",
    "sqrt(current_ratio)",
    "ts_delta(high, 22)",
    "ts_mean(high, 120)",
    "log(volume)",
    "quantile(ev_to_ebitda)",
    "quantile(revenue_per_share)",
    "s_log_1p(cash_ratio)",
    "ts_zscore(open, 5)",
    "zscore(open)",
    "quantile(current_ratio)",
    "sqrt(return_on_assets)",
    "ts_zscore(open, 240)",
    "ts_delta(low, 120)",
    "normalize(volume)",
    "zscore(return_on_equity)",
    "ts_zscore(high, 5)",
    "normalize(price_earnings_ratio)",
    "ts_mean(low, 22)",
    "ts_ir(high)",
    "normalize(market_cap)",
    "inverse(book_value_per_share)",
    "ts_rank(volume, 240)",
    "rank(price_earnings_ratio)",
    "ts_returns(open)",
    "zscore(low)",
    "ts_arg_max(low)",
    "reverse(vwap)",
    "ts_mean(high, 5)",
    "ts_rank(volume, 22)",
    "ts_delta(close, 120)",
    "log(ev_to_ebitda)",
    "s_log_1p(close)",
    "sqrt(return_on_equity)",
    "ts_mean(volume, 22)",
    "scale_down(return_on_assets)",
    "log(return_on_equity)",
    "rank(price_to_book)",
    "scale_down(return_on_equity)",
    "ts_delta(open, 66)",
    "ts_zscore(volume, 120)",
    "ts_mean(close, 22)",
    "price_to_book",
    "ts_rank(close, 120)",
    "ts_rank(close, 240)",
    "normalize(return_on_equity)",
    "ts_rank(volume, 66)",
    "rank(vwap)",
    "ts_mean(low, 5)",
    "ts_zscore(high, 22)",
    "log(market_cap)",
    "ts_zscore(high, 66)",
    "ts_max_diff(high)",
    "ts_std_dev(close)",
    "log_diff(return_on_assets)",
    "reverse(low)",
    "reverse(high)",
    "ts_zscore(high, 240)",
    "ts_scale(low)",
    "normalize(low)",
    "ts_delta(high, 120)",
    "ts_zscore(low, 240)",
    "fraction(high)",
    "fraction(earnings_per_share)",
    "normalize(earnings_per_share)",
    "quantile(open)",
    "log(book_value_per_share)",
    "fraction(enterprise_value)",
    "quantile(earnings_per_share)",
    "inverse(market_cap)",
    "s_log_1p(return_on_equity)",
    "ts_delta(high, 5)",
    "ts_mean(high, 240)",
    "ts_rank(low, 5)",
    "log_diff(vwap)",
    "ts_delta(volume, 5)",
    "ts_std_dev(volume)",
    "quantile(close)",
    "s_log_1p(earnings_per_share)",
    "normalize(revenue_per_share)",
    "fraction(price_to_book)",
    "ts_arg_max(high)",
    "ts_rank(low, 22)"
  ],
  "failed_alphas": [
    "reverse(volume)",
    "ts_arg_min(close)",
    "rank(cash_ratio)",
    "ts_delta(close, 22)",
    "inverse(vwap)",
    "log_diff(open)",
    "ts_zscore(volume, 240)",
    "zscore(volume)",
    "inverse(price_to_book)",
    "log_diff(return_on_equity)",
    "log(current_ratio)",
    "ts_returns(close)",
    "fraction(return_on_assets)",
    "ts_delta(volume, 240)",
    "log(revenue_per_share)",
    "ts_delta(low, 5)",
    "earnings_per_share",
    "ts_rank(low, 66)",
    "reverse(return_on_equity)",
    "sqrt(price_earnings_ratio)",
    "fraction(open)",
    "zscore(market_cap)",
    "ts_mean(close, 240)",
    "log_diff(market_cap)",
    "scale_down(low)",
    "rank(return_on_assets)",
    "ts_zscore(low, 66)",
    "return_on_equity",
    "reverse(close)",
    "inverse(close)",
    "ts_delta(open, 5)",
    "sqrt(vwap)",
    "fraction(low)",
    "ts_mean(open, 5)",
    "ts_zscore(volume, 5)",
    "inverse(cash_ratio)",
    "ts_min_diff(open)",
    "ts_zscore(open, 22)",
    "reverse(return_on_assets)",
    "close",
    "ts_rank(volume, 5)",
    "ts_rank(open, 66)",
    "normalize(close)",
    "fraction(close)",
    "ts_mean(open, 240)",
    "ts_max_diff(open)",
    "fraction(ev_to_ebitda)",
    "rank(earnings_per_share)",
    "ts_zscore(low, 120)",
    "open",
    "s_log_1p(open)",
    "sqrt(high)",
    "ts_max_diff(close)",
    "zscore(close)",
    "ts_delta(low, 240)",
    "rank(return_on_equity)",
    "normalize(open)",
    "log(close)",
    "ts_arg_min(low)",
    "ts_mean(volume, 66)",
    "ts_zscore(close, 240)",
    "zscore(book_value_per_share)",
    "quantile(high)",
    "rank(revenue_per_share)",
    "ts_mean(low, 240)",
    "quantile(price_to_book)",
    "ts_sum(close)",
    "sqrt(market_cap)",
    "sqrt(price_to_book)",
    "ts_ir(low)",
    "s_log_1p(price_to_book)",
    "reverse(ev_to_ebitda)",
    "s_log_1p(current_ratio)",
    "rank(low)",
    "log(high)",
    "scale_down(revenue_per_share)",
    "normalize(price_to_book)",
    "ts_rank(close, 66)",
    "ts_delta(high, 66)",
    "normalize(enterprise_value)",
    "s_log_1p(return_on_assets)",
    "log_diff(current_ratio)",
    "zscore(high)",
    "market_cap",
    "sqrt(book_value_per_share)",
    "quantile(cash_ratio)",
    "ts_ir(open)",
    "ts_delta(volume, 66)",
    "ts_delta(open, 22)",
    "scale_down(book_value_per_share)",
    "log(return_on_assets)",
    "ts_mean(close, 5)",
    "ts_returns(high)"
  ],
  "stats": {
    "total_generated": 381,
    "total_backtested": 288,
    "total_failed": 93,
    "generation_time": 0.004786014556884766,
    "backtest_time": 113.70451498031616,
    "start_time": 