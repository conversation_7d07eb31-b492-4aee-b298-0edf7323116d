{
  "timestamp": "2025-08-05T07:59:49.297232",
  "config": {
    "dataset": "analyst4",
    "region": "USA",
    "universe": "TOP3000",
    "max_step": 3,
    "max_alphas_per_step": 1000,
    "time_windows": [
      5,
      22,
      66,
      120,
      240
    ],
    "concurrent_backtests": 3,
    "enable_quality_check": true,
    "min_quality_score": 0.3,
    "enable_checkpoint": true,
    "checkpoint_interval": 100,
    "max_expression_length": 500,
    "enable_deduplication": true
  },
  "completed_alphas": [],
  "failed_alphas": [
    "zscore(current_ratio)",
    "ts_mean(open, 22)",
    "ts_mean(high, 5)",
    "s_log_1p(current_ratio)",
    "zscore(book_value_per_share)",
    "sqrt(ev_to_ebitda)",
    "ts_mean(low, 5)",
    "s_log_1p(enterprise_value)",
    "ts_mean(high, 66)",
    "ts_zscore(open, 240)",
    "normalize(ev_to_ebitda)",
    "sqrt(current_ratio)",
    "quantile(price_earnings_ratio)",
    "quantile(revenue_per_share)",
    "ts_zscore(high, 120)",
    "ts_min_diff(low)",
    "log(volume)",
    "scale_down(enterprise_value)",
    "zscore(return_on_equity)",
    "ts_rank(open, 120)",
    "zscore(price_earnings_ratio)",
    "ts_rank(high, 120)",
    "normalize(revenue_per_share)",
    "ts_rank(high, 22)",
    "scale_down(cash_ratio)",
    "fraction(return_on_assets)",
    "return_on_assets",
    "fraction(volume)",
    "ts_delta(high, 120)",
    "rank(high)",
    "ts_mean(volume, 22)",
    "scale_down(ev_to_ebitda)",
    "ts_rank(open, 5)",
    "log(return_on_equity)",
    "inverse(price_to_book)",
    "ts_zscore(close, 240)",
    "rank(earnings_per_share)",
    "inverse(current_ratio)",
    "sqrt(vwap)",
    "scale_down(market_cap)",
    "ts_rank(close, 22)",
    "ts_zscore(low, 5)",
    "log(open)",
    "ts_ir(high)",
    "rank(revenue_per_share)",
    "scale_down(vwap)",
    "s_log_1p(return_on_equity)",
    "scale_down(revenue_per_share)",
    "ts_returns(high)",
    "ts_zscore(volume, 22)",
    "ts_zscore(volume, 66)",
    "earnings_per_share",
    "reverse(market_cap)",
    "ts_returns(open)",
    "enterprise_value",
    "rank(volume)",
    "log_diff(return_on_assets)",
    "ts_arg_max(close)",
    "inverse(ev_to_ebitda)",
    "quantile(earnings_per_share)",
    "scale_down(price_to_book)",
    "log(price_earnings_ratio)",
    "fraction(return_on_equity)",
    "normalize(close)",
    "log_diff(low)",
    "ts_delta(low, 240)",
    "zscore(earnings_per_share)",
    "s_log_1p(open)",
    "close",
    "normalize(book_value_per_share)",
    "ts_mean(open, 120)",
    "rank(close)",
    "ts_mean(close, 22)",
    "inverse(close)",
    "ts_arg_max(volume)",
    "log(price_to_book)",
    "zscore(return_on_assets)",
    "zscore(cash_ratio)",
    "ts_delta(open, 5)",
    "ts_mean(high, 120)",
    "ts_max_diff(low)",
    "sqrt(revenue_per_share)",
    "fraction(close)",
    "market_cap",
    "ts_delta(high, 5)",
    "ts_min_diff(open)",
    "ts_zscore(open, 120)",
    "reverse(book_value_per_share)",
    "reverse(return_on_assets)",
    "zscore(ev_to_ebitda)",
    "log(revenue_per_share)",
    "log(low)",
    "ts_delta(low, 22)",
    "ts_zscore(close, 5)",
    "ts_rank(volume, 120)",
    "ts_product(volume)",
    "rank(low)",
    "ts_ir(open)",
    "ts_rank(open, 240)",
    "fraction(current_ratio)",
    "inverse(open)",
    "log(ev_to_ebitda)",
    "quantile(return_on_equity)",
    "normalize(open)",
    "ts_product(high)",
    "quantile(return_on_assets)",
    "log_diff(price_earnings_ratio)",
    "ts_delta(volume, 120)",
    "quantile(high)",
    "inverse(book_value_per_share)",
    "reverse(ev_to_ebitda)",
    "reverse(vwap)",
    "quantile(current_ratio)",
    "ts_delta(volume, 5)",
    "ts_delta(volume, 240)",
    "ts_std_dev(close)",
    "ts_zscore(volume, 5)",
    "fraction(low)",
    "fraction(ev_to_ebitda)",
    "log(current_ratio)",
    "high",
    "ts_zscore(high, 5)",
    "scale_down(current_ratio)",
    "ts_product(open)",
    "sqrt(market_cap)",
    "rank(price_to_book)",
    "ts_rank(volume, 66)",
    "ts_zscore(high, 66)",
    "rank(current_ratio)",
    "inverse(vwap)",
    "rank(market_cap)",
    "ts_delta(high, 66)",
    "reverse(close)",
    "zscore(vwap)",
    "normalize(return_on_equity)",
    "ts_rank(open, 22)",
    "log(close)",
    "ts_scale(high)",
    "s_log_1p(ev_to_ebitda)",
    "ts_delta(low, 5)",
    "ts_rank(low, 5)",
    "ts_scale(open)",
    "scale_down(return_on_assets)",
    "log_diff(market_cap)",
    "zscore(market_cap)",
    "price_earnings_ratio",
    "sqrt(close)",
    "s_log_1p(vwap)",
    "s_log_1p(close)",
    "ts_delta(close, 5)",
    "ts_arg_min(close)",
    "scale_down(high)",
    "ts_ir(low)",
    "inverse(volume)",
    "log_diff(ev_to_ebitda)",
    "normalize(high)",
    "sqrt(price_earnings_ratio)",
    "ts_arg_min(volume)",
    "normalize(cash_ratio)",
    "scale_down(open)",
    "ts_zscore(open, 66)",
    "ts_zscore(close, 66)",
    "ts_rank(low, 120)",
    "ts_std_dev(open)",
    "quantile(enterprise_value)",
    "ts_ir(volume)",
    "ts_rank(close, 5)",
    "zscore(enterprise_value)",
    "scale_down(volume)",
    "reverse(low)",
    "ts_mean(low, 22)",
    "ts_arg_max(open)",
    "rank(open)",
    "ts_arg_min(open)",
    "ts_rank(low, 66)",
    "quantile(cash_ratio)",
    "s_log_1p(price_earnings_ratio)",
    "quantile(close)",
    "log(cash_ratio)",
    "book_value_per_share",
    "ts_mean(volume, 240)",
    "ts_max_diff(high)",
    "ts_mean(low, 240)",
    "ts_arg_max(high)",
    "normalize(price_earnings_ratio)",
    "quantile(low)",
    "reverse(return_on_equity)",
    "scale_down(low)",
    "zscore(open)",
    "reverse(earnings_per_share)",
    "ts_rank(low, 22)",
    "ts_zscore(volume, 240)",
    "fraction(vwap)",
    "log_diff(enterprise_value)",
    "reverse(volume)",
    "fraction(enterprise_value)",
    "sqrt(return_on_equity)",
    "s_log_1p(revenue_per_share)",
    "ts_mean(close, 120)",
    "ts_delta(low, 120)",
    "fraction(book_value_per_share)",
    "fraction(cash_ratio)",
    "ts_min_diff(close)",
    "sqrt(high)",
    "ts_rank(high, 5)",
    "ts_ir(close)",
    "ts_sum(high)",
    "ts_zscore(high, 22)",
    "ts_mean(volume, 66)",
    "fraction(revenue_per_share)",
    "ts_zscore(open, 22)",
    "normalize(volume)",
    "ts_mean(volume, 5)",
    "ts_arg_max(low)",
    "log_diff(high)",
    "zscore(revenue_per_share)",
    "ts_rank(high, 66)",
    "ts_sum(low)",
    "ts_zscore(open, 5)",
    "sqrt(volume)",
    "ts_arg_min(low)",
    "ts_zscore(low, 240)",
    "reverse(cash_ratio)",
    "ts_zscore(volume, 120)",
    "ts_std_dev(volume)",
    "normalize(vwap)",
    "ts_rank(high, 240)",
    "quantile(vwap)",
    "log_diff(earnings_per_share)",
    "s_log_1p(price_to_book)",
    "s_log_1p(low)",
    "inverse(cash_ratio)",
    "ts_zscore(high, 240)",
    "log_diff(cash_ratio)",
    "inverse(revenue_per_share)",
    "ts_delta(open, 22)",
    "cash_ratio",
    "sqrt(cash_ratio)",
    "rank(return_on_equity)",
    "sqrt(price_to_book)",
    "open",
    "reverse(enterprise_value)",
    "log(earnings_per_share)",
    "ts_rank(open, 66)",
    "volume",
    "quantile(ev_to_ebitda)",
    "vwap",
    "ts_arg_min(high)",
    "ts_delta(volume, 22)",
    "s_log_1p(book_value_per_share)",
    "ts_mean(close, 240)",
    "revenue_per_share",
    "ts_mean(close, 5)",
    "normalize(price_to_book)",
    "log_diff(close)",
    "inverse(enterprise_value)",
    "scale_down(book_value_per_share)",
    "log_diff(return_on_equity)",
    "normalize(current_ratio)",
    "ts_sum(open)",
    "ts_delta(low, 66)",
    "ts_rank(close, 240)",
    "s_log_1p(cash_ratio)",
    "ts_rank(close, 120)",
    "ts_zscore(low, 22)",
    "log(vwap)",
    "ts_zscore(low, 66)",
    "price_to_book",
    "s_log_1p(return_on_assets)",
    "s_log_1p(earnings_per_share)",
    "ts_delta(volume, 66)",
    "zscore(low)",
    "quantile(volume)",
    "reverse(high)",
    "ts_product(close)",
    "ts_mean(high, 22)",
    "ts_std_dev(low)",
    "reverse(current_ratio)",
    "return_on_equity",
    "ts_delta(open, 120)",
    "ts_zscore(close, 120)",
    "log_diff(book_value_per_share)",
    "ts_mean(open, 240)",
    "ts_min_diff(high)",
    "sqrt(low)",
    "scale_down(return_on_equity)",
    "log_diff(open)",
    "scale_down(earnings_per_share)",
    "quantile(price_to_book)",
    "fraction(market_cap)",
    "fraction(open)",
    "ts_delta(close, 240)",
    "zscore(volume)",
    "ts_mean(high, 240)",
    "ts_mean(open, 66)",
    "log(market_cap)",
    "quantile(market_cap)",
    "log(enterprise_value)",
    "normalize(low)",
    "reverse(price_earnings_ratio)",
    "ts_delta(close, 120)",
    "log_diff(volume)",
    "rank(vwap)",
    "ts_zscore(low, 120)",
    "ts_mean(volume, 120)",
    "log_diff(price_to_book)",
    "ts_delta(high, 240)",
    "s_log_1p(high)",
    "sqrt(open)",
    "log(book_value_per_share)",
    "ts_delta(close, 66)",
    "inverse(low)",
    "fraction(earnings_per_share)",
    "reverse(price_to_book)",
    "ev_to_ebitda",
    "sqrt(enterprise_value)",
    "rank(return_on_assets)",
    "sqrt(earnings_per_share)",
    "log_diff(vwap)",
    "s_log_1p(market_cap)",
    "rank(ev_to_ebitda)",
    "log(high)",
    "reverse(open)",
    "inverse(market_cap)",
    "ts_scale(close)",
    "s_log_1p(volume)",
    "ts_zscore(close, 22)",
    "ts_returns(low)",
    "ts_sum(close)",
    "log_diff(revenue_per_share)",
    "ts_product(low)",
    "ts_std_dev(high)",
    "fraction(price_earnings_ratio)",
    "ts_delta(high, 22)",
    "ts_delta(open, 66)",
    "ts_sum(volume)",
    "normalize(enterprise_value)",
    "scale_down(close)",
    "quantile(open)",
    "rank(enterprise_value)",
    "rank(cash_ratio)",
    "ts_rank(close, 66)",
    "ts_mean(open, 5)",
    "ts_mean(low, 66)",
    "log_diff(current_ratio)",
    "ts_mean(low, 120)",
    "sqrt(return_on_assets)",
    "rank(book_value_per_share)",
    "scale_down(price_earnings_ratio)",
    "ts_max_diff(close)",
    "reverse(revenue_per_share)",
    "ts_returns(close)",
    "log(return_on_assets)",
    "ts_mean(close, 66)",
    "zscore(price_to_book)",
    "normalize(market_cap)",
    "ts_rank(low, 240)",
    "rank(price_earnings_ratio)",
    "zscore(close)",
    "ts_rank(volume, 5)",
    "normalize(return_on_assets)",
    "inverse(high)",
    "quantile(book_value_per_share)",
    "ts_scale(low)",
    "fraction(price_to_book)",
    "ts_rank(volume, 22)",
    "sqrt(book_value_per_share)",
    "ts_min_diff(volume)",
    "ts_delta(open, 240)",
    "inverse(return_on_equity)",
    "inverse(return_on_assets)",
    "low",
    "inverse(price_earnings_ratio)",
    "normalize(earnings_per_share)",
    "ts_max_diff(open)",
    "current_ratio",
    "fraction(high)",
    "ts_delta(close, 22)",
    "ts_rank(volume, 240)",
    "inverse(earnings_per_share)",
    "zscore(high)"
  ],
  "stats": {
    "total_generated": 381,
    "total_backtested": 0,
    "total_failed": 381,
    "generation_time": 0.003618001937866211,
    "backtest_time": 0.4499330520629883,
    "start_time": 