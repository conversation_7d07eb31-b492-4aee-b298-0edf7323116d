{
  "timestamp": "2025-08-04T19:25:42.101883",
  "config": {
    "dataset": "analyst4",
    "region": "USA",
    "universe": "TOP3000",
    "max_step": 3,
    "max_alphas_per_step": 1000,
    "time_windows": [
      5,
      22,
      66,
      120,
      240
    ],
    "concurrent_backtests": 3,
    "enable_quality_check": true,
    "min_quality_score": 0.3,
    "enable_checkpoint": true,
    "checkpoint_interval": 100,
    "max_expression_length": 500,
    "enable_deduplication": true
  },
  "completed_alphas": [
    "scale_down(high)",
    "normalize(current_ratio)",
    "ts_zscore(high, 22)",
    "return_on_assets",
    "ts_mean(volume, 22)",
    "return_on_equity",
    "log(market_cap)",
    "reverse(return_on_equity)",
    "ts_rank(high, 120)",
    "rank(earnings_per_share)",
    "log(price_to_book)",
    "inverse(ev_to_ebitda)",
    "inverse(book_value_per_share)",
    "ts_zscore(high, 66)",
    "inverse(return_on_equity)",
    "ts_rank(volume, 5)",
    "normalize(market_cap)",
    "normalize(ev_to_ebitda)",
    "log_diff(high)",
    "sqrt(open)",
    "ts_mean(volume, 66)",
    "reverse(low)",
    "quantile(price_earnings_ratio)",
    "ts_delta(low, 5)",
    "normalize(vwap)",
    "sqrt(close)",
    "s_log_1p(cash_ratio)",
    "ts_arg_min(open)",
    "ts_returns(open)",
    "ts_delta(low, 66)",
    "ts_rank(close, 5)",
    "log(price_earnings_ratio)",
    "quantile(book_value_per_share)",
    "log_diff(open)",
    "reverse(current_ratio)",
    "zscore(earnings_per_share)",
    "ts_mean(close, 120)",
    "rank(current_ratio)",
    "s_log_1p(return_on_assets)",
    "ts_delta(low, 120)",
    "fraction(volume)",
    "s_log_1p(revenue_per_share)",
    "quantile(current_ratio)",
    "rank(high)",
    "quantile(cash_ratio)",
    "normalize(price_to_book)",
    "ts_min_diff(high)",
    "ts_zscore(close, 22)",
    "quantile(enterprise_value)",
    "ts_sum(volume)",
    "ts_delta(volume, 66)",
    "ts_zscore(close, 66)",
    "normalize(high)",
    "rank(vwap)",
    "quantile(return_on_assets)",
    "sqrt(return_on_equity)",
    "sqrt(enterprise_value)",
    "ts_rank(close, 240)",
    "ts_zscore(volume, 120)",
    "ts_rank(open, 22)",
    "zscore(cash_ratio)",
    "rank(open)",
    "log_diff(price_to_book)",
    "log_diff(cash_ratio)",
    "ts_delta(low, 22)",
    "ts_delta(volume, 240)",
    "volume",
    "reverse(high)",
    "ts_delta(volume, 120)",
    "ts_arg_max(high)",
    "price_to_book",
    "normalize(book_value_per_share)",
    "rank(price_to_book)",
    "rank(close)",
    "ts_arg_min(low)",
    "ts_mean(low, 66)",
    "sqrt(high)",
    "ts_rank(low, 66)",
    "ts_mean(open, 240)",
    "log(cash_ratio)",
    "rank(return_on_equity)",
    "quantile(return_on_equity)",
    "s_log_1p(low)",
    "fraction(enterprise_value)",
    "ts_rank(low, 5)",
    "rank(volume)",
    "ts_rank(volume, 22)",
    "ts_ir(low)",
    "ts_zscore(volume, 5)",
    "inverse(price_earnings_ratio)",
    "ts_product(volume)",
    "ts_max_diff(close)",
    "ts_mean(volume, 120)",
    "normalize(earnings_per_share)",
    "sqrt(market_cap)",
    "ts_zscore(low, 240)",
    "sqrt(earnings_per_share)",
    "zscore(return_on_assets)",
    "ts_returns(high)",
    "s_log_1p(market_cap)",
    "fraction(high)",
    "fraction(close)",
    "ts_zscore(low, 66)",
    "fraction(price_to_book)",
    "ts_zscore(open, 66)",
    "fraction(vwap)",
    "scale_down(return_on_equity)",
    "ts_zscore(open, 5)",
    "ts_zscore(volume, 22)",
    "ts_zscore(high, 5)",
    "ts_mean(low, 22)",
    "ts_min_diff(low)",
    "ts_mean(low, 5)",
    "low",
    "s_log_1p(book_value_per_share)",
    "quantile(high)",
    "normalize(enterprise_value)",
    "s_log_1p(price_earnings_ratio)",
    "high",
    "ts_zscore(close, 240)",
    "ts_delta(close, 120)",
    "sqrt(low)",
    "ts_zscore(open, 22)",
    "s_log_1p(current_ratio)",
    "ts_mean(open, 120)",
    "scale_down(open)",
    "s_log_1p(high)",
    "log(low)",
    "ts_mean(high, 22)",
    "zscore(vwap)",
    "rank(book_value_per_share)",
    "ts_delta(close, 66)",
    "fraction(revenue_per_share)",
    "log(ev_to_ebitda)",
    "reverse(ev_to_ebitda)",
    "scale_down(vwap)",
    "ts_rank(volume, 120)",
    "reverse(volume)",
    "log(book_value_per_share)",
    "rank(enterprise_value)",
    "ts_mean(open, 66)",
    "quantile(market_cap)",
    "rank(return_on_assets)",
    "inverse(earnings_per_share)",
    "scale_down(earnings_per_share)",
    "zscore(revenue_per_share)",
    "s_log_1p(earnings_per_share)",
    "ts_ir(high)",
    "log_diff(current_ratio)",
    "scale_down(book_value_per_share)",
    "sqrt(price_earnings_ratio)",
    "quantile(revenue_per_share)",
    "ts_sum(high)",
    "ts_arg_max(low)",
    "ts_mean(close, 240)",
    "scale_down(volume)",
    "ts_delta(high, 120)",
    "ts_zscore(open, 240)",
    "reverse(book_value_per_share)",
    "scale_down(current_ratio)",
    "log(current_ratio)",
    "zscore(return_on_equity)",
    "ts_delta(high, 240)",
    "reverse(enterprise_value)",
    "ts_ir(open)",
    "s_log_1p(return_on_equity)",
    "ts_min_diff(open)",
    "ts_zscore(volume, 66)",
    "ts_delta(open, 240)",
    "earnings_per_share",
    "ts_rank(open, 120)",
    "s_log_1p(vwap)",
    "reverse(vwap)",
    "normalize(cash_ratio)",
    "s_log_1p(enterprise_value)",
    "ts_rank(high, 22)",
    "s_log_1p(open)",
    "rank(revenue_per_share)",
    "normalize(close)",
    "log_diff(price_earnings_ratio)",
    "sqrt(cash_ratio)",
    "ts_delta(open, 120)",
    "ts_rank(close, 22)",
    "ts_ir(volume)",
    "fraction(return_on_assets)",
    "sqrt(book_value_per_share)",
    "zscore(current_ratio)",
    "fraction(ev_to_ebitda)",
    "market_cap",
    "ts_rank(high, 5)",
    "ts_zscore(open, 120)",
    "quantile(earnings_per_share)",
    "ts_scale(low)",
    "s_log_1p(volume)",
    "ts_product(close)",
    "ts_arg_max(close)",
    "ts_mean(open, 22)",
    "ts_rank(high, 240)",
    "log(return_on_assets)",
    "ts_mean(low, 120)",
    "rank(price_earnings_ratio)",
    "ts_delta(open, 5)",
    "ts_zscore(volume, 240)",
    "quantile(volume)",
    "ts_max_diff(low)",
    "inverse(volume)",
    "ts_mean(high, 66)",
    "log_diff(vwap)",
    "rank(market_cap)",
    "quantile(low)",
    "log_diff(volume)",
    "fraction(low)",
    "ts_delta(close, 5)",
    "reverse(market_cap)",
    "reverse(close)",
    "scale_down(cash_ratio)",
    "ts_rank(low, 22)",
    "ts_mean(volume, 240)",
    "ts_rank(low, 240)",
    "quantile(ev_to_ebitda)",
    "ts_min_diff(close)",
    "scale_down(close)",
    "ts_delta(high, 66)",
    "ts_std_dev(open)",
    "log_diff(ev_to_ebitda)",
    "ts_arg_max(open)",
    "ts_delta(close, 22)",
    "scale_down(ev_to_ebitda)",
    "rank(cash_ratio)",
    "fraction(current_ratio)",
    "ts_max_diff(high)",
    "normalize(volume)",
    "ts_delta(volume, 5)",
    "log(open)",
    "ts_mean(high, 240)",
    "log(high)",
    "sqrt(price_to_book)",
    "ts_zscore(low, 22)",
    "ts_max_diff(open)",
    "ts_sum(low)",
    "log(vwap)",
    "zscore(book_value_per_share)",
    "inverse(current_ratio)",
    "ts_mean(close, 5)",
    "ts_rank(open, 240)",
    "ts_rank(volume, 66)",
    "reverse(price_earnings_ratio)",
    "log_diff(close)",
    "zscore(market_cap)",
    "ts_zscore(close, 5)",
    "ts_scale(high)",
    "current_ratio",
    "ts_sum(close)",
    "normalize(revenue_per_share)",
    "zscore(price_to_book)",
    "fraction(return_on_equity)",
    "ts_delta(open, 22)",
    "sqrt(volume)",
    "inverse(return_on_assets)",
    "normalize(return_on_assets)",
    "sqrt(current_ratio)",
    "quantile(vwap)",
    "normalize(return_on_equity)",
    "ts_product(open)",
    "ts_delta(high, 5)",
    "ts_arg_min(volume)",
    "log(enterprise_value)",
    "ts_ir(close)",
    "ts_mean(high, 5)",
    "reverse(earnings_per_share)",
    "log(revenue_per_share)",
    "ts_returns(low)",
    "fraction(market_cap)",
    "zscore(high)",
    "inverse(high)",
    "s_log_1p(close)",
    "normalize(open)",
    "log(earnings_per_share)",
    "zscore(price_earnings_ratio)",
    "ts_arg_max(volume)",
    "inverse(low)",
    "zscore(ev_to_ebitda)",
    "normalize(price_earnings_ratio)",
    "ts_mean(high, 120)",
    "log_diff(market_cap)",
    "log_diff(return_on_equity)",
    "ts_zscore(high, 120)",
    "close",
    "normalize(low)",
    "ts_zscore(close, 120)",
    "ts_delta(open, 66)",
    "log_diff(book_value_per_share)",
    "sqrt(vwap)",
    "sqrt(revenue_per_share)",
    "fraction(cash_ratio)",
    "reverse(cash_ratio)"
  ],
  "failed_alphas": [
    "scale_down(price_to_book)",
    "log(return_on_equity)",
    "ts_rank(open, 5)",
    "ts_mean(close, 66)",
    "scale_down(enterprise_value)",
    "rank(low)",
    "quantile(close)",
    "ts_delta(volume, 22)",
    "sqrt(return_on_assets)",
    "cash_ratio",
    "ts_std_dev(close)",
    "log_diff(low)",
    "ts_mean(volume, 5)",
    "log_diff(enterprise_value)",
    "book_value_per_share",
    "ts_rank(high, 66)",
    "ts_sum(open)",
    "s_log_1p(ev_to_ebitda)",
    "ts_rank(close, 120)",
    "log_diff(return_on_assets)",
    "inverse(enterprise_value)",
    "ts_mean(low, 240)",
    "log(volume)",
    "ts_delta(close, 240)",
    "price_earnings_ratio",
    "ts_mean(close, 22)",
    "vwap",
    "inverse(cash_ratio)",
    "scale_down(return_on_assets)",
    "ts_returns(close)",
    "ts_rank(open, 66)",
    "inverse(revenue_per_share)",
    "zscore(enterprise_value)",
    "enterprise_value",
    "zscore(volume)",
    "ts_scale(open)",
    "ts_mean(open, 5)",
    "ts_zscore(low, 120)",
    "ts_std_dev(low)",
    "fraction(earnings_per_share)",
    "ts_product(low)",
    "fraction(open)",
    "ts_rank(volume, 240)",
    "ts_delta(low, 240)",
    "inverse(vwap)",
    "log(close)",
    "ts_zscore(low, 5)",
    "open",
    "inverse(price_to_book)",
    "ts_scale(close)",
    "ev_to_ebitda",
    "revenue_per_share",
    "scale_down(price_earnings_ratio)",
    "reverse(price_to_book)",
    "scale_down(market_cap)",
    "sqrt(ev_to_ebitda)",
    "fraction(book_value_per_share)",
    "inverse(close)",
    "ts_delta(high, 22)",
    "rank(ev_to_ebitda)",
    "fraction(price_earnings_ratio)",
    "reverse(open)",
    "reverse(return_on_assets)",
    "ts_min_diff(volume)",
    "inverse(market_cap)",
    "zscore(close)",
    "quantile(open)",
    "ts_rank(low, 120)",
    "ts_std_dev(high)",
    "scale_down(revenue_per_share)",
    "zscore(open)",
    "ts_arg_min(high)",
    "ts_std_dev(volume)",
    "zscore(low)",
    "inverse(open)",
    "scale_down(low)",
    "ts_rank(close, 66)",
    "log_diff(earnings_per_share)",
    "ts_arg_min(close)",
    "ts_product(high)",
    "ts_zscore(high, 240)",
    "reverse(revenue_per_share)",
    "quantile(price_to_book)",
    "log_diff(revenue_per_share)",
    "s_log_1p(price_to_book)"
  ],
  "stats": {
    "total_generated": 381,
    "total_backtested": 296,
    "total_failed": 85,
    "generation_time": 0.00379180908203125,
    "backtest_time": 163.03593492507935,
    "start_time": 