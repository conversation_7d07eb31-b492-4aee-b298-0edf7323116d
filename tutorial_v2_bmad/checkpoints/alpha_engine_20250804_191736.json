{
  "timestamp": "2025-08-04T19:20:18.469827",
  "config": {
    "dataset": "analyst4",
    "region": "USA",
    "universe": "TOP3000",
    "max_step": 3,
    "max_alphas_per_step": 1000,
    "time_windows": [
      5,
      22,
      66,
      120,
      240
    ],
    "concurrent_backtests": 3,
    "enable_quality_check": true,
    "min_quality_score": 0.3,
    "enable_checkpoint": true,
    "checkpoint_interval": 100,
    "max_expression_length": 500,
    "enable_deduplication": true
  },
  "completed_alphas": [
    "zscore(cash_ratio)",
    "reverse(market_cap)",
    "log_diff(open)",
    "log(low)",
    "zscore(low)",
    "ts_zscore(volume, 66)",
    "zscore(return_on_assets)",
    "rank(low)",
    "log_diff(price_to_book)",
    "ts_delta(low, 120)",
    "reverse(price_to_book)",
    "ts_arg_max(open)",
    "ts_returns(high)",
    "ts_mean(low, 22)",
    "reverse(vwap)",
    "sqrt(ev_to_ebitda)",
    "fraction(market_cap)",
    "ts_rank(volume, 22)",
    "s_log_1p(vwap)",
    "ts_mean(low, 240)",
    "ts_delta(low, 5)",
    "reverse(volume)",
    "high",
    "sqrt(return_on_assets)",
    "ts_zscore(close, 120)",
    "ts_mean(high, 5)",
    "rank(enterprise_value)",
    "ts_delta(open, 240)",
    "ts_delta(low, 66)",
    "ts_zscore(low, 240)",
    "scale_down(price_to_book)",
    "ts_zscore(volume, 5)",
    "ts_zscore(open, 5)",
    "ts_rank(high, 5)",
    "inverse(earnings_per_share)",
    "log_diff(low)",
    "log(high)",
    "ts_product(open)",
    "reverse(revenue_per_share)",
    "log(return_on_assets)",
    "ts_returns(close)",
    "ts_delta(volume, 22)",
    "scale_down(ev_to_ebitda)",
    "zscore(book_value_per_share)",
    "ts_std_dev(open)",
    "ts_arg_max(volume)",
    "fraction(return_on_equity)",
    "earnings_per_share",
    "reverse(price_earnings_ratio)",
    "ts_mean(close, 120)",
    "ts_arg_min(volume)",
    "fraction(volume)",
    "log_diff(revenue_per_share)",
    "normalize(market_cap)",
    "ts_zscore(close, 66)",
    "ts_arg_min(open)",
    "s_log_1p(volume)",
    "sqrt(vwap)",
    "sqrt(current_ratio)",
    "zscore(price_to_book)",
    "s_log_1p(open)",
    "normalize(vwap)",
    "reverse(ev_to_ebitda)",
    "ts_delta(open, 22)",
    "zscore(vwap)",
    "ts_delta(high, 5)",
    "s_log_1p(ev_to_ebitda)",
    "ts_mean(high, 22)",
    "log_diff(enterprise_value)",
    "log(price_to_book)",
    "ts_max_diff(low)",
    "scale_down(earnings_per_share)",
    "rank(return_on_assets)",
    "normalize(cash_ratio)",
    "rank(vwap)",
    "inverse(market_cap)",
    "normalize(price_earnings_ratio)",
    "inverse(price_earnings_ratio)",
    "reverse(close)",
    "ts_rank(open, 120)",
    "rank(revenue_per_share)",
    "ts_sum(low)",
    "ts_scale(open)",
    "normalize(enterprise_value)",
    "fraction(current_ratio)",
    "ts_delta(low, 22)",
    "scale_down(close)",
    "inverse(current_ratio)",
    "sqrt(earnings_per_share)",
    "ts_zscore(close, 5)",
    "inverse(return_on_assets)",
    "return_on_equity",
    "sqrt(market_cap)",
    "ts_rank(close, 5)",
    "ts_mean(open, 5)",
    "sqrt(close)",
    "ts_rank(close, 66)",
    "scale_down(vwap)",
    "scale_down(price_earnings_ratio)",
    "ts_rank(open, 66)",
    "ts_mean(open, 66)",
    "ts_delta(high, 120)",
    "reverse(high)",
    "ts_delta(close, 22)",
    "ts_scale(low)",
    "zscore(price_earnings_ratio)",
    "ts_rank(close, 240)",
    "normalize(low)",
    "sqrt(price_earnings_ratio)",
    "ts_product(low)",
    "ts_zscore(high, 240)",
    "zscore(ev_to_ebitda)",
    "zscore(market_cap)",
    "scale_down(open)",
    "scale_down(enterprise_value)",
    "reverse(earnings_per_share)",
    "s_log_1p(close)",
    "ts_product(high)",
    "log(open)",
    "close",
    "log_diff(earnings_per_share)",
    "s_log_1p(enterprise_value)",
    "ts_scale(high)",
    "log_diff(current_ratio)",
    "log_diff(price_earnings_ratio)",
    "book_value_per_share",
    "quantile(price_earnings_ratio)",
    "ts_ir(volume)",
    "sqrt(volume)",
    "ts_delta(high, 240)",
    "ts_rank(low, 66)",
    "ts_mean(close, 22)",
    "ts_mean(close, 5)",
    "ts_zscore(close, 22)",
    "rank(price_to_book)",
    "inverse(cash_ratio)",
    "rank(market_cap)",
    "ts_rank(close, 120)",
    "zscore(volume)",
    "scale_down(current_ratio)",
    "inverse(book_value_per_share)",
    "quantile(low)",
    "inverse(volume)",
    "vwap",
    "scale_down(book_value_per_share)",
    "ts_sum(volume)",
    "rank(earnings_per_share)",
    "quantile(vwap)",
    "ts_min_diff(low)",
    "ts_mean(low, 120)",
    "quantile(return_on_equity)",
    "sqrt(return_on_equity)",
    "ts_zscore(open, 240)",
    "ts_rank(volume, 120)",
    "ts_arg_min(close)",
    "current_ratio",
    "normalize(close)",
    "ts_ir(low)",
    "ts_zscore(high, 120)",
    "sqrt(cash_ratio)",
    "ts_sum(open)",
    "ts_zscore(open, 120)",
    "zscore(current_ratio)",
    "rank(volume)",
    "fraction(enterprise_value)",
    "ts_rank(volume, 66)",
    "sqrt(enterprise_value)",
    "inverse(enterprise_value)",
    "log_diff(book_value_per_share)",
    "log_diff(vwap)",
    "quantile(ev_to_ebitda)",
    "ts_ir(open)",
    "scale_down(high)",
    "ts_zscore(volume, 22)",
    "rank(open)",
    "ts_rank(high, 22)",
    "ts_rank(high, 120)",
    "quantile(high)",
    "s_log_1p(return_on_assets)",
    "normalize(earnings_per_share)",
    "ts_zscore(low, 5)",
    "s_log_1p(high)",
    "ts_max_diff(high)",
    "ts_delta(close, 240)",
    "price_earnings_ratio",
    "quantile(volume)",
    "ts_mean(low, 66)",
    "log(cash_ratio)",
    "ts_sum(high)",
    "ts_product(close)",
    "quantile(market_cap)",
    "ts_mean(volume, 22)",
    "low",
    "volume",
    "s_log_1p(return_on_equity)",
    "normalize(open)",
    "ts_scale(close)",
    "scale_down(cash_ratio)",
    "return_on_assets",
    "fraction(low)",
    "scale_down(revenue_per_share)",
    "reverse(return_on_equity)",
    "scale_down(return_on_assets)",
    "log(return_on_equity)",
    "quantile(enterprise_value)",
    "scale_down(market_cap)",
    "s_log_1p(earnings_per_share)",
    "log(revenue_per_share)",
    "ev_to_ebitda",
    "scale_down(volume)",
    "log_diff(volume)",
    "ts_rank(open, 5)",
    "normalize(return_on_assets)",
    "ts_zscore(low, 66)",
    "s_log_1p(price_earnings_ratio)",
    "rank(current_ratio)",
    "ts_rank(open, 240)",
    "ts_arg_max(high)",
    "log(earnings_per_share)",
    "inverse(high)",
    "fraction(revenue_per_share)",
    "rank(cash_ratio)",
    "ts_mean(high, 240)",
    "zscore(earnings_per_share)",
    "zscore(enterprise_value)",
    "normalize(current_ratio)",
    "cash_ratio",
    "revenue_per_share",
    "ts_zscore(low, 120)",
    "ts_std_dev(low)",
    "ts_mean(open, 240)",
    "ts_arg_max(close)",
    "ts_arg_max(low)",
    "log_diff(ev_to_ebitda)",
    "s_log_1p(market_cap)",
    "ts_zscore(open, 22)",
    "fraction(vwap)",
    "normalize(price_to_book)",
    "ts_delta(open, 5)",
    "ts_mean(open, 120)",
    "log_diff(market_cap)",
    "ts_std_dev(high)",
    "ts_min_diff(close)",
    "ts_zscore(volume, 120)",
    "log(vwap)",
    "fraction(price_earnings_ratio)",
    "inverse(close)",
    "inverse(return_on_equity)",
    "quantile(close)",
    "price_to_book",
    "ts_std_dev(volume)",
    "sqrt(low)",
    "log_diff(cash_ratio)",
    "ts_rank(low, 120)",
    "ts_min_diff(volume)",
    "s_log_1p(current_ratio)",
    "fraction(return_on_assets)",
    "sqrt(book_value_per_share)",
    "inverse(vwap)",
    "quantile(current_ratio)",
    "s_log_1p(price_to_book)",
    "ts_zscore(high, 5)",
    "fraction(price_to_book)",
    "s_log_1p(low)",
    "log(price_earnings_ratio)",
    "ts_arg_min(high)",
    "log(enterprise_value)",
    "open",
    "ts_returns(low)",
    "quantile(open)",
    "reverse(return_on_assets)",
    "inverse(open)",
    "ts_mean(high, 120)",
    "ts_mean(open, 22)",
    "ts_zscore(close, 240)",
    "rank(close)",
    "ts_rank(low, 5)",
    "ts_mean(volume, 5)",
    "ts_max_diff(open)",
    "log_diff(high)",
    "ts_std_dev(close)",
    "ts_delta(volume, 66)",
    "log_diff(return_on_assets)",
    "sqrt(open)",
    "ts_min_diff(open)",
    "ts_min_diff(high)",
    "zscore(revenue_per_share)",
    "ts_delta(open, 66)",
    "ts_returns(open)",
    "inverse(price_to_book)",
    "normalize(volume)",
    "inverse(revenue_per_share)",
    "log(book_value_per_share)",
    "ts_delta(volume, 240)",
    "ts_ir(close)",
    "enterprise_value",
    "rank(return_on_equity)",
    "quantile(earnings_per_share)",
    "ts_max_diff(close)",
    "normalize(ev_to_ebitda)",
    "ts_delta(close, 120)",
    "reverse(cash_ratio)",
    "ts_delta(open, 120)",
    "ts_delta(close, 66)",
    "log(ev_to_ebitda)",
    "ts_zscore(high, 22)",
    "zscore(return_on_equity)",
    "ts_mean(high, 66)",
    "scale_down(return_on_equity)",
    "log(close)"
  ],
  "failed_alphas": [
    "s_log_1p(cash_ratio)",
    "fraction(close)",
    "quantile(revenue_per_share)",
    "ts_mean(low, 5)",
    "ts_rank(volume, 240)",
    "reverse(book_value_per_share)",
    "quantile(cash_ratio)",
    "s_log_1p(book_value_per_share)",
    "normalize(revenue_per_share)",
    "ts_sum(close)",
    "ts_mean(volume, 240)",
    "log_diff(return_on_equity)",
    "ts_rank(low, 22)",
    "fraction(book_value_per_share)",
    "rank(price_earnings_ratio)",
    "ts_rank(close, 22)",
    "ts_mean(volume, 120)",
    "ts_delta(high, 66)",
    "market_cap",
    "zscore(high)",
    "log_diff(close)",
    "ts_mean(close, 240)",
    "ts_mean(volume, 66)",
    "reverse(open)",
    "fraction(cash_ratio)",
    "fraction(high)",
    "zscore(open)",
    "reverse(enterprise_value)",
    "sqrt(price_to_book)",
    "inverse(ev_to_ebitda)",
    "ts_rank(high, 240)",
    "ts_arg_min(low)",
    "quantile(price_to_book)",
    "ts_delta(volume, 120)",
    "ts_product(volume)",
    "ts_zscore(low, 22)",
    "scale_down(low)",
    "ts_rank(open, 22)",
    "ts_rank(high, 66)",
    "normalize(return_on_equity)",
    "ts_zscore(high, 66)",
    "inverse(low)",
    "ts_rank(volume, 5)",
    "ts_delta(volume, 5)",
    "quantile(return_on_assets)",
    "log(current_ratio)",
    "ts_rank(low, 240)",
    "fraction(earnings_per_share)",
    "ts_ir(high)",
    "log(market_cap)",
    "normalize(book_value_per_share)",
    "zscore(close)",
    "fraction(open)",
    "sqrt(high)",
    "ts_delta(close, 5)",
    "rank(high)",
    "s_log_1p(revenue_per_share)",
    "fraction(ev_to_ebitda)",
    "rank(book_value_per_share)",
    "reverse(low)",
    "normalize(high)",
    "quantile(book_value_per_share)",
    "reverse(current_ratio)",
    "rank(ev_to_ebitda)",
    "sqrt(revenue_per_share)",
    "ts_zscore(open, 66)",
    "ts_zscore(volume, 240)",
    "ts_mean(close, 66)",
    "ts_delta(high, 22)",
    "log(volume)",
    "ts_delta(low, 240)"
  ],
  "stats": {
    "total_generated": 381,
    "total_backtested": 310,
    "total_failed": 71,
    "generation_time": 0.003909111022949219,
    "backtest_time": 162.26260209083557,
    "start_time": 