{
  "timestamp": "2025-08-04T20:41:40.264080",
  "config": {
    "dataset": "analyst4",
    "region": "USA",
    "universe": "TOP3000",
    "max_step": 3,
    "max_alphas_per_step": 1000,
    "time_windows": [
      5,
      22,
      66,
      120,
      240
    ],
    "concurrent_backtests": 3,
    "enable_quality_check": true,
    "min_quality_score": 0.3,
    "enable_checkpoint": true,
    "checkpoint_interval": 100,
    "max_expression_length": 500,
    "enable_deduplication": true
  },
  "completed_alphas": [
    "ts_delta(high, 5)",
    "ts_delta(low, 240)",
    "fraction(earnings_per_share)",
    "s_log_1p(earnings_per_share)",
    "scale_down(high)",
    "price_to_book",
    "zscore(vwap)",
    "quantile(ev_to_ebitda)",
    "rank(price_to_book)",
    "zscore(return_on_assets)",
    "normalize(revenue_per_share)",
    "rank(cash_ratio)",
    "ts_zscore(low, 240)",
    "cash_ratio",
    "ts_min_diff(volume)",
    "ts_mean(close, 120)",
    "scale_down(volume)",
    "rank(close)",
    "ts_zscore(volume, 240)",
    "scale_down(return_on_equity)",
    "zscore(price_earnings_ratio)",
    "ts_mean(low, 240)",
    "ts_mean(open, 5)",
    "log(cash_ratio)",
    "sqrt(return_on_equity)",
    "ts_mean(close, 66)",
    "sqrt(current_ratio)",
    "inverse(revenue_per_share)",
    "ts_rank(volume, 240)",
    "rank(ev_to_ebitda)",
    "close",
    "normalize(high)",
    "rank(book_value_per_share)",
    "ts_delta(volume, 5)",
    "reverse(close)",
    "inverse(price_earnings_ratio)",
    "scale_down(ev_to_ebitda)",
    "scale_down(return_on_assets)",
    "fraction(open)",
    "s_log_1p(ev_to_ebitda)",
    "reverse(ev_to_ebitda)",
    "reverse(return_on_assets)",
    "ts_mean(open, 120)",
    "ts_scale(open)",
    "log_diff(price_to_book)",
    "inverse(price_to_book)",
    "inverse(return_on_assets)",
    "ts_delta(low, 5)",
    "zscore(high)",
    "rank(high)",
    "return_on_equity",
    "ts_zscore(close, 5)",
    "rank(revenue_per_share)",
    "sqrt(revenue_per_share)",
    "ts_rank(low, 120)",
    "ts_zscore(volume, 66)",
    "ev_to_ebitda",
    "ts_rank(open, 66)",
    "ts_arg_min(low)",
    "sqrt(open)",
    "fraction(price_to_book)",
    "log(high)",
    "ts_product(high)",
    "revenue_per_share",
    "reverse(revenue_per_share)",
    "normalize(cash_ratio)",
    "sqrt(price_earnings_ratio)",
    "reverse(vwap)",
    "log_diff(vwap)",
    "scale_down(cash_ratio)",
    "ts_rank(close, 66)",
    "ts_sum(close)",
    "inverse(high)",
    "ts_min_diff(low)",
    "log_diff(return_on_assets)",
    "zscore(market_cap)",
    "ts_rank(volume, 5)",
    "ts_min_diff(high)",
    "ts_delta(low, 66)",
    "ts_mean(volume, 240)",
    "ts_max_diff(open)",
    "ts_zscore(low, 5)",
    "ts_rank(volume, 22)",
    "zscore(enterprise_value)",
    "ts_product(close)",
    "scale_down(close)",
    "s_log_1p(market_cap)",
    "reverse(return_on_equity)",
    "quantile(vwap)",
    "ts_arg_max(volume)",
    "rank(volume)",
    "scale_down(low)",
    "ts_delta(high, 240)",
    "quantile(revenue_per_share)",
    "log(open)",
    "ts_mean(low, 5)",
    "inverse(current_ratio)",
    "ts_zscore(low, 120)",
    "ts_arg_min(volume)",
    "s_log_1p(return_on_assets)",
    "ts_mean(high, 240)",
    "log(book_value_per_share)",
    "ts_zscore(volume, 5)",
    "quantile(book_value_per_share)",
    "zscore(volume)",
    "ts_scale(high)",
    "rank(market_cap)",
    "return_on_assets",
    "ts_rank(open, 5)",
    "s_log_1p(high)",
    "fraction(low)",
    "low",
    "zscore(cash_ratio)",
    "log_diff(market_cap)",
    "normalize(return_on_equity)",
    "ts_rank(open, 240)",
    "ts_mean(volume, 120)",
    "ts_zscore(volume, 22)",
    "rank(return_on_assets)",
    "log_diff(enterprise_value)",
    "fraction(vwap)",
    "ts_delta(open, 66)",
    "log_diff(cash_ratio)",
    "ts_mean(open, 66)",
    "reverse(market_cap)",
    "ts_ir(volume)",
    "fraction(close)",
    "ts_returns(high)",
    "fraction(ev_to_ebitda)",
    "ts_mean(close, 240)",
    "fraction(current_ratio)",
    "ts_product(low)",
    "ts_rank(high, 66)",
    "ts_rank(low, 66)",
    "ts_product(volume)",
    "ts_max_diff(high)",
    "market_cap",
    "ts_zscore(open, 120)",
    "normalize(return_on_assets)",
    "sqrt(book_value_per_share)",
    "ts_ir(open)",
    "ts_zscore(high, 120)",
    "scale_down(vwap)",
    "fraction(enterprise_value)",
    "ts_arg_min(high)",
    "inverse(book_value_per_share)",
    "ts_delta(volume, 22)",
    "normalize(price_earnings_ratio)",
    "book_value_per_share",
    "normalize(earnings_per_share)",
    "ts_delta(low, 120)",
    "ts_returns(low)",
    "ts_zscore(close, 240)",
    "ts_zscore(volume, 120)",
    "ts_mean(close, 5)",
    "fraction(return_on_equity)",
    "normalize(low)",
    "normalize(price_to_book)",
    "ts_rank(close, 5)",
    "ts_zscore(close, 120)",
    "ts_max_diff(close)",
    "s_log_1p(book_value_per_share)",
    "ts_delta(open, 120)",
    "zscore(current_ratio)",
    "log(return_on_equity)",
    "ts_std_dev(high)",
    "scale_down(current_ratio)",
    "reverse(price_to_book)",
    "log(close)",
    "log(vwap)",
    "normalize(market_cap)",
    "scale_down(enterprise_value)",
    "quantile(return_on_equity)",
    "sqrt(earnings_per_share)",
    "ts_mean(high, 66)",
    "ts_zscore(low, 66)",
    "zscore(earnings_per_share)",
    "ts_std_dev(open)",
    "ts_mean(volume, 66)",
    "ts_ir(close)",
    "reverse(enterprise_value)",
    "volume",
    "zscore(open)",
    "fraction(high)",
    "log_diff(earnings_per_share)",
    "ts_rank(low, 22)",
    "ts_rank(close, 120)",
    "earnings_per_share",
    "s_log_1p(low)",
    "ts_mean(open, 240)",
    "log_diff(price_earnings_ratio)",
    "ts_rank(volume, 66)",
    "ts_rank(volume, 120)",
    "zscore(price_to_book)",
    "reverse(high)",
    "enterprise_value",
    "reverse(volume)",
    "log_diff(book_value_per_share)",
    "quantile(earnings_per_share)",
    "ts_product(open)",
    "ts_delta(low, 22)",
    "zscore(ev_to_ebitda)",
    "quantile(price_to_book)",
    "s_log_1p(close)",
    "log(volume)",
    "fraction(market_cap)",
    "ts_zscore(high, 22)",
    "ts_zscore(high, 66)",
    "fraction(return_on_assets)",
    "ts_ir(high)",
    "log_diff(current_ratio)",
    "ts_delta(close, 66)",
    "ts_rank(open, 120)",
    "ts_zscore(close, 66)",
    "fraction(price_earnings_ratio)",
    "ts_rank(high, 240)",
    "ts_std_dev(low)",
    "log_diff(close)",
    "log_diff(ev_to_ebitda)",
    "rank(price_earnings_ratio)",
    "ts_rank(close, 22)",
    "s_log_1p(open)",
    "scale_down(price_to_book)",
    "inverse(close)",
    "ts_sum(low)",
    "ts_zscore(open, 22)",
    "s_log_1p(price_to_book)",
    "scale_down(price_earnings_ratio)",
    "rank(vwap)",
    "ts_mean(volume, 22)",
    "ts_delta(volume, 66)",
    "s_log_1p(current_ratio)",
    "scale_down(market_cap)",
    "ts_zscore(open, 5)",
    "ts_rank(high, 5)",
    "log(market_cap)",
    "zscore(revenue_per_share)",
    "inverse(volume)",
    "inverse(vwap)",
    "vwap",
    "normalize(book_value_per_share)",
    "s_log_1p(enterprise_value)",
    "quantile(low)",
    "normalize(enterprise_value)",
    "inverse(cash_ratio)",
    "ts_delta(volume, 240)",
    "ts_zscore(low, 22)",
    "ts_rank(low, 240)",
    "sqrt(return_on_assets)",
    "open",
    "log(current_ratio)",
    "ts_rank(low, 5)",
    "high",
    "ts_delta(close, 120)",
    "ts_delta(high, 120)",
    "ts_min_diff(open)",
    "zscore(close)",
    "sqrt(vwap)",
    "ts_mean(high, 22)",
    "zscore(book_value_per_share)",
    "sqrt(enterprise_value)",
    "scale_down(book_value_per_share)",
    "fraction(revenue_per_share)",
    "scale_down(revenue_per_share)",
    "ts_delta(volume, 120)",
    "scale_down(open)",
    "rank(low)",
    "quantile(market_cap)",
    "quantile(close)",
    "fraction(volume)",
    "ts_scale(low)",
    "ts_delta(close, 22)",
    "ts_std_dev(close)",
    "ts_mean(close, 22)",
    "log_diff(volume)",
    "reverse(price_earnings_ratio)",
    "log_diff(high)",
    "ts_zscore(high, 5)",
    "sqrt(close)",
    "s_log_1p(price_earnings_ratio)",
    "normalize(current_ratio)",
    "log_diff(revenue_per_share)",
    "current_ratio",
    "ts_delta(high, 66)",
    "ts_arg_max(low)",
    "fraction(cash_ratio)",
    "ts_ir(low)",
    "ts_mean(low, 22)",
    "rank(current_ratio)",
    "ts_std_dev(volume)",
    "log(price_to_book)",
    "log(enterprise_value)"
  ],
  "failed_alphas": [
    "normalize(volume)",
    "reverse(cash_ratio)",
    "ts_sum(open)",
    "rank(enterprise_value)",
    "ts_arg_max(close)",
    "inverse(low)",
    "zscore(low)",
    "zscore(return_on_equity)",
    "sqrt(market_cap)",
    "normalize(close)",
    "reverse(current_ratio)",
    "quantile(cash_ratio)",
    "sqrt(cash_ratio)",
    "ts_scale(close)",
    "rank(return_on_equity)",
    "normalize(open)",
    "inverse(return_on_equity)",
    "reverse(open)",
    "fraction(book_value_per_share)",
    "ts_rank(high, 120)",
    "ts_returns(open)",
    "inverse(open)",
    "ts_mean(high, 5)",
    "inverse(enterprise_value)",
    "ts_delta(open, 240)",
    "ts_arg_max(high)",
    "ts_rank(close, 240)",
    "inverse(market_cap)",
    "log_diff(open)",
    "sqrt(ev_to_ebitda)",
    "inverse(ev_to_ebitda)",
    "quantile(open)",
    "ts_arg_max(open)",
    "quantile(price_earnings_ratio)",
    "ts_zscore(high, 240)",
    "ts_zscore(open, 66)",
    "sqrt(low)",
    "ts_mean(low, 66)",
    "quantile(volume)",
    "s_log_1p(cash_ratio)",
    "sqrt(price_to_book)",
    "rank(earnings_per_share)",
    "log_diff(low)",
    "s_log_1p(vwap)",
    "ts_arg_min(open)",
    "ts_delta(open, 22)",
    "scale_down(earnings_per_share)",
    "ts_returns(close)",
    "reverse(earnings_per_share)",
    "log(price_earnings_ratio)",
    "ts_zscore(close, 22)",
    "s_log_1p(return_on_equity)",
    "sqrt(high)",
    "normalize(ev_to_ebitda)",
    "s_log_1p(volume)",
    "ts_sum(high)",
    "quantile(high)",
    "log(revenue_per_share)",
    "ts_delta(high, 22)",
    "quantile(current_ratio)",
    "ts_mean(low, 120)",
    "rank(open)",
    "quantile(enterprise_value)",
    "log(return_on_assets)",
    "log(low)",
    "sqrt(volume)",
    "normalize(vwap)",
    "inverse(earnings_per_share)",
    "s_log_1p(revenue_per_share)",
    "price_earnings_ratio",
    "ts_delta(close, 5)",
    "ts_min_diff(close)",
    "ts_arg_min(close)",
    "ts_mean(volume, 5)",
    "log_diff(return_on_equity)",
    "ts_sum(volume)",
    "ts_mean(open, 22)",
    "ts_max_diff(low)",
    "log(earnings_per_share)",
    "reverse(book_value_per_share)",
    "ts_delta(open, 5)",
    "reverse(low)",
    "ts_rank(high, 22)",
    "ts_zscore(open, 240)",
    "ts_mean(high, 120)",
    "ts_rank(open, 22)",
    "log(ev_to_ebitda)",
    "quantile(return_on_assets)",
    "ts_delta(close, 240)"
  ],
  "stats": {
    "total_generated": 381,
    "total_backtested": 292,
    "total_failed": 89,
    "generation_time": 0.0037670135498046875,
    "backtest_time": 113.37856698036194,
    "start_time": 