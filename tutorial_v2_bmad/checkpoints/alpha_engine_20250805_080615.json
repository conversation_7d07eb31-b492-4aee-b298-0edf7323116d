{
  "timestamp": "2025-08-05T08:06:16.506951",
  "config": {
    "dataset": "analyst4",
    "region": "USA",
    "universe": "TOP3000",
    "max_step": 3,
    "max_alphas_per_step": 1000,
    "time_windows": [
      5,
      22,
      66,
      120,
      240
    ],
    "concurrent_backtests": 3,
    "enable_quality_check": true,
    "min_quality_score": 0.3,
    "enable_checkpoint": true,
    "checkpoint_interval": 100,
    "max_expression_length": 500,
    "enable_deduplication": true
  },
  "completed_alphas": [],
  "failed_alphas": [
    "rank(price_to_book)",
    "s_log_1p(return_on_assets)",
    "cash_ratio",
    "ts_zscore(open, 240)",
    "ts_product(high)",
    "ts_delta(volume, 66)",
    "ts_mean(close, 120)",
    "volume",
    "log_diff(revenue_per_share)",
    "log(vwap)",
    "reverse(high)",
    "reverse(enterprise_value)",
    "fraction(cash_ratio)",
    "ts_rank(close, 120)",
    "fraction(high)",
    "log_diff(low)",
    "ts_delta(open, 120)",
    "log(revenue_per_share)",
    "normalize(high)",
    "log(book_value_per_share)",
    "s_log_1p(enterprise_value)",
    "ts_ir(close)",
    "quantile(low)",
    "ts_returns(high)",
    "price_to_book",
    "s_log_1p(current_ratio)",
    "zscore(earnings_per_share)",
    "current_ratio",
    "ts_rank(open, 120)",
    "s_log_1p(ev_to_ebitda)",
    "fraction(price_earnings_ratio)",
    "ts_delta(volume, 240)",
    "sqrt(ev_to_ebitda)",
    "sqrt(open)",
    "fraction(return_on_equity)",
    "s_log_1p(cash_ratio)",
    "ts_zscore(close, 5)",
    "ts_zscore(high, 22)",
    "inverse(low)",
    "ts_delta(low, 66)",
    "quantile(price_to_book)",
    "ts_zscore(open, 66)",
    "log_diff(volume)",
    "close",
    "fraction(revenue_per_share)",
    "ts_zscore(open, 5)",
    "sqrt(revenue_per_share)",
    "normalize(open)",
    "reverse(return_on_equity)",
    "log_diff(current_ratio)",
    "ts_rank(high, 66)",
    "ts_zscore(volume, 66)",
    "scale_down(book_value_per_share)",
    "revenue_per_share",
    "enterprise_value",
    "ts_min_diff(high)",
    "ts_returns(low)",
    "zscore(price_earnings_ratio)",
    "log_diff(return_on_assets)",
    "ts_rank(close, 240)",
    "reverse(book_value_per_share)",
    "ts_sum(high)",
    "s_log_1p(vwap)",
    "ts_zscore(low, 66)",
    "ts_ir(volume)",
    "rank(book_value_per_share)",
    "scale_down(price_to_book)",
    "ts_arg_max(close)",
    "reverse(market_cap)",
    "scale_down(cash_ratio)",
    "ts_rank(volume, 22)",
    "ts_rank(close, 5)",
    "inverse(cash_ratio)",
    "normalize(current_ratio)",
    "ts_max_diff(open)",
    "normalize(low)",
    "scale_down(open)",
    "normalize(cash_ratio)",
    "sqrt(cash_ratio)",
    "sqrt(return_on_assets)",
    "zscore(low)",
    "ts_mean(open, 120)",
    "s_log_1p(close)",
    "s_log_1p(low)",
    "fraction(enterprise_value)",
    "rank(low)",
    "ts_rank(close, 22)",
    "reverse(low)",
    "ts_returns(open)",
    "sqrt(market_cap)",
    "ts_rank(high, 22)",
    "ts_mean(high, 5)",
    "ts_max_diff(low)",
    "inverse(price_earnings_ratio)",
    "ts_mean(close, 22)",
    "ts_zscore(open, 120)",
    "log_diff(price_to_book)",
    "inverse(return_on_assets)",
    "reverse(price_to_book)",
    "inverse(open)",
    "ts_ir(high)",
    "ts_rank(volume, 240)",
    "ts_sum(low)",
    "scale_down(return_on_assets)",
    "ts_std_dev(low)",
    "rank(current_ratio)",
    "ts_returns(close)",
    "ts_arg_min(low)",
    "reverse(vwap)",
    "ts_max_diff(close)",
    "ts_arg_min(volume)",
    "ts_arg_max(volume)",
    "ts_mean(high, 240)",
    "scale_down(close)",
    "normalize(market_cap)",
    "fraction(market_cap)",
    "ts_delta(close, 120)",
    "ts_product(volume)",
    "sqrt(earnings_per_share)",
    "ts_delta(low, 240)",
    "ev_to_ebitda",
    "book_value_per_share",
    "ts_min_diff(close)",
    "ts_zscore(low, 5)",
    "scale_down(revenue_per_share)",
    "ts_ir(low)",
    "reverse(current_ratio)",
    "open",
    "quantile(return_on_assets)",
    "inverse(volume)",
    "ts_arg_max(high)",
    "ts_arg_max(low)",
    "sqrt(low)",
    "scale_down(market_cap)",
    "reverse(open)",
    "zscore(market_cap)",
    "s_log_1p(market_cap)",
    "ts_zscore(low, 22)",
    "normalize(return_on_assets)",
    "ts_mean(volume, 240)",
    "ts_rank(high, 240)",
    "ts_mean(volume, 120)",
    "rank(high)",
    "sqrt(return_on_equity)",
    "rank(return_on_equity)",
    "quantile(revenue_per_share)",
    "sqrt(enterprise_value)",
    "earnings_per_share",
    "ts_mean(low, 5)",
    "log_diff(close)",
    "s_log_1p(price_to_book)",
    "fraction(low)",
    "ts_std_dev(open)",
    "ts_delta(low, 5)",
    "ts_delta(open, 5)",
    "zscore(volume)",
    "ts_mean(high, 120)",
    "scale_down(ev_to_ebitda)",
    "ts_product(low)",
    "inverse(market_cap)",
    "reverse(return_on_assets)",
    "sqrt(current_ratio)",
    "ts_delta(volume, 120)",
    "log(high)",
    "log(price_to_book)",
    "ts_rank(high, 5)",
    "normalize(ev_to_ebitda)",
    "ts_delta(low, 22)",
    "fraction(book_value_per_share)",
    "rank(revenue_per_share)",
    "inverse(current_ratio)",
    "zscore(enterprise_value)",
    "zscore(cash_ratio)",
    "sqrt(price_earnings_ratio)",
    "ts_rank(low, 22)",
    "sqrt(book_value_per_share)",
    "normalize(volume)",
    "rank(return_on_assets)",
    "ts_std_dev(volume)",
    "ts_sum(volume)",
    "zscore(book_value_per_share)",
    "ts_rank(low, 240)",
    "ts_delta(close, 22)",
    "ts_rank(open, 22)",
    "quantile(book_value_per_share)",
    "ts_scale(open)",
    "log_diff(vwap)",
    "quantile(ev_to_ebitda)",
    "ts_delta(high, 240)",
    "log(enterprise_value)",
    "ts_delta(high, 120)",
    "ts_delta(open, 240)",
    "inverse(book_value_per_share)",
    "log(return_on_equity)",
    "reverse(close)",
    "ts_rank(volume, 66)",
    "fraction(ev_to_ebitda)",
    "ts_zscore(close, 240)",
    "rank(price_earnings_ratio)",
    "ts_delta(high, 22)",
    "log_diff(earnings_per_share)",
    "ts_zscore(low, 120)",
    "ts_mean(open, 66)",
    "zscore(return_on_assets)",
    "reverse(cash_ratio)",
    "ts_min_diff(open)",
    "fraction(close)",
    "ts_zscore(volume, 120)",
    "ts_rank(low, 120)",
    "ts_delta(high, 66)",
    "ts_mean(close, 5)",
    "ts_scale(close)",
    "ts_mean(high, 22)",
    "log_diff(high)",
    "reverse(revenue_per_share)",
    "reverse(price_earnings_ratio)",
    "log(volume)",
    "quantile(price_earnings_ratio)",
    "quantile(enterprise_value)",
    "log_diff(price_earnings_ratio)",
    "ts_delta(low, 120)",
    "ts_rank(low, 5)",
    "scale_down(current_ratio)",
    "ts_min_diff(volume)",
    "log(earnings_per_share)",
    "quantile(return_on_equity)",
    "ts_zscore(volume, 5)",
    "inverse(return_on_equity)",
    "normalize(earnings_per_share)",
    "ts_zscore(volume, 240)",
    "log_diff(return_on_equity)",
    "log_diff(cash_ratio)",
    "scale_down(vwap)",
    "zscore(revenue_per_share)",
    "log(price_earnings_ratio)",
    "ts_mean(open, 5)",
    "fraction(vwap)",
    "return_on_assets",
    "return_on_equity",
    "fraction(volume)",
    "ts_zscore(close, 22)",
    "ts_delta(high, 5)",
    "ts_ir(open)",
    "quantile(open)",
    "quantile(high)",
    "scale_down(price_earnings_ratio)",
    "log_diff(open)",
    "ts_mean(volume, 5)",
    "rank(ev_to_ebitda)",
    "zscore(price_to_book)",
    "scale_down(volume)",
    "log(close)",
    "rank(earnings_per_share)",
    "inverse(vwap)",
    "log(open)",
    "quantile(close)",
    "quantile(current_ratio)",
    "ts_mean(high, 66)",
    "s_log_1p(earnings_per_share)",
    "rank(open)",
    "ts_rank(volume, 5)",
    "log(cash_ratio)",
    "quantile(market_cap)",
    "sqrt(close)",
    "zscore(return_on_equity)",
    "scale_down(enterprise_value)",
    "ts_delta(close, 5)",
    "ts_min_diff(low)",
    "normalize(vwap)",
    "ts_std_dev(close)",
    "ts_rank(low, 66)",
    "s_log_1p(open)",
    "s_log_1p(high)",
    "ts_arg_min(high)",
    "log_diff(market_cap)",
    "quantile(volume)",
    "sqrt(high)",
    "zscore(current_ratio)",
    "log_diff(enterprise_value)",
    "normalize(price_earnings_ratio)",
    "zscore(open)",
    "ts_rank(open, 5)",
    "ts_product(open)",
    "ts_rank(close, 66)",
    "ts_mean(volume, 66)",
    "scale_down(high)",
    "zscore(close)",
    "ts_delta(volume, 5)",
    "inverse(price_to_book)",
    "inverse(ev_to_ebitda)",
    "fraction(price_to_book)",
    "ts_sum(open)",
    "ts_arg_max(open)",
    "log(low)",
    "ts_scale(low)",
    "rank(volume)",
    "log_diff(ev_to_ebitda)",
    "ts_mean(open, 22)",
    "s_log_1p(volume)",
    "ts_zscore(high, 120)",
    "ts_max_diff(high)",
    "ts_std_dev(high)",
    "quantile(vwap)",
    "ts_mean(close, 66)",
    "zscore(high)",
    "ts_rank(open, 66)",
    "rank(cash_ratio)",
    "normalize(book_value_per_share)",
    "s_log_1p(price_earnings_ratio)",
    "market_cap",
    "sqrt(price_to_book)",
    "ts_scale(high)",
    "ts_mean(volume, 22)",
    "normalize(close)",
    "log(ev_to_ebitda)",
    "ts_zscore(high, 5)",
    "log(return_on_assets)",
    "ts_arg_min(close)",
    "inverse(earnings_per_share)",
    "ts_mean(low, 66)",
    "quantile(cash_ratio)",
    "s_log_1p(revenue_per_share)",
    "sqrt(vwap)",
    "reverse(volume)",
    "quantile(earnings_per_share)",
    "ts_zscore(low, 240)",
    "fraction(current_ratio)",
    "rank(close)",
    "zscore(ev_to_ebitda)",
    "inverse(revenue_per_share)",
    "ts_zscore(high, 66)",
    "price_earnings_ratio",
    "ts_delta(close, 66)",
    "reverse(ev_to_ebitda)",
    "fraction(open)",
    "sqrt(volume)",
    "ts_delta(close, 240)",
    "scale_down(return_on_equity)",
    "ts_product(close)",
    "log(current_ratio)",
    "ts_mean(low, 240)",
    "inverse(close)",
    "inverse(enterprise_value)",
    "ts_mean(open, 240)",
    "log(market_cap)",
    "normalize(enterprise_value)",
    "rank(enterprise_value)",
    "log_diff(book_value_per_share)",
    "vwap",
    "ts_zscore(open, 22)",
    "ts_delta(volume, 22)",
    "scale_down(earnings_per_share)",
    "ts_mean(low, 22)",
    "ts_zscore(close, 120)",
    "inverse(high)",
    "ts_arg_min(open)",
    "rank(vwap)",
    "high",
    "normalize(price_to_book)",
    "ts_mean(low, 120)",
    "s_log_1p(return_on_equity)",
    "s_log_1p(book_value_per_share)",
    "scale_down(low)",
    "ts_rank(high, 120)",
    "ts_delta(open, 66)",
    "ts_rank(volume, 120)",
    "ts_rank(open, 240)",
    "ts_zscore(high, 240)",
    "ts_zscore(close, 66)",
    "reverse(earnings_per_share)",
    "ts_delta(open, 22)",
    "low",
    "zscore(vwap)",
    "ts_zscore(volume, 22)",
    "normalize(return_on_equity)",
    "ts_sum(close)",
    "fraction(return_on_assets)",
    "rank(market_cap)",
    "ts_mean(close, 240)",
    "normalize(revenue_per_share)",
    "fraction(earnings_per_share)"
  ],
  "stats": {
    "total_generated": 381,
    "total_backtested": 0,
    "total_failed": 381,
    "generation_time": 0.0037870407104492188,
    "backtest_time": 0.5344538688659668,
    "start_time": 