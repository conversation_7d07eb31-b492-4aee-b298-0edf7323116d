"""
WQ因子挖掘系统主应用入口

这是系统的主要入口点，提供CLI界面和工作流编排功能。
"""
import asyncio
import json
import os
import sys
from typing import Optional, Dict, Any

from rich.console import Console
from rich.panel import Panel
from rich.prompt import Prompt
from rich.text import Text
from rich import box

from .cli.console_ui import ConsoleUI
from .cli.menu_system import InteractiveMenu
from .cli.error_handler import ErrorHandler
from .cli.progress_tracker import ProgressTracker
from .lib.session_manager import SessionManager
from .lib.exceptions import AuthenticationError, InvalidCredentialsError
from .core import AlphaEngine, AlphaConfig


class WQFactorMiningApp:
    """WQ因子挖掘系统主应用"""
    
    def __init__(self):
        self.console = Console()
        self.ui = ConsoleUI()
        self.menu = InteractiveMenu(self.console)
        self.error_handler = ErrorHandler(self.console)
        self.progress_tracker = ProgressTracker(self.console)
        
        # 核心模块将在后续故事中集成
        self.data_manager = None
        self.session_manager = None
        self.factor_engine = None
        self.factor_validator = None
        self.submit_manager = None
        
        # 当前会话状态
        self.current_session = None
        
        self.config = {}
        
    async def initialize(self) -> bool:
        """初始化系统"""
        try:
            # 先进行基本环境检查（不依赖配置）
            await self.basic_environment_check()
            # 加载配置
            await self.load_configuration()
            # 再进行完整环境检查（依赖配置）
            await self.check_environment()
            # 初始化模块
            await self.initialize_modules()
            return True
        except Exception as e:
            self.error_handler.handle_general_error(e)
            return False
            
    async def basic_environment_check(self) -> bool:
        """基本环境检查（不依赖配置文件）"""
        self.ui.show_info("🔍 进行基本环境检查...")
        
        # 创建必要目录
        for directory in ["data", "logs", "src/config"]:
            os.makedirs(directory, exist_ok=True)
            
        # 检查虚拟环境
        if not os.path.exists(".venv") and not hasattr(sys, 'base_prefix'):
            self.ui.show_warning("⚠️ 建议使用虚拟环境运行")
            
        return True
            
    async def check_environment(self) -> bool:
        """检查运行环境（基于配置文件）"""
        self.ui.show_info("🔍 检查Python版本要求...")
        
        # 从配置文件读取最低Python版本要求
        system_config = self.config.get("system", {})
        min_version = system_config.get("min_python_version")
        
        if min_version is None:
            raise ValueError("配置文件中缺少system.min_python_version配置")
            
        min_version_tuple = tuple(min_version)
        
        if sys.version_info < min_version_tuple:
            version_str = ".".join(map(str, min_version))
            raise RuntimeError(f"需要Python {version_str}+，当前版本: {sys.version}")
            
        self.ui.show_success("✅ 环境检查完成")
        return True
        
    async def load_configuration(self) -> Dict[str, Any]:
        """加载配置文件"""
        from .cli.config_manager import ConfigManager
        
        self.ui.show_info("📋 加载系统配置...")
        
        config_manager = ConfigManager()
        self.config = await config_manager.load_config()
        
        self.ui.show_success("✅ 配置加载完成")
        return self.config
        
    async def initialize_modules(self) -> None:
        """初始化核心模块"""
        self.ui.show_info("🔧 初始化核心模块...")
        
        # TODO: 在其他故事实现后集成实际模块
        # self.data_manager = DataManager(self.config.get("database", {}))
        # self.session_manager = SessionManager(self.config.get("session", {}))
        # 等等...
        
        self.ui.show_success("✅ 模块初始化完成")
        
    async def start_mining_workflow(self, config: Optional[Dict] = None) -> bool:
        """启动因子挖掘工作流"""
        if not config:
            config = await self.menu.show_workflow_options()
            
        self.ui.show_info("🚀 开始因子挖掘工作流...")
        
        try:
            # 步骤1: 真实的WQ平台身份认证
            session = await self._authenticate_wq_platform()
            if not session:
                self.ui.show_error("❌ 身份认证失败，工作流中止")
                return False
                
            # 调用真实的Alpha挖掘引擎
            result = await self._execute_alpha_mining_workflow(config, session)
                    
            self.ui.show_success("🎉 工作流执行完成！")
            
            # 工作流完成后的操作选择
            return await self.handle_workflow_completion()
            
        except Exception as e:
            self.error_handler.handle_general_error(e)
            return False
            
    async def _authenticate_wq_platform(self):
        """WQ平台身份认证"""
        self.ui.show_info("🔐 开始WQ平台身份认证...")
        
        try:
            # 从配置获取会话管理设置
            session_config = self.config.get('session_management', {})
            
            # 创建SessionManager实例（不使用async with，手动管理生命周期）
            session_manager = SessionManager(session_config)
            await session_manager._init_session_pool()
            
            # 使用SessionManager进行认证，这里会自动打印用户信息
            session = await session_manager.authenticate_from_file('user_info.txt')
            
            # 存储session和manager到应用实例中，供其他步骤使用
            self.current_session = session
            self.session_manager = session_manager
            
            self.ui.show_success("✅ WQ平台身份认证成功")
            return session
                
        except InvalidCredentialsError:
            self.ui.show_error("❌ 认证失败: 用户名或密码错误")
            self.ui.show_info("💡 请检查 user_info.txt 文件中的凭证信息")
            return None
        except AuthenticationError as e:
            self.ui.show_error(f"❌ 认证失败: {str(e)}")
            return None
        except FileNotFoundError:
            self.ui.show_error("❌ 未找到 user_info.txt 凭证文件")
            self.ui.show_info("💡 请在项目根目录创建 user_info.txt 文件")
            return None
        except Exception as e:
            self.ui.show_error(f"❌ 认证过程发生异常: {str(e)}")
            return None
            
    async def _execute_alpha_mining_workflow(self, config: Dict[str, Any], session) -> Dict[str, Any]:
        """执行真实的Alpha挖掘工作流"""
        self.ui.show_info("🚀 启动Alpha挖掘引擎...")
        
        try:
            # 创建Alpha配置
            alpha_config = AlphaConfig(
                dataset=config['dataset'],
                region=config['region'],
                universe=config['universe'],
                max_step=config['max_step'],
                max_alphas_per_step=config.get('max_alphas_per_step', 1000)
            )
            
            # 初始化Alpha引擎
            alpha_engine = AlphaEngine(alpha_config, self.session_manager)
            
            # 执行完整的Alpha挖掘流程（包含生成、回测、质量评估、检查点保存）
            self.ui.show_info("🎯 运行完整Alpha挖掘流程...")
            result = await alpha_engine.run_full_pipeline()
            
            # 处理结果统计
            alphas = result['alphas']
            stats = result['stats']
            
            self.ui.show_success(f"🎉 Alpha挖掘完成!")
            self.ui.show_info(f"📊 总计生成: {stats['total_alphas']} 个Alpha")
            self.ui.show_info(f"✨ 高质量Alpha: {stats['quality_alphas']} 个")
            self.ui.show_info(f"📈 成功率: {stats['success_rate']:.1%}")
            self.ui.show_info(f"⏱️ 总耗时: {stats['total_duration']:.1f} 秒")
            
            return result
            
        except Exception as e:
            self.ui.show_error(f"❌ Alpha挖掘工作流执行失败: {str(e)}")
            # 在生产环境中，这里应该记录详细的错误日志
            import traceback
            traceback.print_exc()
            raise
            
    async def handle_workflow_completion(self) -> bool:
        """处理工作流完成后的用户选择"""
        self.console.print()
        
        completion_text = Text()
        completion_text.append("工作流已完成！请选择下一步操作：\n\n", style="bold green")
        
        options = [
            ("1", "🔄 再次运行", "使用相同配置重新开始挖掘"),
            ("2", "⚙️ 修改配置", "返回主菜单调整参数"),
            ("3", "📊 查看报告", "查看系统状态和统计信息"),
            ("4", "❌ 退出系统", "结束程序")
        ]
        
        for num, title, desc in options:
            completion_text.append(f"{num}. ", style="bold white")
            completion_text.append(f"{title}\n", style="cyan")
            completion_text.append(f"   {desc}\n\n", style="dim")
            
        self.console.print(Panel(
            completion_text,
            title="✅ 工作流完成",
            border_style="green",
            box=box.ROUNDED
        ))
        
        choice = Prompt.ask(
            "[bold cyan]请选择下一步操作[/bold cyan]",
            choices=["1", "2", "3", "4"],
            default="2",
            show_choices=False
        )
        
        if choice == "1":
            return False  # 重新运行当前工作流
        elif choice == "2":
            return True   # 返回主菜单
        elif choice == "3":
            await self.show_status()
            return True   # 显示报告后返回主菜单
        elif choice == "4":
            self.ui.show_info("👋 感谢使用WQ因子挖掘系统！")
            return "exit"  # 退出系统
        else:
            return True   # 默认返回主菜单
            
    async def show_status(self) -> None:
        """显示系统状态"""
        self.ui.show_info("📊 获取系统状态...")
        
        # TODO: 实现实际的状态查询
        status_data = {
            "系统状态": "运行正常",
            "数据库连接": "正常", 
            "会话状态": "未登录",
            "因子数量": 0,
            "上次运行": "无记录"
        }
        
        self.ui.show_status_panel(status_data)
        
    async def show_statistics(self) -> None:
        """显示统计信息"""
        self.ui.show_info("📈 生成统计报告...")
        
        # TODO: 实现实际的统计查询
        stats_data = {
            "总因子数": 0,
            "通过检验": 0,
            "成功提交": 0,
            "成功率": "0%"
        }
        
        self.ui.show_statistics_table(stats_data)
        
    async def run(self) -> None:
        """运行主程序"""
        # 显示欢迎界面
        self.ui.show_welcome_banner()
        
        # 初始化系统
        if not await self.initialize():
            return
            
        # 主循环
        while True:
            try:
                choice = await self.menu.show_main_menu()
                
                if choice == "quick_start":
                    # 快速开始 - 使用默认配置
                    config = await self.menu.get_default_config()
                    
                    # 处理工作流循环
                    current_config = config
                    while True:
                        result = await self.start_mining_workflow(current_config)
                        if result is True:  # 返回主菜单
                            break
                        elif result == "exit":  # 退出系统
                            self.ui.show_info("👋 感谢使用WQ因子挖掘系统！")
                            return
                        elif result is False:  # 重新运行相同配置
                            continue  # 继续使用相同配置
                            
                elif choice == "custom_config":
                    # 自定义配置
                    config = await self.menu.show_workflow_options()
                    
                    # 处理工作流循环
                    current_config = config
                    while True:
                        result = await self.start_mining_workflow(current_config)
                        if result is True:  # 返回主菜单
                            break
                        elif result == "exit":  # 退出系统
                            self.ui.show_info("👋 感谢使用WQ因子挖掘系统！")
                            return
                        elif result is False:  # 重新运行相同配置
                            continue  # 继续使用相同配置
                            
                elif choice == "show_status":
                    await self.show_status()
                elif choice == "show_statistics":
                    await self.show_statistics()
                elif choice == "configure":
                    self.ui.show_info("⚙️ 系统设置功能将在后续版本实现")
                elif choice == "exit":
                    self.ui.show_info("👋 感谢使用WQ因子挖掘系统！")
                    break
                else:
                    self.ui.show_error("❌ 无效选择")
                    
            except KeyboardInterrupt:
                self.ui.show_info("\n👋 程序已被用户中断")
                break
            except Exception as e:
                self.error_handler.handle_general_error(e)
                

async def main():
    """主函数 - 一键启动"""
    app = WQFactorMiningApp()
    await app.run()


if __name__ == "__main__":
    asyncio.run(main())