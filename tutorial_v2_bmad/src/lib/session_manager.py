"""
WQ平台会话管理系统

负责管理与WorldQuant平台的认证会话，包括自动登录、会话刷新、权限控制等功能。
基于architecture/02-核心模块设计.md的设计实现。
"""
import asyncio
import json
import time
from dataclasses import dataclass
from typing import Dict, Any, Optional, List, Set
from pathlib import Path

import aiohttp

from .exceptions import (
    SessionManagerError,
    AuthenticationError, 
    SessionExpiredError,
    InvalidCredentialsError,
    TokenRefreshError,
    NetworkError,
    ConfigurationError
)


@dataclass
class Session:
    """WQ平台会话信息"""
    user_id: str
    token_expiry: float
    permissions: List[str]
    session_data: Dict[str, Any]
    start_time: float
    
    @property
    def is_expired(self) -> bool:
        """检查会话是否过期"""
        current_time = time.time()
        # 提前30秒标记为过期，用于自动刷新
        return (current_time + 30) >= self.start_time + self.token_expiry
    
    @property
    def remaining_time(self) -> float:
        """获取剩余有效时间（秒）"""
        current_time = time.time()
        return max(0, self.start_time + self.token_expiry - current_time)


class SessionManager:
    """
    WQ平台会话管理器
    
    功能：
    1. 自动认证和会话管理
    2. 会话自动刷新 
    3. 权限验证
    4. 多账户支持
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化会话管理器
        
        Args:
            config: 会话管理配置
        """
        self.config = config or {}
        self.sessions: Dict[str, Session] = {}  # user_id -> Session
        self.session_pool: Optional[aiohttp.ClientSession] = None
        self._refresh_tasks: Dict[str, asyncio.Task] = {}
        
        # 配置参数
        self.base_url = self.config.get('base_url', 'https://api.worldquantbrain.com')
        self.timeout = self.config.get('timeout', 30)
        self.max_retries = self.config.get('max_retries', 3)
        self.ssl_verify = self.config.get('ssl_verify', False)
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._init_session_pool()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.cleanup()
        
    async def _init_session_pool(self):
        """初始化aiohttp会话池"""
        if self.session_pool is None:
            connector = aiohttp.TCPConnector(
                ssl=self.ssl_verify,
                limit=10,  # 最大连接数
                limit_per_host=5
            )
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session_pool = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
    
    async def authenticate(self, username: str, password: str) -> Session:
        """
        使用用户名密码进行认证
        
        Args:
            username: 用户名/邮箱
            password: 密码
            
        Returns:
            Session: 认证成功的会话对象
            
        Raises:
            AuthenticationError: 认证失败
            NetworkError: 网络连接失败
        """
        if not self.session_pool:
            await self._init_session_pool()
            
        auth_url = f"{self.base_url}/authentication"
        
        # 构建认证请求
        auth = aiohttp.BasicAuth(username, password)
        
        for attempt in range(self.max_retries):
            try:
                async with self.session_pool.post(auth_url, auth=auth) as response:
                    if response.status in [200, 201]:  # WQ平台成功认证返回201
                        data = await response.json()
                        return await self._create_session_from_response(data)
                    elif response.status == 401:
                        raise InvalidCredentialsError(f"认证失败: 用户名或密码错误")
                    elif response.status == 429:
                        # 处理速率限制错误
                        error_text = await response.text()
                        retry_after = response.headers.get('Retry-After')
                        
                        if retry_after:
                            wait_time = int(retry_after)
                        else:
                            # 如果没有Retry-After头，使用递增等待时间
                            wait_time = min(60, 10 + (attempt * 10))  # 10, 20, 30秒，最多60秒
                        
                        if attempt < self.max_retries - 1:
                            print(f"⏳ WQ平台速率限制 (尝试 {attempt + 1}/{self.max_retries}): 等待 {wait_time} 秒后重试...")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise AuthenticationError(f"认证请求失败: 达到最大重试次数，仍然遇到速率限制")
                    else:
                        error_text = await response.text()
                        if attempt < self.max_retries - 1:
                            wait_time = 2 ** attempt  # 指数退避
                            print(f"⚠️ 认证失败 (尝试 {attempt + 1}/{self.max_retries}): {response.status} - 等待 {wait_time} 秒后重试")
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise AuthenticationError(f"认证请求失败: {response.status} - {error_text}")
                        
            except aiohttp.ClientError as e:
                if attempt < self.max_retries - 1:
                    wait_time = 2 ** attempt
                    print(f"🌐 网络错误 (尝试 {attempt + 1}/{self.max_retries}): 等待 {wait_time} 秒后重试")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise NetworkError(f"网络连接失败: {str(e)}")
            except json.JSONDecodeError as e:
                raise AuthenticationError(f"认证响应解析失败: {str(e)}")
        
        raise AuthenticationError(f"认证失败，已重试 {self.max_retries} 次")
    
    async def authenticate_from_file(self, credentials_file: str = "user_info.txt") -> Session:
        """
        从凭证文件读取用户信息并进行认证
        
        Args:
            credentials_file: 凭证文件路径
            
        Returns:
            Session: 认证成功的会话对象
            
        Raises:
            ConfigurationError: 凭证文件格式错误
            AuthenticationError: 认证失败
        """
        try:
            credentials = self._read_credentials_file(credentials_file)
            username = credentials.get('username')
            password = credentials.get('password')
            
            if not username or not password:
                raise ConfigurationError("凭证文件中缺少用户名或密码")
                
            return await self.authenticate(username, password)
            
        except FileNotFoundError:
            raise ConfigurationError(f"凭证文件不存在: {credentials_file}")
        except Exception as e:
            if isinstance(e, (AuthenticationError, NetworkError, ConfigurationError)):
                raise
            raise ConfigurationError(f"读取凭证文件失败: {str(e)}")
    
    def _read_credentials_file(self, file_path: str) -> Dict[str, str]:
        """
        读取凭证文件
        
        支持格式:
        username: '<EMAIL>'
        password: 'your_password'
        
        Args:
            file_path: 凭证文件路径
            
        Returns:
            Dict[str, str]: 包含用户名和密码的字典
        """
        credentials = {}
        
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                line = line.strip()
                if ':' in line and not line.startswith('#'):
                    key, value = line.split(':', 1)
                    key = key.strip()
                    value = value.strip().strip('\'"')  # 移除引号
                    credentials[key] = value
                    
        return credentials
    
    async def _create_session_from_response(self, response_data: Dict[str, Any]) -> Session:
        """
        从认证响应创建会话对象
        
        预期响应格式:
        {
            "user": {"id": "KY19421"},
            "token": {"expiry": 14400.0},
            "permissions": ["BEFORE_AND_AFTER_PERFORMANCE_V2", "CONSULTANT", ...]
        }
        
        Args:
            response_data: WQ平台认证响应数据
            
        Returns:
            Session: 会话对象
        """
        try:
            user_data = response_data.get('user', {})
            token_data = response_data.get('token', {})
            permissions = response_data.get('permissions', [])
            
            user_id = user_data.get('id')
            token_expiry = token_data.get('expiry', 14400.0)  # 默认4小时
            
            if not user_id:
                raise AuthenticationError("认证响应中缺少用户ID")
                
            session = Session(
                user_id=user_id,
                token_expiry=token_expiry,
                permissions=permissions,
                session_data=response_data,
                start_time=time.time()
            )
            
            # 存储会话
            self.sessions[user_id] = session
            
            # 打印认证成功信息
            print("🎉 WQ平台认证成功!")
            print(f"👤 用户ID: {user_id}")
            print(f"⏰ 令牌有效期: {token_expiry} 秒 ({token_expiry/3600:.1f} 小时)")
            print(f"🔑 权限列表: {', '.join(permissions) if permissions else '无特殊权限'}")
            print(f"⏱️ 会话剩余时间: {session.remaining_time:.0f} 秒")
            print()
            
            # 启动自动刷新任务
            await self._start_refresh_task(user_id)
            
            return session
            
        except KeyError as e:
            raise AuthenticationError(f"认证响应格式错误，缺少字段: {str(e)}")
    
    async def refresh_session(self, session: Session) -> Session:
        """
        刷新会话
        
        Args:
            session: 需要刷新的会话
            
        Returns:
            Session: 刷新后的会话对象
            
        Raises:
            TokenRefreshError: 刷新失败
        """
        # TODO: 实现实际的会话刷新逻辑
        # 这里需要根据WQ平台的实际刷新接口实现
        raise NotImplementedError("会话刷新功能待实现")
    
    def is_expired(self, user_id: str) -> bool:
        """
        检查指定用户的会话是否过期
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否过期
        """
        session = self.sessions.get(user_id)
        return session is None or session.is_expired
    
    def has_permission(self, user_id: str, permission: str) -> bool:
        """
        检查用户是否具有指定权限
        
        Args:
            user_id: 用户ID
            permission: 权限名称 (如 'CONSULTANT', 'MULTI_SIMULATION')
            
        Returns:
            bool: 是否具有权限
        """
        session = self.sessions.get(user_id)
        if not session:
            return False
        # 注意：对于测试，我们允许检查未过期的会话权限
        # 在生产环境中，可能需要根据业务需求决定是否检查过期状态
        return permission in session.permissions
    
    def get_session(self, user_id: str) -> Optional[Session]:
        """
        获取指定用户的会话
        
        Args:
            user_id: 用户ID
            
        Returns:
            Optional[Session]: 会话对象，不存在则返回None
        """
        return self.sessions.get(user_id)
    
    def get_any_valid_session(self) -> Optional[Session]:
        """
        获取任意一个有效的会话（用于单用户场景）
        
        Returns:
            Optional[Session]: 第一个找到的有效会话，无有效会话则返回None
        """
        for session in self.sessions.values():
            if not session.is_expired:
                return session
        return None
    
    def get_current_session(self) -> Optional[Session]:
        """
        获取当前会话（单用户场景的便捷方法）
        
        Returns:
            Optional[Session]: 当前会话，如果有多个会话则返回最新的
        """
        if not self.sessions:
            return None
        
        # 如果只有一个会话，直接返回
        if len(self.sessions) == 1:
            return next(iter(self.sessions.values()))
        
        # 如果有多个会话，返回最新的（start_time最大的）
        return max(self.sessions.values(), key=lambda s: s.start_time)
    
    async def get_valid_session(self) -> Optional[Session]:
        """
        获取有效的会话（自动处理过期会话的刷新）
        
        Returns:
            Optional[Session]: 有效的会话对象，如果无法获取有效会话则返回None
        """
        current_session = self.get_current_session()
        if not current_session:
            return None
        
        # 如果会话已过期，尝试刷新
        if current_session.is_expired:
            try:
                refreshed_session = await self.refresh_session(current_session)
                return refreshed_session
            except Exception:
                # 刷新失败，返回None
                return None
        
        return current_session
    
    async def _start_refresh_task(self, user_id: str):
        """
        为指定用户启动会话自动刷新任务
        
        Args:
            user_id: 用户ID
        """
        # 取消已存在的刷新任务
        if user_id in self._refresh_tasks:
            self._refresh_tasks[user_id].cancel()
            
        # 启动新的刷新任务
        task = asyncio.create_task(self._refresh_loop(user_id))
        self._refresh_tasks[user_id] = task
    
    async def _refresh_loop(self, user_id: str):
        """
        会话自动刷新循环
        
        Args:
            user_id: 用户ID
        """
        while user_id in self.sessions:
            session = self.sessions[user_id]
            
            # 计算下次检查时间 (会话过期前30秒)
            remaining_time = session.remaining_time - 30
            
            if remaining_time > 0:
                await asyncio.sleep(remaining_time)
            
            # 检查会话是否仍然存在
            if user_id not in self.sessions:
                break
                
            session = self.sessions[user_id]
            
            # 如果会话即将过期，尝试刷新
            if session.is_expired:
                try:
                    refreshed_session = await self.refresh_session(session)
                    self.sessions[user_id] = refreshed_session
                except Exception as e:
                    # 刷新失败，移除会话
                    self.sessions.pop(user_id, None)
                    break
    
    async def cleanup(self):
        """清理资源"""
        # 取消所有刷新任务
        for task in self._refresh_tasks.values():
            task.cancel()
        self._refresh_tasks.clear()
        
        # 关闭会话池
        if self.session_pool:
            await self.session_pool.close()
            self.session_pool = None
        
        # 清除会话
        self.sessions.clear()
    
    def get_session_status(self) -> Dict[str, Dict[str, Any]]:
        """
        获取所有会话的状态信息
        
        Returns:
            Dict[str, Dict[str, Any]]: 会话状态信息
        """
        status = {}
        
        for user_id, session in self.sessions.items():
            status[user_id] = {
                'user_id': session.user_id,
                'is_expired': session.is_expired,
                'remaining_time': session.remaining_time,
                'permissions': session.permissions,
                'start_time': session.start_time
            }
            
        return status