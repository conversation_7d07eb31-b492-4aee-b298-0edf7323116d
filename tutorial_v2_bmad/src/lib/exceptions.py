"""
WQ因子挖掘系统自定义异常类

定义了会话管理、认证、API调用等相关的异常类型。
"""


class SessionManagerError(Exception):
    """会话管理器基础异常"""
    pass


class AuthenticationError(SessionManagerError):
    """认证失败异常"""
    pass


class SessionExpiredError(SessionManagerError):
    """会话过期异常"""
    pass


class PermissionError(SessionManagerError):
    """权限不足异常"""
    pass


class RateLimitError(SessionManagerError):
    """频率限制异常"""
    
    def __init__(self, message: str, retry_after: float = None):
        super().__init__(message)
        self.retry_after = retry_after


class NetworkError(SessionManagerError):
    """网络连接异常"""
    pass


class InvalidCredentialsError(AuthenticationError):
    """无效凭证异常"""
    pass


class TokenRefreshError(SessionManagerError):
    """令牌刷新失败异常"""
    pass


class ConfigurationError(SessionManagerError):
    """配置错误异常"""
    pass


class APIError(SessionManagerError):
    """API调用异常"""
    
    def __init__(self, message: str, status_code: int = None, response_data: dict = None):
        super().__init__(message)
        self.status_code = status_code
        self.response_data = response_data