"""
WQ平台认证客户端

基于src_xin/machine_lib.py中的async_login函数模式实现，
负责与WorldQuant平台进行异步认证和API通信。
"""
import asyncio
import json
import logging
from typing import Dict, Any, Optional

import aiohttp

from .exceptions import (
    AuthenticationError,
    InvalidCredentialsError,
    NetworkError,
    ConfigurationError
)

logger = logging.getLogger(__name__)


class WQAuthClient:
    """
    WorldQuant平台认证客户端
    
    基于aiohttp实现异步认证和API调用功能。
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化认证客户端
        
        Args:
            config: 客户端配置
        """
        self.config = config or {}
        self.base_url = self.config.get('base_url', 'https://api.worldquantbrain.com')
        self.timeout = self.config.get('timeout', 30)
        self.ssl_verify = self.config.get('ssl_verify', False)
        self.max_retries = self.config.get('max_retries', 3)
        
        self.session: Optional[aiohttp.ClientSession] = None
        
    async def __aenter__(self):
        """异步上下文管理器入口"""
        await self._init_session()
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出"""
        await self.close()
        
    async def _init_session(self):
        """初始化aiohttp会话"""
        if self.session is None:
            connector = aiohttp.TCPConnector(ssl=self.ssl_verify)
            timeout = aiohttp.ClientTimeout(total=self.timeout)
            self.session = aiohttp.ClientSession(
                connector=connector,
                timeout=timeout
            )
    
    async def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """
        异步认证登录
        
        基于src_xin/machine_lib.py的async_login函数模式实现。
        
        Args:
            username: 用户名/邮箱
            password: 密码
            
        Returns:
            Dict[str, Any]: 认证响应数据
            
        Raises:
            AuthenticationError: 认证失败
            NetworkError: 网络连接失败
        """
        if not self.session:
            await self._init_session()
            
        auth_url = f"{self.base_url}/authentication"
        auth = aiohttp.BasicAuth(username, password)
        
        for attempt in range(self.max_retries):
            try:
                async with self.session.post(auth_url, auth=auth) as response:
                    # 检查响应状态
                    if response.status == 200 or response.status == 201:
                        logger.info("WQ平台认证成功!")
                        
                        # 尝试解析JSON响应
                        try:
                            response_data = await response.json()
                            return response_data
                        except json.JSONDecodeError:
                            # 如果没有JSON响应，返回基本信息
                            return {
                                "status": "success",
                                "user": {"id": username},
                                "token": {"expiry": 14400.0},
                                "permissions": []
                            }
                            
                    elif response.status == 401:
                        error_text = await response.text()
                        if "INVALID_CREDENTIALS" in error_text:
                            raise InvalidCredentialsError(
                                "你的账号密码有误，请在【user_info.txt】输入正确的邮箱和密码！\n"
                                "Your username or password is incorrect. Please enter the correct email and password!"
                            )
                        else:
                            raise AuthenticationError(f"认证失败: {error_text}")
                    elif response.status == 429:
                        # 处理速率限制错误
                        error_text = await response.text()
                        retry_after = response.headers.get('Retry-After')
                        
                        if retry_after:
                            wait_time = int(retry_after)
                        else:
                            # 如果没有Retry-After头，使用递增等待时间
                            wait_time = min(60, 10 + (attempt * 10))  # 10, 20, 30秒，最多60秒
                        
                        logger.warning(f"WQ平台速率限制 (尝试 {attempt + 1}/{self.max_retries}): 等待 {wait_time} 秒后重试")
                        await asyncio.sleep(wait_time)
                        continue
                    else:
                        error_text = await response.text()
                        if attempt < self.max_retries - 1:
                            logger.warning(f"认证失败 (尝试 {attempt + 1}/{self.max_retries}): {response.status} - {error_text}")
                            await asyncio.sleep(2 ** attempt)  # 指数退避
                            continue
                        else:
                            raise AuthenticationError(f"认证请求失败: {response.status} - {error_text}")
                            
            except aiohttp.ClientError as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"网络错误 (尝试 {attempt + 1}/{self.max_retries}): {str(e)}")
                    await asyncio.sleep(2 ** attempt)
                    continue
                else:
                    raise NetworkError(f"网络连接失败: {str(e)}")
            except Exception as e:
                if isinstance(e, (AuthenticationError, InvalidCredentialsError)):
                    raise
                raise AuthenticationError(
                    f"认证过程中发生异常: {type(e).__name__} - {str(e)}")
        
        raise AuthenticationError(f"认证失败，已重试 {self.max_retries} 次")
    
    async def authenticate_from_file(self, credentials_file: str = "user_info.txt") -> Dict[str, Any]:
        """
        从凭证文件进行认证
        
        Args:
            credentials_file: 凭证文件路径
            
        Returns:
            Dict[str, Any]: 认证响应数据
            
        Raises:
            ConfigurationError: 凭证文件问题
            AuthenticationError: 认证失败
        """
        try:
            username, password = self._load_credentials_from_file(credentials_file)
            return await self.authenticate(username, password)
        except FileNotFoundError:
            raise ConfigurationError(f"凭证文件不存在: {credentials_file}")
        except Exception as e:
            if isinstance(e, (AuthenticationError, NetworkError, InvalidCredentialsError)):
                raise
            raise ConfigurationError(f"读取凭证文件失败: {str(e)}")
    
    def _load_credentials_from_file(self, txt_file: str = 'user_info.txt') -> tuple[str, str]:
        """
        从txt文件加载用户凭证
        
        基于src_xin/machine_lib.py的load_decrypted_data函数模式。
        
        支持格式:
        username: '<EMAIL>'
        password: 'your_password'
        
        Args:
            txt_file: 凭证文件路径
            
        Returns:
            tuple[str, str]: (用户名, 密码)
            
        Raises:
            ConfigurationError: 文件格式错误
        """
        try:
            with open(txt_file, 'r', encoding='utf-8') as f:
                data = f.read()
                data = data.strip().split('\n')
                
                # 解析每一行
                parsed_data = {}
                for line in data:
                    line = line.strip()
                    if ':' in line and not line.startswith('#'):
                        parts = line.split(': ', 1)  # 限制分割次数为1
                        if len(parts) == 2:
                            key, value = parts
                            parsed_data[key.strip()] = value.strip()
                
                # 提取用户名和密码，移除引号
                username = parsed_data.get('username', '').strip('\'"')
                password = parsed_data.get('password', '').strip('\'"')
                
                if not username or not password:
                    raise ConfigurationError("凭证文件中缺少用户名或密码")
                
                return username, password
                
        except FileNotFoundError:
            raise
        except Exception as e:
            raise ConfigurationError(f"解析凭证文件失败: {str(e)}")
    
    async def get_operators(self) -> list:
        """
        获取可用的操作符列表
        
        Returns:
            list: 操作符列表
            
        Raises:
            NetworkError: 网络请求失败
        """
        if not self.session:
            await self._init_session()
            
        try:
            url = f"{self.base_url}/operators"
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return [op['name'] for op in data] if isinstance(data, list) else []
                else:
                    raise NetworkError(f"获取操作符失败: {response.status}")
                    
        except aiohttp.ClientError as e:
            raise NetworkError(f"网络请求失败: {str(e)}")
    
    async def test_connection(self) -> bool:
        """
        测试与WQ平台的连接
        
        Returns:
            bool: 连接是否正常
        """
        try:
            if not self.session:
                await self._init_session()
                
            url = f"{self.base_url}/operators"
            async with self.session.get(url) as response:
                return response.status in [200, 401]  # 401也表示连接正常，只是未认证
                
        except Exception:
            return False
    
    async def close(self):
        """关闭客户端会话"""
        if self.session:
            await self.session.close()
            self.session = None
    
    def is_closed(self) -> bool:
        """检查客户端是否已关闭"""
        return self.session is None or self.session.closed