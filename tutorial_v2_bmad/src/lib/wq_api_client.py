"""
WorldQuant API客户端 - 统一处理所有WQ平台API调用

提供统一的接口来处理：
- Alpha模拟和回测
- Alpha提交和管理  
- 数据字段查询
- 操作符获取
- 认证和会话管理
- 重试和错误处理
"""

import asyncio
import json
import time
from typing import Dict, Any, Optional, List
from datetime import datetime
from dataclasses import dataclass

import aiohttp

from .session_manager import SessionManager, Session
from .exceptions import (
    NetworkError, 
    AuthenticationError,
    APIError,
    RateLimitError
)
from .logger import get_logger


@dataclass
class APIResponse:
    """HTTP响应包装类 - 避免连接过早关闭问题"""
    status: int
    headers: Dict[str, str]
    data: Optional[Dict[str, Any]] = None
    text: Optional[str] = None
    
    @classmethod
    async def from_aiohttp_response(cls, response: aiohttp.ClientResponse) -> 'APIResponse':
        """从aiohttp响应创建包装对象"""
        # 在连接关闭前读取所有数据
        headers = dict(response.headers)
        
        try:
            # 尝试解析JSON
            data = await response.json()
            text = None
        except (aiohttp.ContentTypeError, json.JSONDecodeError):
            # 如果不是JSON，读取文本
            text = await response.text()
            data = None
        except Exception:
            # 读取失败，设为空
            data = None
            text = None
            
        return cls(
            status=response.status,
            headers=headers,
            data=data,
            text=text
        )
    
    async def json(self) -> Dict[str, Any]:
        """获取JSON数据"""
        if self.data is not None:
            return self.data
        elif self.text:
            import json
            return json.loads(self.text)
        else:
            return {}


@dataclass  
class SimulationRequest:
    """Alpha模拟请求"""
    alpha_expression: str
    region: str = "USA"
    universe: str = "TOP3000" 
    delay: int = 1
    decay: int = 0
    neutralization: str = "SUBUNIV"
    truncation: float = 0.08
    pasteurization: str = "ON"
    unit_handling: str = "VERIFY"
    nan_handling: str = "ON"
    language: str = "FASTEXPR"
    visualization: bool = False


@dataclass
class SimulationResult:
    """Alpha模拟结果"""
    alpha_id: Optional[str] = None
    status: str = "pending"
    annual_return: Optional[float] = None
    sharpe_ratio: Optional[float] = None
    max_drawdown: Optional[float] = None
    volatility: Optional[float] = None
    total_return: Optional[float] = None
    information_ratio: Optional[float] = None
    error_message: Optional[str] = None
    duration_seconds: float = 0.0
    
    def is_successful(self) -> bool:
        """检查模拟是否成功"""
        return self.status == "completed" and self.alpha_id is not None


class WorldQuantAPIClient:
    """WorldQuant平台API客户端"""
    
    def __init__(self, session_manager: SessionManager):
        """
        初始化API客户端
        
        Args:
            session_manager: 会话管理器实例
        """
        self.session_manager = session_manager
        self.logger = get_logger(__name__)
        
        # API端点配置
        self.endpoints = {
            'simulations': '/simulations',
            'alphas': '/alphas',
            'alphas_submit': '/alphas/{alpha_id}/submit',
            'alphas_info': '/alphas/{alpha_id}',
            'correlations': '/alphas/{alpha_id}/correlations',
            'operators': '/operators',
            'data_sets': '/data-sets',
            'data_fields': '/data-fields',
            'users_alphas': '/users/self/alphas'
        }
        
        # 统计信息
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'api_errors': 0,
            'rate_limits': 0
        }

    async def simulate_alpha(self, request: SimulationRequest) -> SimulationResult:
        """
        提交Alpha模拟请求
        
        Args:
            request: 模拟请求参数
            
        Returns:
            SimulationResult: 模拟结果
        """
        self.logger.debug(f"开始Alpha模拟: {request.alpha_expression[:50]}...")
        
        # 构建请求数据 - 使用WQ API要求的字段名（camelCase）
        simulation_data = {
            'type': 'REGULAR',
            'settings': {
                'instrumentType': 'EQUITY',
                'region': request.region,
                'universe': request.universe,
                'delay': request.delay,
                'decay': request.decay,
                'neutralization': request.neutralization,
                'truncation': request.truncation,
                'pasteurization': request.pasteurization,
                'unitHandling': request.unit_handling,    # 注意：API要求camelCase
                'nanHandling': request.nan_handling,      # 注意：API要求camelCase
                'language': request.language,
                'visualization': request.visualization,
            },
            'regular': request.alpha_expression
        }
        
        # 打印完整的请求数据（单行格式便于调试）
        import json
        request_json = json.dumps(
            simulation_data, separators=(',', ':'), ensure_ascii=False)
        self.logger.info(f"📤 API请求: {request_json}")

        try:
            start_time = time.time()
            result = SimulationResult()
            
            # 第一步：提交模拟请求
            submit_response = await self._make_request(
                'POST', 
                self.endpoints['simulations'],
                json=simulation_data
            )
            
            if submit_response.status == 201:
                simulation_url = submit_response.headers.get('Location')
                if simulation_url:
                    # 第二步：轮询模拟结果
                    result = await self._poll_simulation_progress(simulation_url)
                else:
                    result.status = "failed"
                    result.error_message = "API响应缺少Location头"
            else:
                result.status = "failed"
                try:
                    response_data = await submit_response.json()
                    
                    # 详细解析WQ API错误响应格式
                    error_message = self._extract_api_error_message(submit_response.status, response_data)
                    result.error_message = f"API错误: {error_message}"
                    
                    # 记录完整错误响应用于调试
                    self.logger.debug(f"API错误详情 - 状态码: {submit_response.status}")
                    self.logger.debug(f"API错误详情 - 完整响应: {response_data}")
                    
                except Exception as json_error:
                    # 如果无法解析JSON，记录原始响应
                    try:
                        error_text = submit_response.text if hasattr(submit_response, 'text') else str(submit_response.data)
                        result.error_message = f"API错误: HTTP {submit_response.status} - {error_text[:200]}"
                        self.logger.debug(f"API错误 - 无法解析JSON: {json_error}")
                        self.logger.debug(f"API错误 - 原始响应: {error_text}")
                    except Exception:
                        result.error_message = f"API错误: HTTP {submit_response.status} - 响应解析失败"
            
            result.duration_seconds = time.time() - start_time
            return result
            
        except RateLimitError as e:
            self.stats['rate_limits'] += 1
            result = SimulationResult()
            result.status = "failed"
            result.error_message = f"速率限制: {str(e)}"
            return result
            
        except Exception as e:
            self.stats['api_errors'] += 1
            self.logger.error(f"Alpha模拟异常: {str(e)}")
            result = SimulationResult()
            result.status = "failed"
            result.error_message = f"模拟异常: {str(e)}"
            return result

    async def _poll_simulation_progress(self, simulation_url: str) -> SimulationResult:
        """
        轮询模拟进度
        
        Args:
            simulation_url: 模拟进度URL
            
        Returns:
            SimulationResult: 最终结果
        """
        result = SimulationResult()
        
        while True:
            try:
                progress_response = await self._make_request('GET', simulation_url, use_full_url=True)
                
                if progress_response.status == 200:
                    json_data = await progress_response.json()
                    retry_after = progress_response.headers.get('Retry-After')
                    
                    if retry_after:
                        # 还在处理中，等待
                        await asyncio.sleep(float(retry_after))
                    else:
                        # 处理完成，解析结果
                        alpha_id = json_data.get("alpha")
                        if alpha_id:
                            result.status = "completed"
                            result.alpha_id = alpha_id
                            
                            # 提取性能指标
                            performance = json_data.get("performance", {})
                            if performance:
                                result.annual_return = performance.get("annualReturn")
                                result.sharpe_ratio = performance.get("sharpeRatio")
                                result.max_drawdown = performance.get("maxDrawdown")
                                result.volatility = performance.get("volatility")
                                result.total_return = performance.get("totalReturn")
                                result.information_ratio = performance.get("informationRatio")
                        else:
                            result.status = "failed"
                            result.error_message = "模拟结果异常: 缺少alpha_id"
                        break
                else:
                    result.status = "failed"
                    result.error_message = f"进度查询失败: HTTP {progress_response.status}"
                    break
                    
            except Exception as e:
                result.status = "failed"
                result.error_message = f"进度查询异常: {str(e)}"
                break
                
        return result

    async def submit_alpha(self, alpha_id: str) -> Dict[str, Any]:
        """
        提交Alpha到生产环境
        
        Args:
            alpha_id: Alpha ID
            
        Returns:
            Dict: 提交结果
        """
        endpoint = self.endpoints['alphas_submit'].format(alpha_id=alpha_id)
        
        response = await self._make_request('POST', endpoint)
        return await response.json()

    async def get_alpha_info(self, alpha_id: str) -> Dict[str, Any]:
        """
        获取Alpha详细信息
        
        Args:
            alpha_id: Alpha ID
            
        Returns:
            Dict: Alpha信息
        """
        endpoint = self.endpoints['alphas_info'].format(alpha_id=alpha_id)
        
        response = await self._make_request('GET', endpoint)
        return await response.json()

    async def get_available_operators(self) -> Dict[str, Any]:
        """
        获取可用的操作符列表
        
        Returns:
            Dict: 操作符列表
        """
        response = await self._make_request('GET', self.endpoints['operators'])
        return await response.json()

    async def get_data_sets(self) -> Dict[str, Any]:
        """
        获取可用的数据集
        
        Returns:
            Dict: 数据集列表
        """
        response = await self._make_request('GET', self.endpoints['data_sets'])
        return await response.json()

    async def get_data_fields(self, dataset: str) -> Dict[str, Any]:
        """
        获取指定数据集的字段列表
        
        Args:
            dataset: 数据集名称
            
        Returns:
            Dict: 数据字段列表
        """
        response = await self._make_request(
            'GET', 
            self.endpoints['data_fields'],
            params={'dataset': dataset}
        )
        return await response.json()

    async def get_user_alphas(self, limit: int = 100, offset: int = 0) -> Dict[str, Any]:
        """
        获取用户的Alpha列表
        
        Args:
            limit: 返回数量限制
            offset: 偏移量
            
        Returns:
            Dict: Alpha列表
        """
        response = await self._make_request(
            'GET',
            self.endpoints['users_alphas'],
            params={'limit': limit, 'offset': offset}
        )
        return await response.json()

    async def _make_request(self, 
                          method: str, 
                          endpoint: str,
                          use_full_url: bool = False,
                          **kwargs) -> APIResponse:
        """
        统一的HTTP请求方法 - 修复连接过早关闭问题
        
        Args:
            method: HTTP方法
            endpoint: API端点或完整URL（当use_full_url=True时）
            use_full_url: 是否使用完整URL
            **kwargs: 其他请求参数
            
        Returns:
            APIResponse: 包装的HTTP响应（避免连接关闭问题）
        """
        # 更新统计
        self.stats['total_requests'] += 1
        
        # 确保有效会话
        session = await self.session_manager.get_valid_session()
        if not session:
            self.stats['failed_requests'] += 1
            raise AuthenticationError("无法获取有效的认证会话")
        
        # 确保HTTP会话池
        if not self.session_manager.session_pool:
            await self.session_manager._init_session_pool()
        
        # 构建URL
        if use_full_url:
            url = endpoint
        else:
            url = self.session_manager.base_url + endpoint
        
        # 重试循环处理速率限制
        max_retries = 10  # 最大重试次数
        retry_count = 0

        while retry_count < max_retries:
            try:
                # 执行请求
                async with self.session_manager.session_pool.request(
                        method, url, **kwargs) as response:

                    # 处理速率限制 - 自动等待和重试
                    if response.status == 429:
                        retry_count += 1
                        retry_after = float(
                            response.headers.get('Retry-After', 10))
                        self.logger.warning(
                            f"API速率限制，等待 {retry_after} 秒后重试... (第{retry_count}次)")

                        if retry_count >= max_retries:
                            self.stats['failed_requests'] += 1
                            raise RateLimitError(
                                f"API速率限制，已重试{max_retries}次仍失败")

                        await asyncio.sleep(retry_after)
                        continue  # 重试请求

                    # 更新成功统计
                    if response.status < 400:
                        self.stats['successful_requests'] += 1
                    else:
                        self.stats['failed_requests'] += 1

                    # 创建响应包装对象 - 在连接关闭前读取所有数据
                    api_response = await APIResponse.from_aiohttp_response(response)
                    return api_response

            except aiohttp.ClientError as e:
                self.stats['failed_requests'] += 1
                raise NetworkError(f"网络请求失败: {str(e)}")

        # 如果到这里说明重试次数用尽
        self.stats['failed_requests'] += 1
        raise RateLimitError(f"API速率限制，重试{max_retries}次后仍失败")

    def _extract_api_error_message(self, status_code: int, response_data: Dict[str, Any]) -> str:
        """
        从WQ API响应中提取详细错误信息
        
        Args:
            status_code: HTTP状态码
            response_data: API响应数据
            
        Returns:
            str: 详细的错误信息
        """
        if not response_data:
            # 对于空响应，直接使用状态码信息
            if status_code in [500, 502, 503, 504]:
                return f"HTTP {status_code} - 服务器内部错误"
            elif status_code == 429:
                return f"HTTP {status_code} - 请求频率限制"
            elif status_code in [400, 422]:
                return f"HTTP {status_code} - 请求参数错误"
            elif status_code in [401, 403]:
                return f"HTTP {status_code} - 认证或权限错误"
            else:
                return f"HTTP {status_code} - 空响应"
        
        # 常见的错误字段列表（按优先级排序）
        error_fields = [
            'detail',           # WQ API常用
            'message',          # 通用错误消息
            'error',            # 简单错误
            'error_message',    # 完整错误消息
            'errors',           # 错误数组
            'description',      # 错误描述
            'reason',           # 错误原因
            'title',            # 错误标题
        ]
        
        # 1. 尝试从标准错误字段提取
        for field in error_fields:
            if field in response_data:
                error_value = response_data[field]
                
                # 处理字符串类型错误
                if isinstance(error_value, str) and error_value.strip():
                    return f"HTTP {status_code} - {error_value.strip()}"
                
                # 处理数组类型错误
                elif isinstance(error_value, list) and error_value:
                    if isinstance(error_value[0], str):
                        return f"HTTP {status_code} - {error_value[0]}"
                    elif isinstance(error_value[0], dict):
                        # 提取嵌套错误信息
                        nested_error = self._extract_nested_error(error_value[0])
                        if nested_error:
                            return f"HTTP {status_code} - {nested_error}"
                
                # 处理字典类型错误
                elif isinstance(error_value, dict):
                    nested_error = self._extract_nested_error(error_value)
                    if nested_error:
                        return f"HTTP {status_code} - {nested_error}"
        
        # 2. 特殊状态码处理
        status_messages = {
            400: "请求参数错误",
            401: "认证失败",
            403: "权限不足",
            404: "资源不存在",
            409: "资源冲突",
            422: "参数验证失败",
            429: "请求频率限制",
            500: "服务器内部错误", 
            502: "网关错误",
            503: "服务不可用",
            504: "网关超时"
        }
        
        if status_code in status_messages:
            # 尝试添加更多详细信息
            extra_info = []
            
            # 查找可能的额外信息字段
            for key, value in response_data.items():
                if isinstance(value, str) and len(value) < 200:  # 避免过长的值
                    extra_info.append(f"{key}: {value}")
            
            base_message = status_messages[status_code]
            if extra_info:
                return f"HTTP {status_code} - {base_message} ({', '.join(extra_info[:3])})"
            else:
                return f"HTTP {status_code} - {base_message}"
        
        # 3. 如果都找不到，尝试提取关键信息
        if response_data:
            # 查找任何有用的字符串值
            useful_strings = []
            for key, value in response_data.items():
                if isinstance(value, str) and 10 <= len(value) <= 200:
                    useful_strings.append(f"{key}: {value}")
                elif isinstance(value, (int, float, bool)):
                    useful_strings.append(f"{key}: {value}")
            
            if useful_strings:
                return f"HTTP {status_code} - {', '.join(useful_strings[:2])}"
        
        # 4. 最后的fallback
        return f"HTTP {status_code} - 响应: {str(response_data)[:100]}..."

    def _extract_nested_error(self, error_dict: Dict[str, Any]) -> Optional[str]:
        """
        从嵌套的错误字典中提取错误信息
        
        Args:
            error_dict: 错误字典
            
        Returns:
            Optional[str]: 提取的错误信息
        """
        # 常见的嵌套错误字段
        nested_fields = ['message', 'description', 'detail', 'text', 'reason']
        
        for field in nested_fields:
            if field in error_dict and isinstance(error_dict[field], str):
                value = error_dict[field].strip()
                if value:
                    return value
        
        # 如果没有找到标准字段，返回第一个字符串值
        for key, value in error_dict.items():
            if isinstance(value, str) and value.strip():
                return f"{key}: {value.strip()}"
        
        return None

    def get_statistics(self) -> Dict[str, Any]:
        """
        获取API客户端统计信息
        
        Returns:
            Dict: 统计信息
        """
        total = self.stats['total_requests']
        if total > 0:
            success_rate = self.stats['successful_requests'] / total * 100
        else:
            success_rate = 0
            
        return {
            **self.stats,
            'success_rate': f"{success_rate:.1f}%"
        }

    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            Dict: 健康状态
        """
        try:
            # 简单的操作符查询作为健康检查
            await self.get_available_operators()
            return {
                'status': 'healthy',
                'timestamp': datetime.now().isoformat(),
                'statistics': self.get_statistics()
            }
        except Exception as e:
            return {
                'status': 'unhealthy',
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'statistics': self.get_statistics()
            }