"""
WQ因子挖掘系统核心库模块

包含会话管理、认证客户端、频率控制、统一API客户端等核心功能。
"""

from .session_manager import SessionManager, Session
from .auth_client import WQAuthClient
from .rate_limiter import APIRateLimiter
from .wq_api_client import WorldQuantAPIClient, SimulationRequest, SimulationResult
from .logger import get_logger, get_ui
from .exceptions import (
    SessionManagerError,
    AuthenticationError,
    SessionExpiredError,
    PermissionError,
    RateLimitError,
    NetworkError,
    InvalidCredentialsError,
    TokenRefreshError,
    ConfigurationError,
    APIError
)

__all__ = [
    # 会话管理
    'SessionManager',
    'Session',

    # 认证和API
    'WQAuthClient',
    'WorldQuantAPIClient',
    'SimulationRequest',
    'SimulationResult',

    # 工具类
    'APIRateLimiter',
    'get_logger',
    'get_ui',

    # 异常类
    'SessionManagerError',
    'AuthenticationError',
    'SessionExpiredError',
    'PermissionError',
    'RateLimitError',
    'NetworkError',
    'InvalidCredentialsError',
    'TokenRefreshError',
    'ConfigurationError',
    'APIError'
]
