"""
API频率控制器

实现滑动窗口算法限制API调用频率，避免触发WQ平台的速率限制。
支持不同endpoint的不同限制策略。
"""
import asyncio
import time
from collections import defaultdict, deque
from typing import Dict, Any, Optional
from dataclasses import dataclass, field

from .exceptions import RateLimitError


@dataclass
class RateLimit:
    """频率限制配置"""
    calls: int  # 允许的调用次数
    period: float  # 时间窗口（秒）
    burst: int = 0  # 突发允许次数（可选）


@dataclass 
class RateLimitState:
    """频率限制状态"""
    calls: deque = field(default_factory=deque)  # 调用时间戳队列
    last_reset: float = field(default_factory=time.time)  # 上次重置时间
    wait_until: float = 0  # 等待到此时间才能继续调用


class APIRateLimiter:
    """
    API频率控制器
    
    使用滑动窗口算法实现频率控制，支持：
    1. 全局频率限制
    2. 按endpoint的独立限制
    3. 按用户的独立限制
    4. 突发调用控制
    5. 自动等待和重试
    """
    
    def __init__(self, config: Dict[str, Any] = None):
        """
        初始化频率控制器
        
        Args:
            config: 频率控制配置
        """
        self.config = config or {}
        
        # 默认限制配置 (基于WQ平台经验值)
        self.default_limits = {
            'global': RateLimit(calls=60, period=60),  # 全局每分钟60次
            'authentication': RateLimit(calls=5, period=300),  # 认证每5分钟5次
            'simulation': RateLimit(calls=10, period=60),  # 仿真每分钟10次
            'alpha_submission': RateLimit(calls=5, period=60),  # 提交每分钟5次
            'data_query': RateLimit(calls=30, period=60),  # 数据查询每分钟30次
        }
        
        # 从配置更新限制
        self._update_limits_from_config()
        
        # 状态跟踪
        self.global_state = RateLimitState()
        self.endpoint_states: Dict[str, RateLimitState] = defaultdict(RateLimitState)
        self.user_states: Dict[str, RateLimitState] = defaultdict(RateLimitState)
        
        # 锁保护并发访问
        self._locks: Dict[str, asyncio.Lock] = defaultdict(asyncio.Lock)
    
    def _update_limits_from_config(self):
        """从配置更新频率限制"""
        rate_limits = self.config.get('rate_limits', {})
        
        for endpoint, config in rate_limits.items():
            if isinstance(config, dict):
                calls = config.get('calls', 60)
                period = config.get('period', 60)
                burst = config.get('burst', 0)
                self.default_limits[endpoint] = RateLimit(calls, period, burst)
    
    async def acquire(self, 
                     endpoint: str = 'default',
                     user_id: Optional[str] = None,
                     weight: int = 1) -> None:
        """
        获取API调用许可
        
        Args:
            endpoint: API端点名称
            user_id: 用户ID（可选，用于用户级限制）
            weight: 调用权重（默认1，重要操作可以设为更高值）
            
        Raises:
            RateLimitError: 超出频率限制
        """
        # 检查全局限制
        await self._check_and_wait('global', self.default_limits['global'], weight)
        
        # 检查endpoint限制
        endpoint_limit = self.default_limits.get(endpoint, self.default_limits['global'])
        await self._check_and_wait(f'endpoint:{endpoint}', endpoint_limit, weight)
        
        # 检查用户限制（如果指定）
        if user_id:
            user_limit = self.default_limits.get('per_user', self.default_limits['global'])
            await self._check_and_wait(f'user:{user_id}', user_limit, weight)
    
    async def _check_and_wait(self, key: str, limit: RateLimit, weight: int = 1):
        """
        检查限制并等待
        
        Args:
            key: 限制键名
            limit: 频率限制配置
            weight: 调用权重
        """
        async with self._locks[key]:
            state = self._get_state_for_key(key)
            current_time = time.time()
            
            # 清理过期的调用记录
            self._cleanup_expired_calls(state, limit, current_time)
            
            # 检查是否需要等待
            if state.wait_until > current_time:
                wait_time = state.wait_until - current_time
                raise RateLimitError(
                    f"频率限制: {key}，需要等待 {wait_time:.2f} 秒",
                    retry_after=wait_time
                )
            
            # 检查当前调用数是否超限
            current_calls = len(state.calls)
            if current_calls + weight > limit.calls:
                # 计算等待时间
                if state.calls:
                    oldest_call = state.calls[0]
                    wait_time = oldest_call + limit.period - current_time
                    if wait_time > 0:
                        state.wait_until = current_time + wait_time
                        raise RateLimitError(
                            f"频率限制: {key}，当前 {current_calls}/{limit.calls} 调用，需要等待 {wait_time:.2f} 秒",
                            retry_after=wait_time
                        )
            
            # 记录此次调用
            for _ in range(weight):
                state.calls.append(current_time)
    
    def _get_state_for_key(self, key: str) -> RateLimitState:
        """获取指定键的状态对象"""
        if key == 'global':
            return self.global_state
        elif key.startswith('endpoint:'):
            endpoint = key[9:]  # 移除 'endpoint:' 前缀
            return self.endpoint_states[endpoint]
        elif key.startswith('user:'):
            user_id = key[5:]  # 移除 'user:' 前缀
            return self.user_states[user_id]
        else:
            return self.endpoint_states[key]
    
    def _cleanup_expired_calls(self, state: RateLimitState, limit: RateLimit, current_time: float):
        """清理过期的调用记录"""
        cutoff_time = current_time - limit.period
        
        while state.calls and state.calls[0] <= cutoff_time:
            state.calls.popleft()
    
    async def wait_if_needed(self, 
                           endpoint: str = 'default',
                           user_id: Optional[str] = None,
                           weight: int = 1,
                           max_wait: float = 60) -> float:
        """
        如果需要等待，则等待到可以调用为止
        
        Args:
            endpoint: API端点名称
            user_id: 用户ID
            weight: 调用权重
            max_wait: 最大等待时间（秒）
            
        Returns:
            float: 实际等待时间
            
        Raises:
            RateLimitError: 等待时间超过最大限制
        """
        start_time = time.time()
        
        while True:
            try:
                await self.acquire(endpoint, user_id, weight)
                return time.time() - start_time
            except RateLimitError as e:
                if e.retry_after and e.retry_after <= max_wait:
                    await asyncio.sleep(e.retry_after)
                    continue
                else:
                    raise RateLimitError(
                        f"等待时间 {e.retry_after:.2f}秒 超过最大限制 {max_wait}秒",
                        retry_after=e.retry_after
                    )
    
    def get_status(self, endpoint: str = None, user_id: str = None) -> Dict[str, Any]:
        """
        获取频率限制状态
        
        Args:
            endpoint: 指定端点（可选）
            user_id: 指定用户（可选）
            
        Returns:
            Dict[str, Any]: 状态信息
        """
        current_time = time.time()
        status = {}
        
        # 全局状态
        global_limit = self.default_limits['global']
        self._cleanup_expired_calls(self.global_state, global_limit, current_time)
        status['global'] = {
            'limit': global_limit.calls,
            'period': global_limit.period,
            'current_calls': len(self.global_state.calls),
            'remaining': max(0, global_limit.calls - len(self.global_state.calls)),
            'reset_time': self.global_state.calls[0] + global_limit.period if self.global_state.calls else None
        }
        
        # 端点状态
        if endpoint:
            endpoint_limit = self.default_limits.get(endpoint, global_limit)
            endpoint_state = self.endpoint_states.get(endpoint)
            if endpoint_state:
                self._cleanup_expired_calls(endpoint_state, endpoint_limit, current_time)
                status[f'endpoint:{endpoint}'] = {
                    'limit': endpoint_limit.calls,
                    'period': endpoint_limit.period,
                    'current_calls': len(endpoint_state.calls),
                    'remaining': max(0, endpoint_limit.calls - len(endpoint_state.calls)),
                    'reset_time': endpoint_state.calls[0] + endpoint_limit.period if endpoint_state.calls else None
                }
        
        # 用户状态
        if user_id:
            user_limit = self.default_limits.get('per_user', global_limit)
            user_state = self.user_states.get(user_id)
            if user_state:
                self._cleanup_expired_calls(user_state, user_limit, current_time)
                status[f'user:{user_id}'] = {
                    'limit': user_limit.calls,
                    'period': user_limit.period,
                    'current_calls': len(user_state.calls),
                    'remaining': max(0, user_limit.calls - len(user_state.calls)),
                    'reset_time': user_state.calls[0] + user_limit.period if user_state.calls else None
                }
        
        return status
    
    def reset_limits(self, endpoint: str = None, user_id: str = None):
        """
        重置频率限制状态
        
        Args:
            endpoint: 指定端点（可选，None表示重置所有）
            user_id: 指定用户（可选，None表示重置所有）
        """
        if endpoint is None and user_id is None:
            # 重置所有
            self.global_state = RateLimitState()
            self.endpoint_states.clear()
            self.user_states.clear()
        elif endpoint:
            # 重置指定端点
            if endpoint in self.endpoint_states:
                self.endpoint_states[endpoint] = RateLimitState()
        elif user_id:
            # 重置指定用户
            if user_id in self.user_states:
                self.user_states[user_id] = RateLimitState()
    
    def update_limit(self, key: str, calls: int, period: float, burst: int = 0):
        """
        动态更新频率限制
        
        Args:
            key: 限制键名 ('global', endpoint名称等)
            calls: 允许调用次数
            period: 时间窗口
            burst: 突发允许次数
        """
        self.default_limits[key] = RateLimit(calls, period, burst)