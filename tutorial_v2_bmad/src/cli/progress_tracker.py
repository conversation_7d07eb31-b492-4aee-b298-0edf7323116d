"""
进度追踪器

提供实时的任务进度监控和状态展示。
"""
import asyncio
from typing import List, Dict, Any, Optional
from contextlib import asynccontextmanager

from rich.console import Console
from rich.progress import (
    Progress, 
    SpinnerColumn, 
    TextColumn, 
    BarColumn, 
    MofNCompleteColumn, 
    TimeElapsedColumn,
    TaskID
)
from rich.live import Live
from rich.panel import Panel
from rich.table import Table
from rich.text import Text


class ProgressTracker:
    """进度追踪器"""
    
    def __init__(self, console: Console):
        self.console = console
        self.progress = None
        self.live_display = None
        
    def create_progress(self):
        """创建进度条组件"""
        return Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(complete_style="green", finished_style="green"),
            MofNCompleteColumn(),
            TimeElapsedColumn(),
            console=self.console,
            transient=False
        )
        
    @asynccontextmanager
    async def track_workflow(self, workflow_name: str):
        """追踪工作流进度的上下文管理器"""
        try:
            self.console.print(f"\n🚀 开始执行: {workflow_name}")
            self.console.print("=" * 50)
            
            with self.create_progress() as progress:
                self.progress = progress
                yield progress
                
        finally:
            self.progress = None
            self.console.print("=" * 50)
            self.console.print(f"✅ 工作流 '{workflow_name}' 执行完成\n")
            
    async def track_factor_generation(self, config: Dict[str, Any]):
        """追踪因子生成进度"""
        max_order = config.get("max_order", 3)
        factors_per_order = config.get("max_factors_per_order", 1000)
        
        with self.create_progress() as progress:
            main_task = progress.add_task(
                f"生成 {max_order} 阶因子...", 
                total=max_order
            )
            
            for order in range(1, max_order + 1):
                # 创建当前阶数的子任务
                order_task = progress.add_task(
                    f"生成 {order} 阶因子 (目标: {factors_per_order})", 
                    total=factors_per_order
                )
                
                # 模拟因子生成过程
                for i in range(factors_per_order + 1):
                    progress.update(order_task, completed=i)
                    await asyncio.sleep(0.001)  # 模拟处理时间
                    
                # 完成当前阶数
                progress.update(main_task, advance=1)
                self.console.print(f"✅ {order} 阶因子生成完成 ({factors_per_order} 个)")
                
    async def track_validation_progress(self, factors: List[Dict], config: Dict[str, Any]):
        """追踪因子检验进度"""
        total_factors = len(factors)
        concurrent_checks = config.get("concurrent_checks", 3)
        
        with self.create_progress() as progress:
            main_task = progress.add_task(
                f"检验 {total_factors} 个因子...", 
                total=total_factors
            )
            
            # 分批处理
            batch_size = max(1, total_factors // 10)  # 分10批显示
            
            for i in range(0, total_factors, batch_size):
                batch = factors[i:i + batch_size]
                batch_task = progress.add_task(
                    f"检验批次 {i//batch_size + 1} ({len(batch)} 个因子)", 
                    total=len(batch)
                )
                
                # 模拟检验过程
                for j, factor in enumerate(batch):
                    progress.update(batch_task, completed=j + 1)
                    progress.update(main_task, advance=1)
                    await asyncio.sleep(0.02)  # 模拟检验时间
                    
                self.console.print(f"✅ 批次 {i//batch_size + 1} 检验完成")
                
    async def track_submission_progress(self, submittable_factors: List[Dict]):
        """追踪因子提交进度"""
        total_factors = len(submittable_factors)
        
        if total_factors == 0:
            self.console.print("ℹ️ 没有可提交的因子")
            return
            
        with self.create_progress() as progress:
            main_task = progress.add_task(
                f"提交 {total_factors} 个因子...", 
                total=total_factors
            )
            
            success_count = 0
            failed_count = 0
            
            for i, factor in enumerate(submittable_factors):
                factor_task = progress.add_task(
                    f"提交因子 {factor.get('id', i+1)}", 
                    total=100
                )
                
                # 模拟提交过程的各个阶段
                stages = [
                    ("验证因子", 20),
                    ("连接WQ平台", 40),
                    ("上传数据", 70),
                    ("等待确认", 100)
                ]
                
                try:
                    for stage_name, stage_progress in stages:
                        progress.update(
                            factor_task, 
                            completed=stage_progress,
                            description=f"提交因子 {factor.get('id', i+1)} - {stage_name}"
                        )
                        await asyncio.sleep(0.5)  # 模拟处理时间
                        
                    # 模拟提交成功/失败
                    import random
                    if random.random() > 0.2:  # 80% 成功率
                        success_count += 1
                        result = "✅ 成功"
                    else:
                        failed_count += 1
                        result = "❌ 失败"
                        
                except Exception:
                    failed_count += 1
                    result = "❌ 错误"
                    
                progress.update(main_task, advance=1)
                self.console.print(f"{result} - 因子 {factor.get('id', i+1)}")
                
            # 显示提交摘要
            self.console.print(f"\n📊 提交摘要:")
            self.console.print(f"  • 总计: {total_factors} 个")
            self.console.print(f"  • 成功: {success_count} 个")
            self.console.print(f"  • 失败: {failed_count} 个")
            self.console.print(f"  • 成功率: {success_count/total_factors*100:.1f}%")
            
    async def show_live_status(self, status_data: Dict[str, Any]):
        """显示实时状态监控"""
        def create_status_table():
            table = Table(title="🔄 实时系统状态", show_header=True)
            table.add_column("指标", style="cyan", width=15)
            table.add_column("当前值", style="green", width=15)
            table.add_column("状态", style="yellow", width=10)
            
            for metric, value in status_data.items():
                if isinstance(value, dict):
                    table.add_row(
                        metric,
                        str(value.get("value", "N/A")),
                        value.get("status", "正常")
                    )
                else:
                    table.add_row(metric, str(value), "正常")
                    
            return table
            
        # 显示10秒的实时状态
        with Live(create_status_table(), refresh_per_second=1) as live:
            for i in range(10):
                # 模拟状态更新
                if "运行时间" in status_data:
                    status_data["运行时间"] = f"{i+1} 秒"
                    
                live.update(create_status_table())
                await asyncio.sleep(1)
                
    def create_step_progress(self, steps: List[str]) -> Progress:
        """创建分步骤的进度条"""
        progress = Progress(
            TextColumn("[progress.description]"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            console=self.console
        )
        
        return progress
        
    async def execute_with_steps(self, steps: List[Dict[str, Any]]):
        """执行带步骤的任务"""
        total_steps = len(steps)
        
        with self.create_progress() as progress:
            main_task = progress.add_task("执行工作流", total=total_steps)
            
            for i, step in enumerate(steps):
                step_name = step.get("name", f"步骤 {i+1}")
                step_work = step.get("work", 100)
                
                step_task = progress.add_task(
                    f"🔄 {step_name}", 
                    total=step_work
                )
                
                # 执行步骤工作
                for j in range(step_work + 1):
                    progress.update(step_task, completed=j)
                    await asyncio.sleep(0.01)
                    
                progress.update(main_task, advance=1)
                self.console.print(f"✅ {step_name} 完成")
                
    def show_progress_summary(self, results: Dict[str, Any]):
        """显示进度摘要"""
        summary_text = Text()
        summary_text.append("📊 执行摘要\n\n", style="bold cyan")
        
        for category, data in results.items():
            summary_text.append(f"{category}:\n", style="bold yellow")
            
            if isinstance(data, dict):
                for key, value in data.items():
                    summary_text.append(f"  • {key}: {value}\n", style="white")
            else:
                summary_text.append(f"  • {data}\n", style="white")
                
            summary_text.append("\n")
            
        self.console.print(Panel(
            summary_text,
            title="📈 执行结果",
            border_style="green"
        ))