"""
结果展示系统

提供美观的结果展示，包括表格、图表和统计信息。
"""
from typing import List, Dict, Any, Optional
from rich.console import Console
from rich.table import Table
from rich.panel import Panel
from rich.text import Text
from rich.progress import Progress, BarColumn, TextColumn
from rich import box
import datetime


class ResultsDisplay:
    """结果展示系统"""
    
    def __init__(self, console: Console):
        self.console = console
        
    def show_factor_generation_results(self, results: Dict[str, Dict]):
        """显示因子生成结果"""
        if not results:
            self.console.print("📋 暂无因子生成结果")
            return
            
        table = Table(title="🏭 因子生成结果", show_header=True, box=box.ROUNDED)
        table.add_column("阶数", style="cyan", width=10)
        table.add_column("生成数量", style="green", width=10, justify="right")
        table.add_column("回测完成", style="yellow", width=10, justify="right")
        table.add_column("平均Sharpe", style="magenta", width=12, justify="right")
        table.add_column("平均收益率", style="blue", width=12, justify="right")
        table.add_column("状态", style="white", width=10)
        
        total_generated = 0
        total_backtested = 0
        
        for order, data in results.items():
            generated = data.get('generated', 0)
            backtested = data.get('backtested', 0)
            sharpe_value = data.get('avg_sharpe', 0)
            returns_value = data.get('avg_returns', 0)
            
            total_generated += generated
            total_backtested += backtested
            
            # 格式化数值
            sharpe_str = f"{sharpe_value:.3f}" if sharpe_value > 0 else "N/A"
            returns_str = f"{returns_value:.2%}" if returns_value > 0 else "N/A"
            
            # 根据Sharpe比率设置状态
            if sharpe_value >= 1.5:
                status = "[green]优秀[/green]"
            elif sharpe_value >= 1.0:
                status = "[yellow]良好[/yellow]"
            elif sharpe_value > 0:
                status = "[blue]一般[/blue]"
            else:
                status = "[dim]待处理[/dim]"
                
            table.add_row(
                f"{order}阶因子",
                str(generated),
                str(backtested),
                sharpe_str,
                returns_str,
                status
            )
            
        # 添加总计行
        table.add_section()
        table.add_row(
            "[bold]总计[/bold]",
            f"[bold]{total_generated}[/bold]",
            f"[bold]{total_backtested}[/bold]",
            "[bold]--[/bold]",
            "[bold]--[/bold]",
            "[bold]--[/bold]"
        )
        
        self.console.print(table)
        
        # 显示生成摘要
        self._show_generation_summary(total_generated, total_backtested)
        
    def show_validation_results(self, results: Dict[str, Any]):
        """显示检验结果"""
        if not results:
            self.console.print("📋 暂无检验结果")
            return
            
        table = Table(title="🔍 因子检验结果", show_header=True, box=box.ROUNDED)
        table.add_column("检验项目", style="cyan", width=18)
        table.add_column("通过数量", style="green", width=10, justify="right")
        table.add_column("失败数量", style="red", width=10, justify="right")
        table.add_column("通过率", style="yellow", width=10, justify="right")
        table.add_column("平均值", style="magenta", width=10, justify="right")
        table.add_column("阈值", style="blue", width=10, justify="right")
        
        validation_items = [
            ("自相关性检验", results.get("self_correlation", {})),
            ("生产相关性检验", results.get("prod_correlation", {})),
            ("综合质量评估", results.get("quality_assessment", {}))
        ]
        
        total_passed = 0
        total_failed = 0
        
        for item_name, item_data in validation_items:
            passed = item_data.get("passed", 0)
            failed = item_data.get("failed", 0)
            total = passed + failed
            
            total_passed += passed
            total_failed += failed
            
            pass_rate = f"{(passed/total*100):.1f}%" if total > 0 else "N/A"
            avg_value = f"{item_data.get('average', 0):.3f}" if item_data.get('average') else "N/A"
            threshold = f"{item_data.get('threshold', 0):.1f}" if item_data.get('threshold') else "N/A"
            
            table.add_row(item_name, str(passed), str(failed), pass_rate, avg_value, threshold)
            
        # 添加总计行
        table.add_section()
        grand_total = total_passed + total_failed
        overall_rate = f"{(total_passed/grand_total*100):.1f}%" if grand_total > 0 else "0%"
        
        table.add_row(
            "[bold]总计[/bold]",
            f"[bold]{total_passed}[/bold]",
            f"[bold]{total_failed}[/bold]",
            f"[bold]{overall_rate}[/bold]",
            "[bold]--[/bold]",
            "[bold]--[/bold]"
        )
        
        self.console.print(table)
        
        # 显示检验质量分级
        self._show_quality_grades(results.get("quality_grades", {}))
        
    def show_submission_results(self, results: Dict[str, Any]):
        """显示提交结果"""
        if not results:
            self.console.print("📋 暂无提交结果")
            return
            
        table = Table(title="📤 因子提交结果", show_header=True, box=box.ROUNDED)
        table.add_column("提交状态", style="cyan", width=12)
        table.add_column("数量", style="green", width=8, justify="right")
        table.add_column("百分比", style="yellow", width=10, justify="right")
        table.add_column("平均用时", style="blue", width=10, justify="right")
        table.add_column("说明", style="white", width=25)
        
        total = results.get("total", 0)
        
        status_items = [
            ("成功提交", results.get("success", 0), "green", "已成功提交到WQ平台"),
            ("提交失败", results.get("failed", 0), "red", "提交过程中出现错误"),
            ("超时失败", results.get("timeout", 0), "yellow", "提交超时（30分钟限制）"),
            ("跳过提交", results.get("skipped", 0), "dim", "质量不符合要求")
        ]
        
        for status, count, color, description in status_items:
            percentage = f"{(count/total*100):.1f}%" if total > 0 else "0%"
            avg_time = results.get(f"{status.split()[0].lower()}_avg_time", "N/A")
            
            table.add_row(
                f"[{color}]{status}[/{color}]",
                str(count),
                percentage,
                str(avg_time),
                description
            )
            
        self.console.print(table)
        
        # 显示提交统计
        self._show_submission_stats(results)
        
    def show_workflow_summary(self, workflow_results: Dict[str, Any]):
        """显示完整工作流摘要"""
        summary_text = Text()
        summary_text.append("🎯 工作流执行摘要\n\n", style="bold cyan")
        
        # 基本信息
        start_time = workflow_results.get("start_time", "未知")
        end_time = workflow_results.get("end_time", "未知")
        duration = workflow_results.get("duration", "未知")
        
        summary_text.append("⏱️ 执行时间信息：\n", style="bold yellow")
        summary_text.append(f"  • 开始时间: {start_time}\n", style="white")
        summary_text.append(f"  • 结束时间: {end_time}\n", style="white")
        summary_text.append(f"  • 总耗时: {duration}\n\n", style="white")
        
        # 各阶段统计
        stages = workflow_results.get("stages", {})
        
        for stage_name, stage_data in stages.items():
            summary_text.append(f"📊 {stage_name}：\n", style="bold green")
            
            if isinstance(stage_data, dict):
                for key, value in stage_data.items():
                    summary_text.append(f"  • {key}: {value}\n", style="cyan")
            else:
                summary_text.append(f"  • 结果: {stage_data}\n", style="cyan")
                
            summary_text.append("\n")
            
        # 总体评估
        overall_status = workflow_results.get("overall_status", "未知")
        summary_text.append(f"🏆 总体状态: ", style="bold magenta")
        
        if overall_status == "成功":
            summary_text.append(f"{overall_status}\n", style="bold green")
        elif overall_status == "部分成功":
            summary_text.append(f"{overall_status}\n", style="bold yellow")
        else:
            summary_text.append(f"{overall_status}\n", style="bold red")
            
        self.console.print(Panel(
            summary_text,
            title="📈 工作流执行报告",
            border_style="green",
            box=box.ROUNDED
        ))
        
    def show_performance_metrics(self, metrics: Dict[str, Any]):
        """显示性能指标"""
        table = Table(title="⚡ 性能指标", show_header=True, box=box.ROUNDED)
        table.add_column("指标", style="cyan", width=20)
        table.add_column("当前值", style="green", width=15, justify="right")
        table.add_column("目标值", style="yellow", width=15, justify="right")
        table.add_column("状态", style="blue", width=10)
        
        performance_items = [
            ("平均响应时间", metrics.get("avg_response_time", "N/A"), "< 5秒"),
            ("内存使用率", metrics.get("memory_usage", "N/A"), "< 4GB"),
            ("CPU使用率", metrics.get("cpu_usage", "N/A"), "< 80%"),
            ("成功率", metrics.get("success_rate", "N/A"), "> 90%"),
            ("并发处理数", metrics.get("concurrent_tasks", "N/A"), "> 3"),
        ]
        
        for metric_name, current_value, target_value in performance_items:
            # 简单的状态评估
            status = "正常"
            if isinstance(current_value, (int, float)):
                if "时间" in metric_name and current_value > 5:
                    status = "[red]超标[/red]"
                elif "使用率" in metric_name and current_value > 80:
                    status = "[yellow]偏高[/yellow]"
                elif "成功率" in metric_name and current_value < 90:
                    status = "[red]偏低[/red]"
                else:
                    status = "[green]正常[/green]"
            else:
                status = "[dim]无数据[/dim]"
                
            table.add_row(metric_name, str(current_value), target_value, status)
            
        self.console.print(table)
        
    def _show_generation_summary(self, total_generated: int, total_backtested: int):
        """显示生成摘要"""
        completion_rate = (total_backtested / total_generated * 100) if total_generated > 0 else 0
        
        summary_text = Text()
        summary_text.append(f"📊 生成摘要: ", style="bold cyan")
        summary_text.append(f"共生成 {total_generated} 个因子, ", style="white")
        summary_text.append(f"完成回测 {total_backtested} 个 ", style="white")
        summary_text.append(f"({completion_rate:.1f}%)", style="yellow")
        
        self.console.print(summary_text)
        
    def _show_quality_grades(self, grades: Dict[str, int]):
        """显示质量分级"""
        if not grades:
            return
            
        # 创建质量分级进度条
        with Progress(
            TextColumn("[progress.description]"),
            BarColumn(),
            TextColumn("{task.completed}/{task.total}"),
        ) as progress:
            
            grade_colors = {"A": "green", "B": "yellow", "C": "blue", "D": "red"}
            total_factors = sum(grades.values())
            
            for grade, count in grades.items():
                color = grade_colors.get(grade, "white")
                task = progress.add_task(
                    f"[{color}]质量等级 {grade}[/{color}]",
                    total=total_factors,
                    completed=count
                )
                
    def _show_submission_stats(self, results: Dict[str, Any]):
        """显示提交统计"""
        stats_text = Text()
        
        success_count = results.get("success", 0)
        total_count = results.get("total", 0)
        success_rate = (success_count / total_count * 100) if total_count > 0 else 0
        
        stats_text.append("📈 提交统计: ", style="bold cyan")
        
        if success_rate >= 80:
            stats_text.append(f"成功率 {success_rate:.1f}% - 优秀", style="bold green")
        elif success_rate >= 60:
            stats_text.append(f"成功率 {success_rate:.1f}% - 良好", style="bold yellow")
        else:
            stats_text.append(f"成功率 {success_rate:.1f}% - 需改进", style="bold red")
            
        # 显示耗时信息
        total_time = results.get("total_time", 0)
        if total_time > 0:
            stats_text.append(f", 总耗时 {total_time:.1f} 分钟", style="white")
            
        self.console.print(stats_text)
        
    def show_comparison_table(self, current_results: Dict, historical_results: Dict = None):
        """显示对比表格"""
        table = Table(title="📊 结果对比", show_header=True, box=box.ROUNDED)
        table.add_column("指标", style="cyan", width=15)
        table.add_column("本次结果", style="green", width=12, justify="right")
        
        if historical_results:
            table.add_column("历史平均", style="yellow", width=12, justify="right")
            table.add_column("变化", style="blue", width=10, justify="right")
        
        # 对比指标
        metrics = [
            ("生成因子数", "total_generated"),
            ("通过检验", "passed_validation"),
            ("成功提交", "successful_submissions"),
            ("整体成功率", "overall_success_rate")
        ]
        
        for metric_name, metric_key in metrics:
            current = current_results.get(metric_key, 0)
            
            if historical_results:
                historical = historical_results.get(metric_key, 0)
                change = current - historical
                change_str = f"+{change}" if change > 0 else str(change)
                change_color = "green" if change > 0 else "red" if change < 0 else "white"
                
                table.add_row(
                    metric_name,
                    str(current),
                    str(historical),
                    f"[{change_color}]{change_str}[/{change_color}]"
                )
            else:
                table.add_row(metric_name, str(current))
                
        self.console.print(table)
        
    def export_results_to_text(self, results: Dict[str, Any], filename: str = None):
        """导出结果到文本文件"""
        if not filename:
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"results_{timestamp}.txt"
            
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                f.write("WQ因子挖掘系统 - 执行结果报告\n")
                f.write("=" * 50 + "\n\n")
                f.write(f"生成时间: {datetime.datetime.now()}\n\n")
                
                # 写入各项结果
                for section, data in results.items():
                    f.write(f"{section}:\n")
                    if isinstance(data, dict):
                        for key, value in data.items():
                            f.write(f"  {key}: {value}\n")
                    else:
                        f.write(f"  {data}\n")
                    f.write("\n")
                    
            self.console.print(f"✅ 结果已导出到: {filename}")
            
        except Exception as e:
            self.console.print(f"❌ 导出失败: {e}")