"""
交互式菜单系统

提供简洁友好的菜单界面，避免复杂的CLI命令和选项。
"""
from typing import Dict, Any
from rich.console import Console
from rich.prompt import Prompt, Confirm
from rich.panel import Panel
from rich.text import Text
from rich import box


class InteractiveMenu:
    """交互式菜单"""
    
    def __init__(self, console: Console):
        self.console = console
        
    async def show_main_menu(self) -> str:
        """显示主菜单"""
        self.console.print()  # 空行
        
        menu_text = Text()
        menu_text.append("请选择要执行的操作：\n\n", style="bold cyan")
        
        menu_options = [
            ("1", "🚀 快速开始", "使用默认配置一键启动因子挖掘"),
            ("2", "⚙️  自定义配置", "自定义参数后启动因子挖掘"),
            ("3", "📊 查看系统状态", "显示当前系统运行状态"),
            ("4", "📈 查看统计报告", "查看历史执行统计信息"),
            ("5", "🔧 系统设置", "查看和修改系统配置"),
            ("6", "❌ 退出系统", "安全退出程序")
        ]
        
        for num, title, desc in menu_options:
            menu_text.append(f"{num}. ", style="bold white")
            menu_text.append(f"{title}\n", style="green")
            menu_text.append(f"   {desc}\n\n", style="dim")
            
        self.console.print(Panel(
            menu_text,
            title="🏠 主菜单",
            border_style="blue",
            box=box.ROUNDED
        ))
        
        choice = Prompt.ask(
            "[bold cyan]请输入选择[/bold cyan]",
            choices=["1", "2", "3", "4", "5", "6"],
            default="1",
            show_choices=False
        )
        
        choice_mapping = {
            "1": "quick_start",
            "2": "custom_config",
            "3": "show_status", 
            "4": "show_statistics",
            "5": "configure",
            "6": "exit"
        }
        
        return choice_mapping.get(choice, "exit")
        
    async def get_default_config(self) -> Dict[str, Any]:
        """获取默认配置用于快速开始"""
        from .config_manager import ConfigManager
        
        # 从配置文件读取实际配置
        config_manager = ConfigManager()
        config = await config_manager.load_config()
        
        # 从config.json提取快速开始的默认值
        quick_start_config = config.get("quick_start", {})
        alpha_config = config.get("alpha_generation", {})
        validation_config = config.get("validation", {})
        submission_config = config.get("submission", {})
        
        # 从配置文件读取，不提供硬编码默认值
        dataset = quick_start_config.get("default_dataset")
        region = quick_start_config.get("default_region") 
        universe = quick_start_config.get("default_universe")
        max_step = quick_start_config.get("default_max_step")
        
        # 如果quick_start配置不完整，从全局配置获取
        if not dataset:
            datasets = config.get("datasets", [])
            if not datasets:
                raise ValueError("配置文件中缺少datasets配置")
            dataset = datasets[0]
            
        if not region:
            regions = config.get("regions", [])
            if not regions:
                raise ValueError("配置文件中缺少regions配置")
            region = regions[0]
            
        if not universe:
            universes = config.get("universes", [])
            if not universes:
                raise ValueError("配置文件中缺少universes配置")
            universe = universes[0]
            
        if not max_step:
            raise ValueError("配置文件中缺少quick_start.default_max_step配置")
        
        max_alphas = alpha_config.get("max_alphas_per_step")
        concurrent = alpha_config.get("concurrent_backtests")
        
        if max_alphas is None:
            raise ValueError("配置文件中缺少alpha_generation.max_alphas_per_step配置")
        if concurrent is None:
            raise ValueError("配置文件中缺少alpha_generation.concurrent_backtests配置")
        
        self.console.print()
        self.console.print(Panel(
            f"[bold green]🚀 快速开始模式[/bold green]\n\n"
            f"使用以下配置启动因子挖掘（来自 config.json）：\n\n"
            f"📊 数据集: {dataset}\n"
            f"🌍 地区: {region}\n" 
            f"📈 股票池: {universe}\n"
            f"🔢 最大步数: {max_step}步Alpha\n"
            f"⚙️ 每步Alpha数: {max_alphas}个\n"
            f"🔄 并发数: {concurrent}个\n"
            f"✅ 质量检验: {'启用' if validation_config.get('enable_quality_grading') else '禁用'}\n"
            f"📤 自动提交: {'启用' if submission_config.get('enable_batch_submission') else '禁用'}\n\n"
            f"[dim]💡 可通过修改 src/config/config.json 自定义这些默认值[/dim]",
            title="默认配置 (来自配置文件)",
            border_style="green",
            box=box.ROUNDED
        ))
        
        if not Confirm.ask("\n确认使用以上配置开始挖掘？", default=True):
            return await self.show_workflow_options()  # 转到自定义配置
            
        # 验证并获取其他配置项
        enable_validation = validation_config.get("enable_quality_grading")
        enable_submission = submission_config.get("enable_batch_submission")
        
        if enable_validation is None:
            raise ValueError("配置文件中缺少validation.enable_quality_grading配置")
        if enable_submission is None:
            raise ValueError("配置文件中缺少submission.enable_batch_submission配置")
        
        # 返回从配置文件读取的默认配置
        return {
            "dataset": dataset,
            "region": region,
            "universe": universe,
            "max_step": max_step,
            "max_alphas_per_step": max_alphas,
            "concurrent_backtests": concurrent,
            "enable_validation": enable_validation,
            "enable_submission": enable_submission
        }
        
    async def show_workflow_options(self) -> Dict[str, Any]:
        """显示工作流选项配置"""
        # 从配置文件获取默认值
        from .config_manager import ConfigManager
        config_manager = ConfigManager()
        config = await config_manager.load_config()
        
        alpha_config = config.get("alpha_generation", {})
        validation_config = config.get("validation", {})
        submission_config = config.get("submission", {})
        
        self.console.print()
        self.console.print(Panel(
            "[bold cyan]⚙️ 自定义配置模式[/bold cyan]\n\n"
            "您可以自定义以下参数。所有默认值来自配置文件，\n"
            "如果不确定，直接按回车使用配置文件中的默认值。\n\n"
            "[dim]提示: 如果只是想快速体验，建议使用 '快速开始' 选项[/dim]",
            title="高级配置",
            border_style="yellow",
            box=box.ROUNDED
        ))
        
        workflow_config = {}
        
        # 1. 选择数据集
        self.console.print("\n[bold]1. 数据集选择[/bold]")
        self.show_dataset_options()
        dataset_choice = Prompt.ask(
            "\n选择数据集",
            choices=["1", "2", "3"],
            default="1",
            show_default=True
        )
        
        dataset_mapping = {
            "1": "analyst4",
            "2": "pv15", 
            "3": "both"
        }
        workflow_config["dataset"] = dataset_mapping[dataset_choice]
        
        # 2. 选择地区
        self.console.print("\n[bold]2. 地区选择[/bold]")
        self.show_region_options()
        region_choice = Prompt.ask(
            "\n选择交易地区",
            choices=["1", "2", "3"],
            default="1",
            show_default=True
        )
        
        region_mapping = {
            "1": "USA",
            "2": "EUR",
            "3": "both"
        }
        workflow_config["region"] = region_mapping[region_choice]
        
        # 3. 选择股票池
        self.console.print("\n[bold]3. 股票池选择[/bold]")
        self.show_universe_options()
        universe_choice = Prompt.ask(
            "\n选择股票池范围",
            choices=["1", "2", "3"],
            default="1",
            show_default=True
        )
        
        universe_mapping = {
            "1": "TOP3000",
            "2": "TOP1200",
            "3": "both"
        }
        workflow_config["universe"] = universe_mapping[universe_choice]
        
        # 4. 因子生成阶数
        self.console.print("\n[bold]4. 因子生成阶数[/bold]")
        self.console.print("[dim]• 1阶: 基础因子（最快）")
        self.console.print("• 2阶: 基础 + 组合因子")
        self.console.print("• 3阶: 1+2阶 + 高级组合（推荐平衡）")
        self.console.print("• 4阶: 全部阶数（最全面但最慢）[/dim]")
        max_step = Prompt.ask(
            "\n最大因子阶数",
            choices=["1", "2", "3", "4"],
            default="3",
            show_default=True
        )
        workflow_config["max_step"] = int(max_step)
        
        # 5. 高级选项（可选）
        self.console.print("\n[bold]5. 高级选项[/bold]")
        if Confirm.ask("是否配置高级选项？", default=False):
            workflow_config.update(await self._show_advanced_options())
        else:
            # 从配置文件获取默认的高级配置
            max_factors = factor_config.get("max_factors_per_order")
            concurrent = factor_config.get("concurrent_simulations")
            enable_validation = validation_config.get("enable_quality_grading")
            enable_submission = submission_config.get("enable_batch_submission")
            
            if max_factors is None:
                raise ValueError("配置文件中缺少alpha_generation.max_alphas_per_step配置")
            if concurrent is None:
                raise ValueError("配置文件中缺少alpha_generation.concurrent_backtests配置")
            if enable_validation is None:
                raise ValueError("配置文件中缺少validation.enable_quality_grading配置")
            if enable_submission is None:
                raise ValueError("配置文件中缺少submission.enable_batch_submission配置")
                
            workflow_config.update({
                "max_factors_per_order": max_factors,
                "concurrent_simulations": concurrent,
                "enable_validation": enable_validation,
                "enable_submission": enable_submission
            })
            
        # 显示配置摘要
        await self._show_config_summary(workflow_config)
        
        if not Confirm.ask("\n确认使用以上配置开始挖掘？", default=True):
            return await self.show_workflow_options()  # 重新配置
            
        return workflow_config
        
    async def _show_advanced_options(self) -> Dict[str, Any]:
        """显示高级选项配置"""
        # 从配置文件获取默认值
        from .config_manager import ConfigManager
        config_manager = ConfigManager()
        config = await config_manager.load_config()
        
        alpha_config = config.get("alpha_generation", {})
        validation_config = config.get("validation", {})
        submission_config = config.get("submission", {})
        
        # 获取配置文件中的默认值，如果没有则报错
        default_max_alphas = alpha_config.get("max_alphas_per_step")
        default_concurrent = alpha_config.get("concurrent_backtests")
        default_validation = validation_config.get("enable_quality_grading")
        default_submission = submission_config.get("enable_batch_submission")
        
        if default_max_alphas is None:
            raise ValueError("配置文件中缺少alpha_generation.max_alphas_per_step配置")
        if default_concurrent is None:
            raise ValueError("配置文件中缺少alpha_generation.concurrent_backtests配置")
        if default_validation is None:
            raise ValueError("配置文件中缺少validation.enable_quality_grading配置")
        if default_submission is None:
            raise ValueError("配置文件中缺少submission.enable_batch_submission配置")
            
        advanced_config = {}
        
        # 每阶最大因子数
        max_alphas = Prompt.ask(
            f"每阶最大因子数 (100-5000)",
            default=str(default_max_alphas),
            show_default=True
        )
        try:
            advanced_config["max_alphas_per_step"] = int(max_alphas)
        except ValueError:
            raise ValueError(f"无效的Alpha数量: {max_alphas}")
            
        # 并发模拟数
        concurrent = Prompt.ask(
            f"并发回测数量 (1-10)",
            default=str(default_concurrent),
            show_default=True
        )
        try:
            advanced_config["concurrent_backtests"] = int(concurrent)
        except ValueError:
            raise ValueError(f"无效的并发数量: {concurrent}")
            
        # 是否启用检验
        advanced_config["enable_validation"] = Confirm.ask(
            "启用因子质量检验？", 
            default=default_validation
        )
        
        # 是否启用提交
        if advanced_config["enable_validation"]:
            advanced_config["enable_submission"] = Confirm.ask(
                "启用自动因子提交？", 
                default=default_submission
            )
        else:
            advanced_config["enable_submission"] = False
            
        return advanced_config
        
    async def _show_config_summary(self, config: Dict[str, Any]) -> None:
        """显示配置摘要"""
        summary_text = Text()
        summary_text.append("工作流配置摘要：\n\n", style="bold cyan")
        
        # 基础配置
        summary_text.append("📊 数据设置：\n", style="bold yellow")
        summary_text.append(f"  • 数据集: {config['dataset']}\n", style="white")
        summary_text.append(f"  • 地区: {config['region']}\n", style="white")
        summary_text.append(f"  • 股票池: {config['universe']}\n", style="white")
        summary_text.append(f"  • 最大步数: {config['max_step']}\n\n", style="white")
        
        # 高级配置
        summary_text.append("⚙️ 执行设置：\n", style="bold yellow")
        summary_text.append(f"  • 每步Alpha数: {config.get('max_alphas_per_step', 1000)}\n", style="white")
        summary_text.append(f"  • 并发数量: {config.get('concurrent_backtests', 3)}\n", style="white")
        summary_text.append(f"  • 质量检验: {'是' if config.get('enable_validation', True) else '否'}\n", style="white")
        summary_text.append(f"  • 自动提交: {'是' if config.get('enable_submission', True) else '否'}\n", style="white")
        
        self.console.print(Panel(
            summary_text,
            title="📋 配置摘要",
            border_style="green",
            box=box.ROUNDED
        ))
        
    def show_dataset_options(self):
        """显示数据集选项"""
        options_text = Text()
        options_text.append("📊 可用数据集：\n\n", style="bold cyan")
        options_text.append("1. analyst4 - 分析师预测数据集 (推荐)\n", style="green")
        options_text.append("   • 包含分析师评级、预测等高质量数据\n", style="dim")
        options_text.append("   • 适合基本面因子挖掘\n\n", style="dim")
        
        options_text.append("2. pv15 - 价格成交量数据集\n", style="green")
        options_text.append("   • 包含价格、成交量、技术指标数据\n", style="dim")
        options_text.append("   • 适合技术面因子挖掘\n\n", style="dim")
        
        options_text.append("3. both - 使用两个数据集\n", style="green")
        options_text.append("   • 同时使用两个数据集进行因子挖掘\n", style="dim")
        options_text.append("   • 更全面但耗时更长\n", style="dim")
        
        self.console.print(options_text)
        
    def show_region_options(self):
        """显示地区选项"""
        options_text = Text()
        options_text.append("🌍 可用交易地区：\n\n", style="bold cyan")
        options_text.append("1. USA - 美国市场 (推荐)\n", style="green")
        options_text.append("   • 流动性最好，数据质量最高\n", style="dim")
        options_text.append("   • 适合大部分因子策略\n\n", style="dim")
        
        options_text.append("2. EUR - 欧洲市场\n", style="green")
        options_text.append("   • 欧洲股票市场数据\n", style="dim")
        options_text.append("   • 时区和交易规则不同\n\n", style="dim")
        
        options_text.append("3. both - 两个市场\n", style="green")
        options_text.append("   • 同时在美国和欧洲市场测试\n", style="dim")
        options_text.append("   • 更全面但计算量翻倍\n", style="dim")
        
        self.console.print(options_text)
        
    def show_universe_options(self):
        """显示股票池选项"""
        options_text = Text()
        options_text.append("📈 可用股票池：\n\n", style="bold cyan")
        options_text.append("1. TOP3000 - 前3000支股票 (推荐)\n", style="green")
        options_text.append("   • 包含大中小盘股票\n", style="dim")
        options_text.append("   • 覆盖面广，因子适用性强\n\n", style="dim")
        
        options_text.append("2. TOP1200 - 前1200支股票\n", style="green")
        options_text.append("   • 主要是大中盘股票\n", style="dim")
        options_text.append("   • 流动性更好，回测更准确\n\n", style="dim")
        
        options_text.append("3. both - 两个股票池\n", style="green")
        options_text.append("   • 同时在两个股票池中测试\n", style="dim")
        options_text.append("   • 更全面验证因子有效性\n", style="dim")
        
        self.console.print(options_text)
        
    async def confirm_action(self, message: str, default: bool = True) -> bool:
        """确认操作"""
        return Confirm.ask(message, default=default)
        
    async def get_user_input(self, prompt: str, default: str = "") -> str:
        """获取用户输入"""
        return Prompt.ask(prompt, default=default)