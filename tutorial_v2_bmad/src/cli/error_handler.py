"""
错误处理器

提供友好的错误提示和处理建议，帮助用户解决问题。
"""
import traceback
from typing import Dict, Any, Optional
from rich.console import Console
from rich.panel import Panel
from rich.text import Text
from rich import box


class ErrorHandler:
    """错误处理器"""
    
    def __init__(self, console: Console):
        self.console = console
        self.error_solutions = self._init_error_solutions()
        
    def _init_error_solutions(self) -> Dict[str, Dict[str, str]]:
        """初始化错误解决方案库"""
        return {
            "FileNotFoundError": {
                "title": "文件未找到错误",
                "description": "系统无法找到指定的文件",
                "solutions": [
                    "检查文件路径是否正确",
                    "确认文件是否存在",
                    "检查文件权限设置",
                    "尝试重新创建配置文件"
                ]
            },
            
            "ConnectionError": {
                "title": "网络连接错误", 
                "description": "无法连接到WQ平台或网络服务",
                "solutions": [
                    "检查网络连接是否正常",
                    "确认WQ平台服务状态",
                    "检查防火墙设置",
                    "尝试使用VPN或更换网络",
                    "稍后重试连接"
                ]
            },
            
            "AuthenticationError": {
                "title": "身份认证错误",
                "description": "WQ平台认证失败",
                "solutions": [
                    "检查 user_info.txt 文件是否存在",
                    "确认用户名和密码是否正确",
                    "检查账户是否已激活",
                    "确认账户权限是否足够",
                    "联系WQ平台技术支持"
                ]
            },
            
            "ConfigurationError": {
                "title": "配置错误",
                "description": "系统配置文件存在问题",
                "solutions": [
                    "检查 config.json 文件格式",
                    "恢复默认配置文件",
                    "检查配置参数取值范围",
                    "重新生成配置文件"
                ]
            },
            
            "PermissionError": {
                "title": "权限错误",
                "description": "文件或目录访问权限不足",
                "solutions": [
                    "检查文件/目录权限设置",
                    "使用管理员权限运行",
                    "修改文件所有者",
                    "检查磁盘空间是否充足"
                ]
            },
            
            "MemoryError": {
                "title": "内存不足错误",
                "description": "系统内存不足，无法完成操作",
                "solutions": [
                    "关闭其他占用内存的程序",
                    "减少并发处理数量",
                    "降低批处理大小",
                    "增加系统虚拟内存",
                    "考虑分批处理数据"
                ]
            },
            
            "TimeoutError": {
                "title": "超时错误",
                "description": "操作执行时间过长，已超时",
                "solutions": [
                    "检查网络连接稳定性",
                    "增加超时时间设置",
                    "减少单次处理数据量",
                    "重试失败的操作",
                    "检查WQ平台服务状态"
                ]
            },
            
            "ValueError": {
                "title": "数值错误",
                "description": "输入的参数值不符合要求",
                "solutions": [
                    "检查输入参数的格式",
                    "确认数值范围是否正确",
                    "使用默认配置值",
                    "查看参数说明文档"
                ]
            }
        }
        
    def handle_general_error(self, error: Exception) -> None:
        """处理一般错误"""
        error_type = type(error).__name__
        error_message = str(error)
        
        # 获取错误解决方案
        solution_info = self.error_solutions.get(error_type, {
            "title": "未知错误",
            "description": "发生了未预期的错误",
            "solutions": [
                "重新启动程序",
                "检查系统环境",
                "查看错误日志",
                "联系技术支持"
            ]
        })
        
        # 构建错误显示内容
        error_text = Text()
        
        # 错误标题
        error_text.append(f"❌ {solution_info['title']}\n\n", style="bold red")
        
        # 错误描述
        error_text.append("📋 错误描述：\n", style="bold yellow")
        error_text.append(f"{solution_info['description']}\n", style="white")
        error_text.append(f"详细信息: {error_message}\n\n", style="dim")
        
        # 解决方案
        error_text.append("🔧 建议解决方案：\n", style="bold green")
        for i, solution in enumerate(solution_info["solutions"], 1):
            error_text.append(f"{i}. {solution}\n", style="cyan")
            
        # 显示错误面板
        self.console.print(Panel(
            error_text,
            title=f"🚨 错误处理 - {error_type}",
            border_style="red",
            box=box.ROUNDED
        ))
        
    def handle_authentication_error(self, error: Exception) -> None:
        """处理认证错误"""
        error_text = Text()
        error_text.append("🔐 WQ平台认证失败\n\n", style="bold red")
        
        error_text.append("📋 可能的原因：\n", style="bold yellow")
        causes = [
            "user_info.txt 文件不存在或格式错误",
            "用户名或密码不正确",
            "账户已被锁定或禁用",
            "网络连接问题",
            "WQ平台服务暂时不可用"
        ]
        
        for i, cause in enumerate(causes, 1):
            error_text.append(f"{i}. {cause}\n", style="white")
            
        error_text.append("\n🔧 解决步骤：\n", style="bold green")
        steps = [
            "检查 user_info.txt 文件格式：",
            "  username: '<EMAIL>'",
            "  password: 'your_password'",
            "",
            "确认WQ平台账户状态",
            "测试网络连接到 api.worldquantbrain.com",
            "如问题持续，请联系WQ平台技术支持"
        ]
        
        for step in steps:
            if step.startswith("  "):
                error_text.append(f"{step}\n", style="dim cyan")
            elif step == "":
                error_text.append("\n")
            else:
                error_text.append(f"• {step}\n", style="cyan")
                
        self.console.print(Panel(
            error_text,
            title="🚨 认证错误处理",
            border_style="red",
            box=box.ROUNDED
        ))
        
    def handle_configuration_error(self, error: Exception) -> None:
        """处理配置错误"""
        error_text = Text()
        error_text.append("⚙️ 系统配置错误\n\n", style="bold red")
        
        error_text.append(f"错误详情: {str(error)}\n\n", style="white")
        
        error_text.append("🔧 快速修复：\n", style="bold green")
        solutions = [
            "删除 src/config/config.json 文件，系统将自动重新创建",
            "检查 JSON 格式是否正确（注意逗号和引号）",
            "确认所有必需的配置项都存在",
            "使用默认配置值替换错误的参数"
        ]
        
        for i, solution in enumerate(solutions, 1):
            error_text.append(f"{i}. {solution}\n", style="cyan")
            
        error_text.append("\n📁 配置文件位置: src/config/config.json\n", style="dim")
        
        self.console.print(Panel(
            error_text,
            title="🚨 配置错误处理",
            border_style="red",
            box=box.ROUNDED
        ))
        
    def handle_network_error(self, error: Exception) -> None:
        """处理网络错误"""
        error_text = Text()
        error_text.append("🌐 网络连接错误\n\n", style="bold red")
        
        error_text.append("🔍 网络诊断：\n", style="bold yellow")
        diagnostics = [
            "检查互联网连接是否正常",
            "测试是否能访问 https://www.worldquant.com",
            "确认防火墙未阻止程序网络访问",
            "检查代理设置（如果使用代理）"
        ]
        
        for diagnostic in diagnostics:
            error_text.append(f"• {diagnostic}\n", style="white")
            
        error_text.append("\n🔧 解决方案：\n", style="bold green")
        solutions = [
            "重启网络连接",
            "更换网络环境（如使用手机热点）",
            "配置正确的代理设置",
            "联系网络管理员检查防火墙设置",
            "稍后重试（可能是临时网络问题）"
        ]
        
        for i, solution in enumerate(solutions, 1):
            error_text.append(f"{i}. {solution}\n", style="cyan")
            
        self.console.print(Panel(
            error_text,
            title="🚨 网络错误处理",
            border_style="red",
            box=box.ROUNDED
        ))
        
    def handle_permission_error(self, error: Exception) -> None:
        """处理权限错误"""
        error_text = Text()
        error_text.append("🔒 文件权限错误\n\n", style="bold red")
        
        error_text.append(f"错误详情: {str(error)}\n\n", style="white")
        
        error_text.append("🔧 权限解决方案：\n", style="bold green")
        
        # 根据操作系统提供不同建议
        import platform
        system = platform.system()
        
        if system == "Windows":
            solutions = [
                "右键选择'以管理员身份运行'",
                "检查文件是否被其他程序占用",
                "修改文件夹权限设置",
                "确保有足够的磁盘空间"
            ]
        else:  # macOS/Linux
            solutions = [
                "使用 sudo 命令运行程序",
                "修改文件权限: chmod 755 [文件路径]",
                "检查文件所有者: ls -la [文件路径]",
                "确保有足够的磁盘空间"
            ]
            
        for i, solution in enumerate(solutions, 1):
            error_text.append(f"{i}. {solution}\n", style="cyan")
            
        self.console.print(Panel(
            error_text,
            title="🚨 权限错误处理",
            border_style="red",
            box=box.ROUNDED
        ))
        
    def show_debug_info(self, error: Exception) -> None:
        """显示调试信息（开发模式）"""
        debug_text = Text()
        debug_text.append("🐛 调试信息\n\n", style="bold magenta")
        
        # 错误类型和消息
        debug_text.append(f"错误类型: {type(error).__name__}\n", style="yellow")
        debug_text.append(f"错误消息: {str(error)}\n\n", style="white")
        
        # 堆栈跟踪
        debug_text.append("📋 堆栈跟踪:\n", style="bold cyan")
        stack_trace = traceback.format_exc()
        debug_text.append(f"{stack_trace}\n", style="dim")
        
        self.console.print(Panel(
            debug_text,
            title="🔍 开发者调试信息",
            border_style="magenta",
            box=box.ROUNDED
        ))
        
    def show_error_code(self, error_code: str, message: str) -> None:
        """显示错误代码和消息"""
        error_text = Text()
        error_text.append(f"错误代码: {error_code}\n", style="bold red")
        error_text.append(f"错误消息: {message}\n", style="white")
        
        self.console.print(Panel(
            error_text,
            title="❌ 系统错误",
            border_style="red"
        ))
        
    def handle_startup_error(self, error: Exception) -> None:
        """处理启动时的错误"""
        error_text = Text()
        error_text.append("🚀 系统启动失败\n\n", style="bold red")
        
        error_text.append("系统在启动过程中遇到错误，请检查以下项目：\n\n", style="white")
        
        checklist = [
            "Python 版本是否 >= 3.9.13",
            "是否在正确的目录中运行程序",
            "依赖包是否正确安装",
            "配置文件是否存在且格式正确",
            "必要的目录是否存在（data, logs 等）"
        ]
        
        for item in checklist:
            error_text.append(f"• {item}\n", style="cyan")
            
        error_text.append(f"\n详细错误: {str(error)}\n", style="dim")
        
        self.console.print(Panel(
            error_text,
            title="🚨 启动错误",
            border_style="red",
            box=box.ROUNDED
        ))