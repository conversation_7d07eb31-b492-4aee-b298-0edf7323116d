"""
Rich控制台用户界面

提供美观的控制台输出，包括欢迎横幅、进度条、表格展示等。
"""
from typing import Dict, List, Any
from rich.console import Console
from rich.panel import Panel
from rich.table import Table
from rich.text import Text
from rich import box


class ConsoleUI:
    """控制台用户界面"""
    
    def __init__(self):
        self.console = Console()
        
    def show_welcome_banner(self):
        """显示欢迎横幅"""
        banner_text = Text()
        banner_text.append("🚀 WQ因子挖掘系统", style="bold blue")
        banner_text.append("\n")
        banner_text.append("自动化Alpha因子挖掘和提交平台", style="green")
        banner_text.append("\n\n")
        banner_text.append("版本: v2.0 | MVP原型", style="dim")
        
        self.console.print(Panel(
            banner_text,
            title="欢迎使用",
            border_style="blue",
            box=box.ROUNDED
        ))
        
    def show_info(self, message: str):
        """显示信息消息"""
        self.console.print(f"[blue]ℹ️  {message}[/blue]")
        
    def show_success(self, message: str):
        """显示成功消息"""
        self.console.print(f"[green]{message}[/green]")
        
    def show_warning(self, message: str):
        """显示警告消息"""
        self.console.print(f"[yellow]⚠️  {message}[/yellow]")
        
    def show_error(self, message: str):
        """显示错误消息"""
        self.console.print(f"[red]{message}[/red]")
        
    def show_status_panel(self, status_data: Dict[str, Any]):
        """显示系统状态面板"""
        status_text = Text()
        
        for key, value in status_data.items():
            status_text.append(f"{key}: ", style="bold cyan")
            
            # 根据状态设置颜色
            if key == "系统状态" and value == "运行正常":
                status_text.append(f"{value}\n", style="green")
            elif key == "数据库连接" and value == "正常":
                status_text.append(f"{value}\n", style="green")
            elif key == "会话状态" and value == "未登录":
                status_text.append(f"{value}\n", style="yellow")
            else:
                status_text.append(f"{value}\n", style="white")
                
        self.console.print(Panel(
            status_text,
            title="📊 系统状态",
            border_style="cyan",
            box=box.ROUNDED
        ))
        
    def show_statistics_table(self, stats_data: Dict[str, Any]):
        """显示统计信息表格"""
        table = Table(title="📈 系统统计", show_header=True, header_style="bold magenta")
        table.add_column("指标", style="cyan", width=15)
        table.add_column("数值", style="green", width=10)
        table.add_column("状态", style="yellow", width=10)
        
        for metric, value in stats_data.items():
            if metric == "成功率":
                # 根据成功率设置状态
                rate = float(value.replace('%', '')) if isinstance(value, str) else 0
                if rate >= 80:
                    status = "优秀"
                elif rate >= 60:
                    status = "良好"
                elif rate >= 40:
                    status = "一般"
                else:
                    status = "需改进"
            else:
                status = "正常" if value > 0 else "无数据"
                
            table.add_row(metric, str(value), status)
            
        self.console.print(table)
        
    def show_results_table(self, results: List[Dict[str, Any]], title: str = "处理结果"):
        """显示结果表格"""
        if not results:
            self.show_warning("📋 暂无数据显示")
            return
            
        table = Table(title=title, show_header=True, header_style="bold magenta")
        
        # 动态添加列（基于第一行数据）
        if results:
            for column in results[0].keys():
                table.add_column(column, style="cyan")
                
        # 添加数据行
        for row in results:
            table.add_row(*[str(value) for value in row.values()])
            
        self.console.print(table)
        
    def show_factor_generation_results(self, results: Dict[str, Dict]):
        """显示因子生成结果"""
        table = Table(title="🏭 因子生成结果", show_header=True)
        table.add_column("阶数", style="cyan", width=10)
        table.add_column("生成数量", style="green", width=10)
        table.add_column("回测完成", style="yellow", width=10)
        table.add_column("平均Sharpe", style="magenta", width=12)
        table.add_column("状态", style="blue", width=10)
        
        for order, data in results.items():
            sharpe_value = data.get('avg_sharpe', 0)
            sharpe_str = f"{sharpe_value:.3f}" if sharpe_value > 0 else "N/A"
            
            # 根据Sharpe比率设置状态
            if sharpe_value >= 1.5:
                status = "优秀"
            elif sharpe_value >= 1.0:
                status = "良好"
            elif sharpe_value > 0:
                status = "一般"
            else:
                status = "待处理"
                
            table.add_row(
                f"{order}阶因子",
                str(data.get("generated", 0)),
                str(data.get("backtested", 0)),
                sharpe_str,
                status
            )
            
        self.console.print(table)
        
    def show_validation_results(self, results: Dict[str, Any]):
        """显示检验结果"""
        table = Table(title="🔍 因子检验结果", show_header=True)
        table.add_column("检验项目", style="cyan", width=15)
        table.add_column("通过数量", style="green", width=10)
        table.add_column("失败数量", style="red", width=10)
        table.add_column("通过率", style="yellow", width=10)
        table.add_column("平均值", style="magenta", width=10)
        
        validation_items = [
            ("自相关性检验", results.get("self_correlation", {})),
            ("生产相关性检验", results.get("prod_correlation", {})),
            ("综合质量评估", results.get("quality_assessment", {}))
        ]
        
        for item_name, item_data in validation_items:
            passed = item_data.get("passed", 0)
            failed = item_data.get("failed", 0)
            total = passed + failed
            pass_rate = f"{(passed/total*100):.1f}%" if total > 0 else "N/A"
            avg_value = f"{item_data.get('average', 0):.3f}" if item_data.get('average') else "N/A"
            
            table.add_row(item_name, str(passed), str(failed), pass_rate, avg_value)
            
        self.console.print(table)
        
    def show_submission_results(self, results: Dict[str, Any]):
        """显示提交结果"""
        table = Table(title="📤 因子提交结果", show_header=True)
        table.add_column("提交状态", style="cyan", width=12)
        table.add_column("数量", style="green", width=8)
        table.add_column("百分比", style="yellow", width=10)
        table.add_column("说明", style="blue", width=20)
        
        total = results.get("total", 0)
        
        status_items = [
            ("成功提交", results.get("success", 0), "green"),
            ("提交失败", results.get("failed", 0), "red"),
            ("等待提交", results.get("pending", 0), "yellow"),
            ("跳过提交", results.get("skipped", 0), "dim")
        ]
        
        for status, count, color in status_items:
            percentage = f"{(count/total*100):.1f}%" if total > 0 else "0%"
            
            if status == "成功提交":
                description = "已成功提交到WQ平台"
            elif status == "提交失败":
                description = "提交过程中出现错误"
            elif status == "等待提交":
                description = "排队等待提交"
            else:
                description = "质量不符合要求"
                
            table.add_row(
                f"[{color}]{status}[/{color}]",
                str(count),
                percentage,
                description
            )
            
        self.console.print(table)
        
    def print_separator(self, title: str = ""):
        """打印分隔线"""
        if title:
            self.console.rule(f"[bold blue]{title}[/bold blue]")
        else:
            self.console.rule()
            
    def clear_screen(self):
        """清屏"""
        self.console.clear()