"""
配置管理器

负责加载、验证和管理系统配置。严格依赖 config.json 文件，不支持硬编码默认配置。
"""
import json
import os
from typing import Dict, Any
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "src/config/config.json"):
        self.config_path = config_path
        self.config = {}
        
    async def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config = json.load(f)
                
            # 验证配置
            await self.validate_config()
            return self.config
            
        except FileNotFoundError:
            raise ValueError(
                f"配置文件不存在: {self.config_path}\n"
                f"请确保配置文件 config.json 存在并包含所有必需的配置项。\n"
                f"参考文档: CONFIG_GUIDE.md"
            )
            
        except json.JSONDecodeError as e:
            raise ValueError(f"配置文件格式错误: {e}")
            

        
    async def save_config(self, config: Dict[str, Any]) -> None:
        """保存配置到文件"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
        except Exception as e:
            raise ValueError(f"保存配置文件失败: {e}")
            
    async def validate_config(self) -> bool:
        """验证配置的有效性"""
        required_sections = [
            "alpha_generation",
            "validation", 
            "submission",
            "database",
            "ui"
        ]
        
        for section in required_sections:
            if section not in self.config:
                raise ValueError(f"配置缺少必需的部分: {section}")
                
        # 验证具体配置项
        await self._validate_alpha_generation()
        await self._validate_validation_config()
        await self._validate_submission_config()
        await self._validate_database_config()
        await self._validate_quick_start_config()
        
        return True
        
    async def _validate_alpha_generation(self) -> None:
        """验证Alpha生成配置"""
        ag_config = self.config.get("alpha_generation", {})
        
        # 检查最大Alpha数
        max_alphas = ag_config.get("max_alphas_per_step")
        if max_alphas is None:
            raise ValueError("配置文件中缺少alpha_generation.max_alphas_per_step")
        if max_alphas <= 0 or max_alphas > 10000:
            raise ValueError("max_alphas_per_step 必须在 1-10000 之间")
            
        # 检查时间窗口
        time_windows = ag_config.get("time_windows")
        if time_windows is None:
            raise ValueError("配置文件中缺少alpha_generation.time_windows")
        if not time_windows or any(w <= 0 for w in time_windows):
            raise ValueError("time_windows 必须包含正整数")
            
        # 检查并发数
        concurrent = ag_config.get("concurrent_backtests")
        if concurrent is None:
            raise ValueError("配置文件中缺少alpha_generation.concurrent_backtests")
        if concurrent <= 0 or concurrent > 10:
            raise ValueError("concurrent_backtests 必须在 1-10 之间")
            
    async def _validate_validation_config(self) -> None:
        """验证检验配置"""
        val_config = self.config.get("validation", {})
        
        # 检查阈值
        self_threshold = val_config.get("self_correlation_threshold")
        prod_threshold = val_config.get("prod_correlation_threshold")
        
        if self_threshold is None:
            raise ValueError("配置文件中缺少validation.self_correlation_threshold")
        if prod_threshold is None:
            raise ValueError("配置文件中缺少validation.prod_correlation_threshold")
            
        if not (0 < self_threshold <= 1):
            raise ValueError("self_correlation_threshold 必须在 0-1 之间")
            
        if not (0 < prod_threshold <= 1):
            raise ValueError("prod_correlation_threshold 必须在 0-1 之间")
            
        # 检查模式
        modes = val_config.get("modes")
        if modes is None:
            raise ValueError("配置文件中缺少validation.modes")
        valid_modes = ["USER", "CONSULTANT"]
        if not all(mode in valid_modes for mode in modes):
            raise ValueError(f"modes 必须为 {valid_modes} 中的值")
            
    async def _validate_submission_config(self) -> None:
        """验证提交配置"""
        sub_config = self.config.get("submission", {})
        
        # 检查重试次数
        max_retries = sub_config.get("max_retries")
        if max_retries is None:
            raise ValueError("配置文件中缺少submission.max_retries")
        if max_retries < 0 or max_retries > 10:
            raise ValueError("max_retries 必须在 0-10 之间")
            
        # 检查超时时间
        timeout = sub_config.get("timeout_minutes")
        if timeout is None:
            raise ValueError("配置文件中缺少submission.timeout_minutes")
        if timeout <= 0 or timeout > 120:
            raise ValueError("timeout_minutes 必须在 1-120 之间")
            
    async def _validate_database_config(self) -> None:
        """验证数据库配置"""
        db_config = self.config.get("database", {})
        
        # 检查数据库路径
        db_path = db_config.get("path")
        if not db_path:
            raise ValueError("配置文件中缺少 database.path 配置项或值为空")
            
        # 确保数据库目录存在
        db_dir = os.path.dirname(db_path)
        if db_dir:
            os.makedirs(db_dir, exist_ok=True)
            
    async def _validate_quick_start_config(self) -> None:
        """验证快速开始配置"""
        if "quick_start" not in self.config:
            return  # 快速开始配置是可选的
            
        qs_config = self.config["quick_start"]
        
        # 验证默认数据集
        default_dataset = qs_config.get("default_dataset")
        available_datasets = self.config.get("datasets")
        if not available_datasets:
            raise ValueError("配置文件中缺少 datasets 配置项")
        if default_dataset and default_dataset not in available_datasets:
            raise ValueError(f"quick_start.default_dataset '{default_dataset}' 不在可用数据集中: {available_datasets}")
            
        # 验证默认地区
        default_region = qs_config.get("default_region")
        available_regions = self.config.get("regions")
        if not available_regions:
            raise ValueError("配置文件中缺少 regions 配置项")
        if default_region and default_region not in available_regions:
            raise ValueError(f"quick_start.default_region '{default_region}' 不在可用地区中: {available_regions}")
            
        # 验证默认股票池
        default_universe = qs_config.get("default_universe")
        available_universes = self.config.get("universes")
        if not available_universes:
            raise ValueError("配置文件中缺少 universes 配置项")
        if default_universe and default_universe not in available_universes:
            raise ValueError(f"quick_start.default_universe '{default_universe}' 不在可用股票池中: {available_universes}")
            
        # 验证默认最大阶数
        default_max_step = qs_config.get("default_max_step")
        if default_max_step is None:
            raise ValueError("配置文件中缺少quick_start.default_max_step")
        if not isinstance(default_max_step, int) or default_max_step < 1 or default_max_step > 4:
            raise ValueError("quick_start.default_max_step 必须是1-4之间的整数")
            
    def get_config(self, key: str, default: Any = None) -> Any:
        """获取配置项"""
        keys = key.split('.')
        value = self.config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
                
        return value
        
    async def update_config(self, key: str, value: Any) -> None:
        """更新配置项"""
        keys = key.split('.')
        config = self.config
        
        # 导航到目标位置
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
            
        # 设置值
        config[keys[-1]] = value
        
        # 验证并保存
        await self.validate_config()
        await self.save_config(self.config)
        
    def get_datasets(self) -> list:
        """获取可用数据集列表"""
        datasets = self.config.get("datasets")
        if not datasets:
            raise ValueError("配置文件中缺少 datasets 配置项")
        return datasets
        
    def get_regions(self) -> list:
        """获取可用地区列表"""
        regions = self.config.get("regions")
        if not regions:
            raise ValueError("配置文件中缺少 regions 配置项")
        return regions
        
    def get_universes(self) -> list:
        """获取可用股票池列表"""
        universes = self.config.get("universes")
        if not universes:
            raise ValueError("配置文件中缺少 universes 配置项")
        return universes
        
    def get_alpha_generation_config(self) -> Dict[str, Any]:
        """获取Alpha生成配置"""
        config = self.config.get("alpha_generation")
        if not config:
            raise ValueError("配置文件中缺少 alpha_generation 配置项")
        return config
        
    def get_validation_config(self) -> Dict[str, Any]:
        """获取检验配置"""
        config = self.config.get("validation")
        if not config:
            raise ValueError("配置文件中缺少 validation 配置项")
        return config
        
    def get_submission_config(self) -> Dict[str, Any]:
        """获取提交配置"""
        config = self.config.get("submission")
        if not config:
            raise ValueError("配置文件中缺少 submission 配置项")
        return config
        
    def get_quick_start_config(self) -> Dict[str, Any]:
        """获取快速开始配置"""
        config = self.config.get("quick_start")
        if not config:
            raise ValueError("配置文件中缺少 quick_start 配置项")
        return config