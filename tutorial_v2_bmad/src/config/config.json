{"_config_info": {"description": "WQ因子挖掘系统配置文件", "version": "2.0.0", "last_updated": "2024", "注意事项": "修改此文件后需要重启系统生效。所有配置项都是必需的，请勿删除。"}, "system": {"_description": "系统基本信息配置", "name": "WQ因子挖掘系统", "version": "2.0.0", "environment": "development", "min_python_version": [3, 9, 13], "_注释": {"name": "系统显示名称", "version": "系统版本号", "environment": "运行环境：development(开发)/production(生产)/testing(测试)", "min_python_version": "最低Python版本要求，格式：[主版本, 次版本, 修订版本]"}}, "_数据源配置说明": "以下配置定义了系统支持的数据源选项", "datasets": ["analyst4", "pv15"], "_datasets_说明": {"analyst4": "分析师预测数据集 - 包含分析师评级、预测等高质量基本面数据", "pv15": "价格成交量数据集 - 包含价格、成交量、技术指标等市场数据", "作用": "用户可以在这些数据集中选择，影响因子挖掘的数据基础"}, "regions": ["USA", "EUR"], "_regions_说明": {"USA": "美国市场 - 流动性最好，数据质量最高，推荐使用", "EUR": "欧洲市场 - 时区和交易规则与美国不同", "作用": "定义因子挖掘和回测的地理市场范围"}, "universes": ["TOP3000", "TOP1200"], "_universes_说明": {"TOP3000": "前3000支股票 - 包含大中小盘，覆盖面广，因子适用性强", "TOP1200": "前1200支股票 - 主要是大中盘股，流动性更好，回测更准确", "作用": "定义股票池范围，影响因子的适用股票数量"}, "quick_start": {"_description": "快速开始模式的默认配置 - 用户点击'快速开始'时使用这些设置", "default_dataset": "analyst4", "default_region": "USA", "default_universe": "TOP3000", "default_max_step": 3, "_配置说明": {"default_dataset": "默认数据集，必须是datasets中的一个选项", "default_region": "默认地区，必须是regions中的一个选项", "default_universe": "默认股票池，必须是universes中的一个选项", "default_max_step": "默认最大Alpha步数，1-4之间，推荐3（平衡性能和效果）", "修改建议": "如果经常使用特定配置，可以修改这些默认值来提高效率"}}, "alpha_generation": {"_description": "Alpha生成相关配置 - 控制Alpha挖掘的核心参数", "max_alphas_per_step": 1000, "time_windows": [5, 22, 66, 120, 240], "concurrent_backtests": 3, "enable_checkpoint": true, "checkpoint_interval": 100, "min_quality_score": 0.3, "_配置说明": {"max_alphas_per_step": "每步最大生成Alpha数量，范围1-10000，建议1000（平衡质量和数量）", "time_windows": "时间窗口列表（交易日），用于时序Alpha计算，可自定义添加或删除", "concurrent_backtests": "并发回测数量，范围1-10，根据机器性能调整，建议3", "enable_checkpoint": "是否启用断点续传，true可在中断后继续执行", "checkpoint_interval": "检查点保存间隔（处理的Alpha数量），建议100", "min_quality_score": "最小质量分数阈值，0-1之间，低于此分数的Alpha被过滤", "性能提示": "concurrent_backtests越大越快，但占用内存也越多，不建议超过5"}}, "validation": {"_description": "Alpha质量检验配置 - 决定哪些Alpha可以提交", "self_correlation_threshold": 0.7, "prod_correlation_threshold": 0.7, "concurrent_checks": 3, "enable_quality_grading": true, "modes": ["USER", "CONSULTANT"], "_配置说明": {"self_correlation_threshold": "自相关性阈值，0-1之间，越小要求越严格，0.7为推荐值", "prod_correlation_threshold": "生产相关性阈值，0-1之间，越小要求越严格，0.7为推荐值", "concurrent_checks": "并发检验数量，1-10之间，建议3", "enable_quality_grading": "是否启用质量分级（A/B/C/D等级），建议启用", "modes": "检验模式，USER=用户模式，CONSULTANT=顾问模式，影响检验标准", "调优建议": "阈值越低筛选越严格，通过率越低但质量越高"}}, "quality_evaluation": {"_description": "Alpha质量评估配置 - 控制质量评估算法的参数", "weights": {"return": 0.35, "risk": 0.25, "stability": 0.25, "complexity": 0.15}, "benchmarks": {"target_annual_return": 0.1, "target_sharpe_ratio": 2.0, "max_drawdown_limit": 0.2, "target_win_rate": 0.55, "max_complexity_score": 50}, "thresholds": {"min_acceptable_score": 0.3, "good_score_threshold": 0.6, "excellent_score_threshold": 0.8, "min_sharpe_ratio": 0.5, "max_acceptable_drawdown": 0.25}, "_配置说明": {"weights": "各评估维度的权重，总和应为1.0", "benchmarks": "评估基准值，用于计算各维度分数", "thresholds": "质量阈值，用于分级和筛选", "调优建议": "根据投资偏好调整权重，保守投资者可提高risk权重"}}, "checkpoint_management": {"_description": "断点续传配置 - 控制任务状态保存和恢复", "base_dir": "checkpoints", "auto_save_interval": 60, "max_checkpoints": 10, "backup_enabled": true, "backup_dir": "backups", "_配置说明": {"base_dir": "检查点文件存储目录", "auto_save_interval": "自动保存间隔（秒），建议60秒", "max_checkpoints": "最大保留检查点数量，超过后删除旧文件", "backup_enabled": "是否启用检查点备份", "backup_dir": "备份文件存储目录", "磁盘使用": "每个检查点约几KB，总占用量很小"}}, "submission": {"_description": "Alpha提交配置 - 控制向WQ平台提交Alpha的行为", "max_retries": 5, "timeout_minutes": 30, "sort_by_correlation": true, "enable_batch_submission": true, "_配置说明": {"max_retries": "最大重试次数，0-10之间，网络不稳定时可适当增加", "timeout_minutes": "单个Alpha提交超时时间（分钟），1-120之间，WQ平台限制30分钟", "sort_by_correlation": "是否按相关性排序提交，true优先提交低相关性Alpha", "enable_batch_submission": "是否启用批量提交，true可同时提交多个Alpha", "注意": "timeout_minutes不宜超过30分钟，这是WQ平台的硬性限制"}}, "database": {"_description": "数据库配置 - 本地SQLite数据库存储Alpha和结果", "path": "data/wq.db", "enable_auto_backup": true, "backup_interval_hours": 24, "_配置说明": {"path": "数据库文件路径，相对于项目根目录", "enable_auto_backup": "是否启用自动备份，强烈建议启用", "backup_interval_hours": "备份间隔（小时），建议24小时备份一次", "存储内容": "Alpha表达式、回测结果、检验结果、提交状态等"}}, "ui": {"_description": "用户界面配置 - 控制CLI界面的显示效果", "theme": "default", "show_progress": true, "auto_refresh_interval": 1.0, "enable_colors": true, "_配置说明": {"theme": "界面主题，目前只支持default", "show_progress": "是否显示进度条，建议启用", "auto_refresh_interval": "自动刷新间隔（秒），建议1.0秒", "enable_colors": "是否启用彩色输出，建议启用（除非终端不支持）"}}, "logging": {"_description": "日志配置 - 控制系统日志的记录和管理", "level": "INFO", "file": "logs/app.log", "max_size_mb": 100, "backup_count": 5, "_配置说明": {"level": "日志级别：DEBUG/INFO/WARNING/ERROR，INFO适合日常使用", "file": "日志文件路径，相对于项目根目录", "max_size_mb": "单个日志文件最大大小（MB），超过后自动轮转", "backup_count": "保留的备份日志文件数量", "磁盘占用": "总磁盘占用 = max_size_mb × (backup_count + 1)"}}, "performance": {"_description": "性能限制配置 - 防止系统过度占用资源", "max_memory_gb": 4, "max_cpu_percent": 80, "timeout_seconds": 300, "_配置说明": {"max_memory_gb": "最大内存使用限制（GB），根据机器配置调整", "max_cpu_percent": "最大CPU使用率限制（%），建议不超过80%", "timeout_seconds": "单个操作最大超时时间（秒），防止死锁", "调优建议": "根据机器性能和其他应用的资源需求来调整这些限制"}}, "session_management": {"_description": "WQ平台会话管理配置 - 控制认证和会话生命周期", "base_url": "https://api.worldquantbrain.com", "timeout": 30, "max_retries": 3, "ssl_verify": false, "credentials_file": "user_info.txt", "auto_refresh": true, "refresh_before_expiry_seconds": 30, "max_concurrent_sessions": 5, "session_pool_size": 10, "_配置说明": {"base_url": "WQ平台API基础URL，通常不需要修改", "timeout": "网络请求超时时间（秒），建议30秒", "max_retries": "认证失败时的最大重试次数，建议3次", "ssl_verify": "是否验证SSL证书，WQ平台建议设为false", "credentials_file": "用户凭证文件路径，格式：username: 'email' password: 'pass'", "auto_refresh": "是否启用会话自动刷新，强烈建议启用", "refresh_before_expiry_seconds": "会话过期前多少秒开始刷新，建议30秒", "max_concurrent_sessions": "最大并发会话数，根据账户权限调整", "session_pool_size": "HTTP连接池大小，建议10个连接", "权限说明": "会话权限由WQ平台返回，包括CONSULTANT、MULTI_SIMULATION等"}}, "rate_limiting": {"_description": "API频率控制配置 - 防止触发WQ平台速率限制", "global": {"calls": 60, "period": 60}, "authentication": {"calls": 5, "period": 300}, "simulation": {"calls": 10, "period": 60}, "alpha_submission": {"calls": 5, "period": 60}, "data_query": {"calls": 30, "period": 60}, "_配置说明": {"calls": "时间窗口内允许的最大调用次数", "period": "时间窗口长度（秒）", "global": "全局限制，所有API调用的总限制", "authentication": "认证API的独立限制，避免账户被锁定", "simulation": "仿真API限制，因子回测相关", "alpha_submission": "因子提交API限制，最重要的限制", "data_query": "数据查询API限制，获取字段和数据", "调整建议": "根据账户类型和使用模式谨慎调整，过高可能导致账户限制"}}, "_配置文件使用说明": {"修改配置": "1. 直接编辑此JSON文件 2. 重启系统使配置生效 3. 系统会自动验证配置有效性", "配置验证": "系统启动时会验证所有配置项，如有错误会显示具体错误信息", "备份建议": "修改前建议备份此文件，避免配置错误导致系统无法启动", "常用修改": {"快速开始": "修改quick_start部分可定制一键启动的默认行为", "性能调优": "修改alpha_generation.concurrent_backtests和performance部分", "质量控制": "修改validation和quality_evaluation部分的阈值来控制Alpha质量要求", "资源限制": "修改performance部分来适配不同的硬件配置"}}}