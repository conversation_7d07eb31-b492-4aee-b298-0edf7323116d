"""
WQ Alpha挖掘系统 - 核心模块

本模块包含Alpha挖掘引擎的核心功能：
- Alpha生成算法
- 并发回测系统 
- 质量评估机制
- 断点续传功能
"""

from .alpha_models import (
    Alpha, AlphaConfig, BacktestResult, QualityScore, AlphaStatus, QualityGrade,
    create_alpha, create_backtest_result, create_quality_score
)
from .alpha_engine import AlphaEngine
from .backtest_scheduler import BacktestScheduler
from .quality_evaluator import QualityEvaluator
from .checkpoint_manager import CheckpointManager

__all__ = [
    'Alpha',
    'AlphaConfig', 
    'BacktestResult',
    'QualityScore',
    'AlphaStatus',
    'QualityGrade',
    'AlphaEngine',
    'BacktestScheduler',
    'QualityEvaluator', 
    'CheckpointManager',
    'create_alpha',
    'create_backtest_result',
    'create_quality_score'
]