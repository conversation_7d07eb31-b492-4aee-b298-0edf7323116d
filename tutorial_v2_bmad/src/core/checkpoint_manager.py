"""
Alpha挖掘断点续传管理器

负责Alpha挖掘过程的状态管理和断点续传：
- 保存挖掘进度和状态
- 管理已完成的Alpha记录
- 支持任务中断后的恢复
- 提供进度查询和统计
"""

import json
import time
import shutil
from dataclasses import dataclass, asdict
from pathlib import Path
from typing import Dict, List, Optional, Set, Any
from datetime import datetime

from .alpha_models import Alpha, BacktestResult, QualityScore, AlphaConfig, AlphaStatus


@dataclass
class CheckpointData:
    """检查点数据"""
    checkpoint_id: str
    created_at: datetime
    config: Dict[str, Any]
    
    # 进度信息
    current_step: int
    total_steps: int
    completed_alphas: List[str]  # 已完成的Alpha表达式
    failed_alphas: List[str]     # 失败的Alpha表达式
    
    # 统计信息
    stats: Dict[str, Any]
    
    # 状态信息
    status: str  # running, paused, completed, failed
    last_updated: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        data['last_updated'] = self.last_updated.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'CheckpointData':
        """从字典创建"""
        data['created_at'] = datetime.fromisoformat(data['created_at'])
        data['last_updated'] = datetime.fromisoformat(data['last_updated'])
        return cls(**data)


class CheckpointManager:
    """断点续传管理器"""
    
    def __init__(self, base_dir: str = "checkpoints"):
        self.base_dir = Path(base_dir)
        self.base_dir.mkdir(exist_ok=True)
        
        # 当前检查点
        self.current_checkpoint: Optional[CheckpointData] = None
        self.checkpoint_file: Optional[Path] = None
        
        # 配置
        self.auto_save_interval = 60  # 自动保存间隔（秒）
        self.max_checkpoints = 10     # 最大保留检查点数
        
        print(f"💾 CheckpointManager初始化: {self.base_dir}")
    
    def create_checkpoint(self, config: AlphaConfig, total_steps: int = 4) -> str:
        """创建新的检查点"""
        checkpoint_id = f"alpha_mining_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        checkpoint_data = CheckpointData(
            checkpoint_id=checkpoint_id,
            created_at=datetime.now(),
            config=self._config_to_dict(config),
            current_step=0,
            total_steps=total_steps,
            completed_alphas=[],
            failed_alphas=[],
            stats={
                'total_generated': 0,
                'total_backtested': 0,
                'total_failed': 0,
                'generation_time': 0.0,
                'backtest_time': 0.0,
                'start_time': time.time()
            },
            status="running",
            last_updated=datetime.now()
        )
        
        self.current_checkpoint = checkpoint_data
        self.checkpoint_file = self.base_dir / f"{checkpoint_id}.json"
        
        self.save_checkpoint()
        
        print(f"📝 创建检查点: {checkpoint_id}")
        return checkpoint_id
    
    def save_checkpoint(self):
        """保存当前检查点"""
        if not self.current_checkpoint or not self.checkpoint_file:
            return
        
        self.current_checkpoint.last_updated = datetime.now()
        
        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(self.current_checkpoint.to_dict(), f, indent=2, ensure_ascii=False)
            
            print(f"💾 检查点已保存: {self.checkpoint_file.name}")
            
        except Exception as e:
            print(f"❌ 保存检查点失败: {str(e)}")
    
    def load_checkpoint(self, checkpoint_id: str) -> bool:
        """加载指定检查点"""
        checkpoint_file = self.base_dir / f"{checkpoint_id}.json"
        
        if not checkpoint_file.exists():
            print(f"❌ 检查点文件不存在: {checkpoint_file}")
            return False
        
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            self.current_checkpoint = CheckpointData.from_dict(data)
            self.checkpoint_file = checkpoint_file
            
            print(f"📂 加载检查点成功: {checkpoint_id}")
            print(f"   当前步骤: {self.current_checkpoint.current_step}/{self.current_checkpoint.total_steps}")
            print(f"   已完成Alpha: {len(self.current_checkpoint.completed_alphas)}")
            print(f"   失败Alpha: {len(self.current_checkpoint.failed_alphas)}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载检查点失败: {str(e)}")
            return False
    
    def update_progress(self, step: int, completed_alphas: List[str] = None, 
                       failed_alphas: List[str] = None, stats: Dict[str, Any] = None):
        """更新进度"""
        if not self.current_checkpoint:
            return
        
        self.current_checkpoint.current_step = step
        
        if completed_alphas is not None:
            self.current_checkpoint.completed_alphas.extend(completed_alphas)
        
        if failed_alphas is not None:
            self.current_checkpoint.failed_alphas.extend(failed_alphas)
        
        if stats is not None:
            self.current_checkpoint.stats.update(stats)
        
        # 自动保存
        self._auto_save()
    
    def mark_alpha_completed(self, alpha_expression: str):
        """标记Alpha为已完成"""
        if not self.current_checkpoint:
            return
        
        if alpha_expression not in self.current_checkpoint.completed_alphas:
            self.current_checkpoint.completed_alphas.append(alpha_expression)
            self._auto_save()
    
    def mark_alpha_failed(self, alpha_expression: str):
        """标记Alpha为失败"""
        if not self.current_checkpoint:
            return
        
        if alpha_expression not in self.current_checkpoint.failed_alphas:
            self.current_checkpoint.failed_alphas.append(alpha_expression)
            self._auto_save()
    
    def is_alpha_processed(self, alpha_expression: str) -> bool:
        """检查Alpha是否已处理"""
        if not self.current_checkpoint:
            return False
        
        return (alpha_expression in self.current_checkpoint.completed_alphas or 
                alpha_expression in self.current_checkpoint.failed_alphas)
    
    def get_completed_alphas(self) -> Set[str]:
        """获取已完成的Alpha集合"""
        if not self.current_checkpoint:
            return set()
        
        return set(self.current_checkpoint.completed_alphas)
    
    def get_failed_alphas(self) -> Set[str]:
        """获取失败的Alpha集合"""
        if not self.current_checkpoint:
            return set()
        
        return set(self.current_checkpoint.failed_alphas)
    
    def filter_unprocessed_alphas(self, alphas: List[Alpha]) -> List[Alpha]:
        """过滤未处理的Alpha"""
        if not self.current_checkpoint:
            return alphas
        
        processed = set(self.current_checkpoint.completed_alphas + self.current_checkpoint.failed_alphas)
        unprocessed = [alpha for alpha in alphas if alpha.alpha_expression not in processed]
        
        skipped_count = len(alphas) - len(unprocessed)
        if skipped_count > 0:
            print(f"🔄 跳过已处理的Alpha: {skipped_count} 个")
        
        return unprocessed
    
    def set_status(self, status: str):
        """设置检查点状态"""
        if self.current_checkpoint:
            self.current_checkpoint.status = status
            self.save_checkpoint()
    
    def complete_checkpoint(self):
        """标记检查点为完成"""
        if self.current_checkpoint:
            self.current_checkpoint.status = "completed"
            self.current_checkpoint.stats['end_time'] = time.time()
            self.save_checkpoint()
            print(f"✅ 检查点已完成: {self.current_checkpoint.checkpoint_id}")
    
    def get_resume_info(self) -> Optional[Dict[str, Any]]:
        """获取恢复信息"""
        if not self.current_checkpoint:
            return None
        
        total_processed = len(self.current_checkpoint.completed_alphas) + len(self.current_checkpoint.failed_alphas)
        progress_percent = (self.current_checkpoint.current_step / self.current_checkpoint.total_steps) * 100
        
        return {
            'checkpoint_id': self.current_checkpoint.checkpoint_id,
            'current_step': self.current_checkpoint.current_step,
            'total_steps': self.current_checkpoint.total_steps,
            'progress_percent': progress_percent,
            'completed_alphas': len(self.current_checkpoint.completed_alphas),
            'failed_alphas': len(self.current_checkpoint.failed_alphas),
            'total_processed': total_processed,
            'status': self.current_checkpoint.status,
            'last_updated': self.current_checkpoint.last_updated.isoformat(),
            'config': self.current_checkpoint.config,
            'stats': self.current_checkpoint.stats
        }
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """列出所有检查点"""
        checkpoints = []
        
        for checkpoint_file in self.base_dir.glob("*.json"):
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                checkpoints.append({
                    'checkpoint_id': data['checkpoint_id'],
                    'created_at': data['created_at'],
                    'status': data['status'],
                    'current_step': data['current_step'],
                    'total_steps': data['total_steps'],
                    'completed_alphas': len(data['completed_alphas']),
                    'file_path': str(checkpoint_file)
                })
                
            except Exception as e:
                print(f"⚠️ 读取检查点失败: {checkpoint_file} - {str(e)}")
        
        # 按创建时间排序
        checkpoints.sort(key=lambda x: x['created_at'], reverse=True)
        
        return checkpoints
    
    def cleanup_old_checkpoints(self):
        """清理旧的检查点"""
        checkpoints = self.list_checkpoints()
        
        if len(checkpoints) <= self.max_checkpoints:
            return
        
        # 保留最新的检查点，删除多余的
        to_delete = checkpoints[self.max_checkpoints:]
        
        for checkpoint in to_delete:
            try:
                file_path = Path(checkpoint['file_path'])
                file_path.unlink()
                print(f"🗑️ 删除旧检查点: {checkpoint['checkpoint_id']}")
            except Exception as e:
                print(f"❌ 删除检查点失败: {str(e)}")
    
    def export_results(self, output_dir: str = "results"):
        """导出结果数据"""
        if not self.current_checkpoint:
            return
        
        output_path = Path(output_dir)
        output_path.mkdir(exist_ok=True)
        
        # 导出已完成的Alpha列表
        completed_file = output_path / f"{self.current_checkpoint.checkpoint_id}_completed.txt"
        with open(completed_file, 'w', encoding='utf-8') as f:
            for alpha_expr in self.current_checkpoint.completed_alphas:
                f.write(f"{alpha_expr}\n")
        
        # 导出失败的Alpha列表
        failed_file = output_path / f"{self.current_checkpoint.checkpoint_id}_failed.txt"
        with open(failed_file, 'w', encoding='utf-8') as f:
            for alpha_expr in self.current_checkpoint.failed_alphas:
                f.write(f"{alpha_expr}\n")
        
        # 导出统计信息
        stats_file = output_path / f"{self.current_checkpoint.checkpoint_id}_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(self.current_checkpoint.stats, f, indent=2)
        
        print(f"📤 结果已导出到: {output_path}")
    
    def backup_checkpoint(self, backup_dir: str = "backups"):
        """备份当前检查点"""
        if not self.checkpoint_file or not self.checkpoint_file.exists():
            return
        
        backup_path = Path(backup_dir)
        backup_path.mkdir(exist_ok=True)
        
        backup_file = backup_path / f"{self.checkpoint_file.name}.backup"
        shutil.copy2(self.checkpoint_file, backup_file)
        
        print(f"💾 检查点已备份: {backup_file}")
    
    def _auto_save(self):
        """自动保存检查点"""
        if not self.current_checkpoint:
            return
        
        # 检查是否需要自动保存
        time_since_update = (datetime.now() - self.current_checkpoint.last_updated).total_seconds()
        
        if time_since_update >= self.auto_save_interval:
            self.save_checkpoint()
    
    def _config_to_dict(self, config: AlphaConfig) -> Dict[str, Any]:
        """转换配置为字典"""
        return {
            'dataset': config.dataset,
            'region': config.region,
            'universe': config.universe,
            'max_step': config.max_step,
            'max_alphas_per_step': config.max_alphas_per_step,
            'time_windows': config.time_windows,
            'concurrent_backtests': config.concurrent_backtests,
            'enable_quality_check': config.enable_quality_check,
            'min_quality_score': config.min_quality_score,
            'enable_checkpoint': config.enable_checkpoint,
            'checkpoint_interval': config.checkpoint_interval
        }
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self.current_checkpoint:
            return {}
        
        total_processed = len(self.current_checkpoint.completed_alphas) + len(self.current_checkpoint.failed_alphas)
        success_rate = len(self.current_checkpoint.completed_alphas) / total_processed * 100 if total_processed > 0 else 0
        
        return {
            'checkpoint_id': self.current_checkpoint.checkpoint_id,
            'status': self.current_checkpoint.status,
            'current_step': self.current_checkpoint.current_step,
            'total_steps': self.current_checkpoint.total_steps,
            'progress_percent': (self.current_checkpoint.current_step / self.current_checkpoint.total_steps) * 100,
            'completed_alphas': len(self.current_checkpoint.completed_alphas),
            'failed_alphas': len(self.current_checkpoint.failed_alphas),
            'total_processed': total_processed,
            'success_rate': success_rate,
            'runtime': time.time() - self.current_checkpoint.stats.get('start_time', time.time()),
            'last_updated': self.current_checkpoint.last_updated.isoformat()
        }