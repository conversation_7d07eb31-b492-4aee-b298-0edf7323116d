"""
Alpha并发回测调度器

负责管理Alpha的并发回测任务：
- 基于asyncio的并发任务管理
- 回测任务队列和工作池管理
- SessionManager集成认证
- 失败重试和错误处理
- 实时进度监控
"""

import asyncio
import time
import random
from dataclasses import dataclass
from typing import List, Dict, Optional, Callable, Any
from datetime import datetime
from pathlib import Path

from .alpha_models import Alpha, BacktestResult, AlphaStatus, create_backtest_result
from ..lib.session_manager import SessionManager, Session
from ..lib.exceptions import SessionManagerError, NetworkError
from ..lib.logger import get_logger
from ..lib.wq_api_client import WorldQuantAPIClient, SimulationRequest


@dataclass
class BacktestTask:
    """回测任务"""
    alpha: Alpha
    task_id: str
    created_at: datetime
    retry_count: int = 0
    max_retries: int = 3
    
    def can_retry(self) -> bool:
        """检查是否可以重试"""
        return self.retry_count < self.max_retries


class BacktestScheduler:
    """Alpha并发回测调度器"""
    
    def __init__(self, session_manager: SessionManager, 
                 max_concurrent: int = 3,
                 progress_callback: Optional[Callable] = None):
        self.session_manager = session_manager
        self.max_concurrent = max_concurrent
        self.progress_callback = progress_callback
        self.logger = get_logger(__name__)
        
        # 初始化API客户端
        self.api_client = WorldQuantAPIClient(session_manager)
        
        # 任务管理
        self.pending_tasks: asyncio.Queue = asyncio.Queue()
        self.active_tasks: Dict[str, BacktestTask] = {}
        self.completed_tasks: List[BacktestTask] = []
        self.failed_tasks: List[BacktestTask] = []
        
        # 统计信息
        self.stats = {
            'total_submitted': 0,
            'total_completed': 0,
            'total_failed': 0,
            'start_time': None,
            'active_workers': 0,
            'processing_time': 0.0
        }
        
        # 控制标志
        self._shutdown = False
        self._workers: List[asyncio.Task] = []
        
        self.logger.info(f"📊 BacktestScheduler初始化: 最大并发={max_concurrent}")
    
    async def submit_batch_backtests(self, alphas: List[Alpha]) -> List[BacktestResult]:
        """批量提交回测任务"""
        if not alphas:
            return []
        
        self.logger.info(f"🔄 开始批量回测: {len(alphas)} 个Alpha")
        self.stats['start_time'] = time.time()
        self.stats['total_submitted'] = len(alphas)
        
        # 创建回测任务
        tasks = []
        for i, alpha in enumerate(alphas):
            task = BacktestTask(
                alpha=alpha,
                task_id=f"backtest_{int(time.time())}_{i}",
                created_at=datetime.now()
            )
            tasks.append(task)
            await self.pending_tasks.put(task)
        
        # 启动工作池
        await self._start_workers()
        
        # 等待所有任务完成
        await self._wait_for_completion()
        
        # 停止工作池
        await self._stop_workers()
        
        # 收集结果
        results = []
        for alpha in alphas:
            # 查找对应的回测结果
            result = None
            for task in self.completed_tasks + self.failed_tasks:
                if task.alpha.alpha_expression == alpha.alpha_expression:
                    if hasattr(task, 'result'):
                        result = task.result
                    break
            
            if result is None:
                # 创建失败结果
                result = create_backtest_result(alpha.alpha_expression, f"failed_{int(time.time())}")
                result.status = "failed"
                result.error_message = "回测任务未找到结果"
                result.end_time = datetime.now()
            
            results.append(result)
        
        self._print_batch_summary()
        return results
    
    async def _start_workers(self):
        """启动工作池"""
        self._shutdown = False
        self._workers = []
        
        for i in range(self.max_concurrent):
            worker = asyncio.create_task(self._worker(f"worker_{i}"))
            self._workers.append(worker)
        
        self.stats['active_workers'] = len(self._workers)
        self.logger.info(f"🚀 启动 {len(self._workers)} 个回测工作线程")
    
    async def _stop_workers(self):
        """停止工作池"""
        self._shutdown = True
        
        # 取消所有工作线程
        for worker in self._workers:
            worker.cancel()
        
        # 等待工作线程结束
        await asyncio.gather(*self._workers, return_exceptions=True)
        
        self._workers.clear()
        self.stats['active_workers'] = 0
        self.logger.info("🛑 所有回测工作线程已停止")
    
    async def _worker(self, worker_name: str):
        """工作线程"""
        try:
            while not self._shutdown:
                try:
                    # 获取任务
                    task = await asyncio.wait_for(
                        self.pending_tasks.get(), 
                        timeout=1.0
                    )
                    
                    # 执行回测
                    await self._execute_backtest(task, worker_name)
                    
                except asyncio.TimeoutError:
                    # 超时继续循环
                    continue
                except Exception as e:
                    self.logger.error(f"❌ 工作线程 {worker_name} 异常: {str(e)}")
                    continue
        
        except asyncio.CancelledError:
            self.logger.info(f"🛑 工作线程 {worker_name} 被取消")
    
    async def _execute_backtest(self, task: BacktestTask, worker_name: str):
        """执行单个回测任务"""
        start_time = time.time()
        
        try:
            # 更新Alpha状态
            task.alpha.status = AlphaStatus.BACKTESTING
            self.active_tasks[task.task_id] = task
            
            self.logger.debug(f"⚡ {worker_name} 开始回测: {task.alpha.alpha_expression[:50]}...")
            
            # 执行回测
            result = await self._perform_backtest(task.alpha)
            
            # 存储结果
            task.result = result
            
            # 更新Alpha状态
            if result.is_successful():
                task.alpha.status = AlphaStatus.COMPLETED
                self.completed_tasks.append(task)
                self.stats['total_completed'] += 1
                self.logger.info(f"✅ {worker_name} 回测成功: Sharpe={result.sharpe_ratio:.3f}")
            else:
                task.alpha.status = AlphaStatus.FAILED
                await self._handle_backtest_failure(task, result.error_message)
        
        except Exception as e:
            await self._handle_backtest_failure(task, str(e))
        
        finally:
            # 清理
            self.active_tasks.pop(task.task_id, None)
            self.stats['processing_time'] += time.time() - start_time
            
            # 进度回调
            if self.progress_callback:
                await self._call_progress_callback()
    
    async def _perform_backtest(self, alpha: Alpha) -> BacktestResult:
        """执行Alpha回测"""
        try:
            # 确保会话有效
            auth_session = await self.session_manager.get_valid_session()
            if not auth_session:
                result = create_backtest_result(alpha.alpha_expression, f"failed_{int(time.time())}")
                result.status = "failed"
                result.error_message = "无法获取有效的认证会话"
                result.end_time = datetime.now()
                return result
            
            # 创建回测结果
            result = create_backtest_result(alpha.alpha_expression, f"backtest_{int(time.time())}")
            
            # 调用真实的WQ API进行回测
            await self._real_backtest(result)
            
            return result
            
        except SessionManagerError as e:
            result = create_backtest_result(alpha.alpha_expression, f"failed_{int(time.time())}")
            result.status = "failed"
            result.error_message = f"会话错误: {str(e)}"
            result.end_time = datetime.now()
            return result
    
    async def _real_backtest(self, result: BacktestResult):
        """真实的WQ平台API回测 - 使用统一API客户端"""
        
        # 创建模拟请求 - 参数参考WQ API规范
        simulation_request = SimulationRequest(
            alpha_expression=result.alpha_expression,
            region="USA",  # 默认值，可以从配置获取
            universe="TOP3000",  # 默认值，可以从配置获取
            delay=1,
            decay=6,  # 衰减参数：必须>0，参考值6
            neutralization="SUBINDUSTRY",
            truncation=0.08,
            pasteurization="ON",
            unit_handling="VERIFY",
            nan_handling="ON",
            language="FASTEXPR",
            visualization=False
        )
        
        try:
            # 使用统一API客户端进行模拟
            simulation_result = await self.api_client.simulate_alpha(simulation_request)
            
            # 更新BacktestResult
            result.status = simulation_result.status
            result.alpha_id = simulation_result.alpha_id
            result.annual_return = simulation_result.annual_return
            result.sharpe_ratio = simulation_result.sharpe_ratio
            result.max_drawdown = simulation_result.max_drawdown
            result.volatility = simulation_result.volatility
            result.total_return = simulation_result.total_return
            result.information_ratio = simulation_result.information_ratio
            result.error_message = simulation_result.error_message
            result.duration_seconds = simulation_result.duration_seconds
            result.end_time = datetime.now()
            
        except Exception as e:
            result.status = "failed"
            result.error_message = f"回测异常: {str(e)}"
            result.end_time = datetime.now()
            result.duration_seconds = 0.0
    
    async def _handle_backtest_failure(self, task: BacktestTask, error_message: str):
        """处理回测失败"""
        task.retry_count += 1
        task.alpha.status = AlphaStatus.FAILED
        
        self.logger.warning(f"❌ 回测失败 (重试{task.retry_count}/{task.max_retries}): {error_message}")
        
        if task.can_retry():
            # 重新提交任务
            await self.pending_tasks.put(task)
            self.logger.info(f"🔄 重新提交回测任务: {task.task_id}")
        else:
            # 标记为最终失败
            self.failed_tasks.append(task)
            self.stats['total_failed'] += 1
            self.logger.error(f"💀 回测最终失败: {task.task_id}")
    
    async def _wait_for_completion(self):
        """等待所有任务完成"""
        while True:
            # 检查是否还有任务
            has_pending = not self.pending_tasks.empty()
            has_active = len(self.active_tasks) > 0

            if not has_pending and not has_active:
                break

            # 等待一段时间再检查
            await asyncio.sleep(0.5)

            # 更新进度
            if self.progress_callback:
                await self._call_progress_callback()

    async def _call_progress_callback(self):
        """调用进度回调"""
        try:
            if self.progress_callback:
                progress_info = {
                    'total': self.stats['total_submitted'],
                    'completed': self.stats['total_completed'],
                    'failed': self.stats['total_failed'],
                    'active': len(self.active_tasks),
                    'pending': self.pending_tasks.qsize()
                }
                await self.progress_callback(progress_info)
        except Exception as e:
            self.logger.warning(f"⚠️ 进度回调异常: {str(e)}")

    def _print_batch_summary(self):
        """打印批量回测摘要"""
        total_time = time.time() - \
            self.stats['start_time'] if self.stats['start_time'] else 0
        success_rate = self.stats['total_completed'] / self.stats['total_submitted'] * \
            100 if self.stats['total_submitted'] > 0 else 0

        self.logger.info(f"📊 批量回测完成摘要:")
        self.logger.info(f"   总任务数: {self.stats['total_submitted']}")
        self.logger.info(
            f"   成功: {self.stats['total_completed']} ({success_rate:.1f}%)")
        self.logger.info(f"   失败: {self.stats['total_failed']}")
        self.logger.info(f"   总耗时: {total_time:.1f}秒")
        self.logger.info(
            f"   平均处理时间: {self.stats['processing_time']/self.stats['total_submitted']:.1f}秒/个")
        self.logger.info(
            f"   并发效率: {self.stats['processing_time']/total_time:.1f}x")

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'active_tasks': len(self.active_tasks),
            'pending_tasks': self.pending_tasks.qsize(),
            'completed_tasks': len(self.completed_tasks),
            'failed_tasks': len(self.failed_tasks)
        }

    def reset(self):
        """重置调度器状态"""
        self.active_tasks.clear()
        self.completed_tasks.clear()
        self.failed_tasks.clear()
        
        # 清空队列
        while not self.pending_tasks.empty():
            try:
                self.pending_tasks.get_nowait()
            except asyncio.QueueEmpty:
                break
        
        # 重置统计
        self.stats = {
            'total_submitted': 0,
            'total_completed': 0,
            'total_failed': 0,
            'start_time': None,
            'active_workers': 0,
            'processing_time': 0.0
        }
        
        self.logger.info("🔄 BacktestScheduler已重置")