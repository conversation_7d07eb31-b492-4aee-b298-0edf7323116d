"""
Alpha挖掘引擎核心类

协调Alpha生成、回测和质量评估的完整流程：
- 多步Alpha渐进式生成
- 并发回测调度
- 质量评估和筛选
- 断点续传支持
"""

import asyncio
import json
import time
from pathlib import Path
from typing import List, Dict, Optional, Any, Set
from datetime import datetime

from .alpha_models import Alpha, AlphaConfig, BacktestResult, QualityScore, AlphaStatus
from .alpha_generator import AlphaGenerator
from ..lib.session_manager import SessionManager
from ..lib.exceptions import SessionManagerError
from ..lib.logger import get_logger
from ..lib.wq_api_client import WorldQuantAPIClient, SimulationRequest


class AlphaEngine:
    """Alpha挖掘引擎核心类"""

    def __init__(self, config: AlphaConfig, session_manager: SessionManager):
        self.config = config
        self.session_manager = session_manager

        # 初始化API客户端
        self.api_client = WorldQuantAPIClient(session_manager)
        self.generator = AlphaGenerator(config)
        self.logger = get_logger()

        # 状态管理
        self.current_alphas: List[Alpha] = []
        self.completed_alphas: Set[str] = set()
        self.failed_alphas: Set[str] = set()

        # 统计信息
        self.stats = {
            'total_generated': 0,
            'total_backtested': 0,
            'total_failed': 0,
            'generation_time': 0.0,
            'backtest_time': 0.0,
            'start_time': None,
            'end_time': None
        }
        
        # 断点续传
        self.checkpoint_file = Path("checkpoints") / f"alpha_engine_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        self.checkpoint_file.parent.mkdir(exist_ok=True)
        
        self.logger.info(f"🚀 AlphaEngine 初始化完成")
        self.logger.info(
            f"📊 配置: {config.dataset}/{config.region}/{config.universe}")
        self.logger.info(
            f"🎯 目标: {config.max_step}步Alpha, 每步{config.max_alphas_per_step}个")
    
    async def generate_first_step_alphas(self, config: Optional[AlphaConfig] = None) -> List[Alpha]:
        """生成第1步Alpha"""
        config = config or self.config
        
        self.logger.info(f"🔨 开始生成第1步Alpha...")
        start_time = time.time()
        
        try:
            alphas = self.generator.generate_first_step_alphas(config.max_alphas_per_step)
            
            # 更新状态
            for alpha in alphas:
                alpha.status = AlphaStatus.GENERATED
            
            self.current_alphas.extend(alphas)
            self.stats['total_generated'] += len(alphas)
            self.stats['generation_time'] += time.time() - start_time
            
            self.logger.info(f"✅ 第1步Alpha生成完成: {len(alphas)} 个")
            return alphas
            
        except Exception as e:
            self.logger.error(f"❌ 第1步Alpha生成失败: {str(e)}")
            raise
    
    async def generate_higher_step_alphas(self, base_alphas: List[Alpha], 
                                          step: int) -> List[Alpha]:
        """生成高步Alpha"""
        self.logger.info(f"🔨 开始生成第 {step} 步Alpha...")
        start_time = time.time()
        
        try:
            # 筛选可用的基础Alpha
            available_alphas = [alpha for alpha in base_alphas 
                               if alpha.status == AlphaStatus.COMPLETED and 
                               alpha.quality_score and alpha.quality_score >= self.config.min_quality_score]
            
            if not available_alphas:
                self.logger.warning(f"⚠️ 没有可用的基础Alpha，跳过第 {step} 步Alpha生成")
                return []
            
            self.logger.info(f"📊 可用基础Alpha: {len(available_alphas)} 个")
            
            alphas = self.generator.generate_higher_step_alphas(
                available_alphas, step, self.config.max_alphas_per_step
            )
            
            # 更新状态
            for alpha in alphas:
                alpha.status = AlphaStatus.GENERATED
            
            self.current_alphas.extend(alphas)
            self.stats['total_generated'] += len(alphas)
            self.stats['generation_time'] += time.time() - start_time
            
            self.logger.info(f"✅ 第 {step} 步Alpha生成完成: {len(alphas)} 个")
            return alphas
            
        except Exception as e:
            self.logger.error(f"❌ 第 {step} 步Alpha生成失败: {str(e)}")
            raise
    
    async def batch_backtest_alphas(self, alphas: List[Alpha]) -> List[BacktestResult]:
        """批量回测Alpha"""
        if not alphas:
            return []
        
        self.logger.info(f"📈 开始批量回测: {len(alphas)} 个Alpha")
        self.logger.info(f"🔄 并发数: {self.config.concurrent_backtests}")
        self.logger.info(f"⚙️ 日志格式: [时间戳][线程ID][任务名] 状态: Alpha表达式 | 详细信息")
        self.logger.info("=" * 80)
        
        start_time = time.time()
        results = []
        
        # 创建回测任务信号量
        semaphore = asyncio.Semaphore(self.config.concurrent_backtests)
        
        async def backtest_single_alpha(alpha: Alpha) -> Optional[BacktestResult]:
            """单个Alpha回测"""
            # 获取本地logger实例
            logger = get_logger()

            # 获取任务名称（用于日志标识）
            task_name = asyncio.current_task().get_name(
            ) if asyncio.current_task() else "unknown"

            async with semaphore:
                try:
                    alpha.status = AlphaStatus.BACKTESTING
                    
                    # 开始回测日志
                    alpha_short = alpha.alpha_expression[:20] + (
                        "..." if len(alpha.alpha_expression) > 20 else "")
                    logger.debug(f"🔄 [{task_name}] 开始回测: {alpha_short}")

                    # 调用真实的WQ平台API进行回测
                    result = await self._real_backtest(alpha)
                    
                    if result and result.is_successful():
                        alpha.status = AlphaStatus.COMPLETED
                        self.completed_alphas.add(alpha.alpha_expression)
                        self.stats['total_backtested'] += 1
                        duration = f"{result.duration_seconds:.2f}s"
                        returns = f"{result.annual_return:.2%}" if result.annual_return else "N/A"
                        logger.info(
                            f"✅ [{task_name}] 回测完成: {alpha_short} | 耗时:{duration} | 年化收益:{returns}")
                    else:
                        alpha.status = AlphaStatus.FAILED
                        self.failed_alphas.add(alpha.alpha_expression)
                        self.stats['total_failed'] += 1
                        error_msg = result.error_message if result and result.error_message else "回测结果异常: 无错误信息"
                        logger.warning(
                            f"❌ [{task_name}] 回测失败: {alpha_short} | 原因: {error_msg}")
                    
                    return result
                    
                except Exception as e:
                    alpha.status = AlphaStatus.FAILED
                    alpha.backtest_error = str(e)
                    self.failed_alphas.add(alpha.alpha_expression)
                    self.stats['total_failed'] += 1
                    alpha_short = alpha.alpha_expression[:20] + (
                        "..." if len(alpha.alpha_expression) > 20 else "")
                    logger.error(
                        f"❌ [{task_name}] 回测异常: {alpha_short} | 异常: {str(e)}")
                    return None
        
        # 并发执行回测，为每个任务分配名称
        tasks = []
        for i, alpha in enumerate(alphas):
            task_name = f"Alpha-{i+1:03d}"
            task = asyncio.create_task(
                backtest_single_alpha(alpha), name=task_name)
            tasks.append(task)

        task_results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 收集结果
        for result in task_results:
            if isinstance(result, BacktestResult):
                results.append(result)
        
        self.stats['backtest_time'] += time.time() - start_time
        
        success_count = len([r for r in results if r and r.is_successful()])
        failure_count = len(alphas) - success_count
        elapsed_time = time.time() - start_time

        self.logger.info("=" * 80)
        self.logger.info(
            f"📊 批量回测完成: 成功 {success_count}/{len(alphas)} 个 (失败 {failure_count} 个)")
        self.logger.info(
            f"⏱️ 总耗时: {elapsed_time:.2f}秒, 平均: {elapsed_time/len(alphas):.2f}秒/Alpha")
        self.logger.info(
            f"🎯 成功率: {success_count/len(alphas):.1%}")
        self.logger.info("=" * 80)
        
        return results
    
    async def _real_backtest(self, alpha: Alpha) -> BacktestResult:
        """真实的WQ平台API回测调用 - 使用统一API客户端"""
        import time

        # 创建模拟请求 - 参数参考WQ API规范
        simulation_request = SimulationRequest(
            alpha_expression=alpha.alpha_expression,
            region=self.config.region,
            universe=self.config.universe,
            delay=1,  # 延迟1天
            decay=6,  # 衰减参数：必须>0，参考值6
            neutralization="SUBINDUSTRY",  # 子行业中性化
            truncation=0.08,
            pasteurization="ON",
            unit_handling="VERIFY",
            nan_handling="ON",
            language="FASTEXPR",
            visualization=False
        )
        
        try:
            # 添加请求间隔，避免触发速率限制
            await asyncio.sleep(0.2)  # 200ms间隔

            # 使用统一API客户端进行模拟
            simulation_result = await self.api_client.simulate_alpha(simulation_request)

            # 转换为BacktestResult格式
            from .alpha_models import create_backtest_result
            result = create_backtest_result(
                alpha.alpha_expression, f"backtest_{int(time.time())}")

            # 复制模拟结果数据
            result.status = simulation_result.status
            result.success = simulation_result.is_successful()
            result.alpha_id = simulation_result.alpha_id
            result.annual_return = simulation_result.annual_return
            result.sharpe_ratio = simulation_result.sharpe_ratio
            result.max_drawdown = simulation_result.max_drawdown
            result.volatility = simulation_result.volatility
            result.total_return = simulation_result.total_return
            result.information_ratio = simulation_result.information_ratio
            result.error_message = simulation_result.error_message
            result.duration_seconds = simulation_result.duration_seconds
            result.end_time = datetime.now()

            return result

        except Exception as e:
            # 异常情况下创建失败的结果
            from .alpha_models import create_backtest_result
            result = create_backtest_result(
                alpha.alpha_expression, f"failed_{int(time.time())}")
            result.status = "failed"
            result.success = False
            result.error_message = f"回测异常: {str(e)}"
            result.end_time = datetime.now()
            result.duration_seconds = 0.0
            return result
    
    async def evaluate_alpha_quality(self, alpha: Alpha, 
                                    backtest_result: BacktestResult) -> QualityScore:
        """评估Alpha质量"""
        from .alpha_models import create_quality_score
        
        score = create_quality_score(alpha.alpha_expression)
        
        if not backtest_result or not backtest_result.is_successful():
            score.is_valid = False
            score.add_warning("回测失败")
            return score
        
        # 计算各项分数
        score.return_score = self._calculate_return_score(backtest_result)
        score.risk_score = self._calculate_risk_score(backtest_result)
        score.stability_score = self._calculate_stability_score(backtest_result)
        score.complexity_score = self._calculate_complexity_score(alpha)
        
        # 计算总分和等级
        score.calculate_overall_score()
        score.assign_grade()
        
        # 更新Alpha质量信息
        alpha.quality_score = score.overall_score
        alpha.quality_grade = score.grade
        
        return score
    
    def _calculate_return_score(self, result: BacktestResult) -> float:
        """计算收益得分"""
        if not result.annual_return:
            return 0.0
        
        # 年化收益率评分 (10%为满分)
        return max(0.0, min(1.0, result.annual_return / 0.1))
    
    def _calculate_risk_score(self, result: BacktestResult) -> float:
        """计算风险得分"""
        sharpe = result.sharpe_ratio or 0.0
        drawdown = result.max_drawdown or 1.0
        
        # 夏普比率评分 (2.0为满分)
        sharpe_score = max(0.0, min(1.0, sharpe / 2.0))
        
        # 回撤评分 (5%回撤为满分)
        drawdown_score = max(0.0, min(1.0, (0.05 - drawdown) / 0.05))
        
        return (sharpe_score + drawdown_score) / 2
    
    def _calculate_stability_score(self, result: BacktestResult) -> float:
        """计算稳定性得分"""
        win_rate = result.win_rate or 0.0
        volatility = result.volatility or 1.0
        
        # 胜率评分 (60%为满分)
        win_score = max(0.0, min(1.0, win_rate / 0.6))
        
        # 波动率评分 (15%波动率为满分)
        vol_score = max(0.0, min(1.0, (0.15 - volatility) / 0.15))
        
        return (win_score + vol_score) / 2
    
    def _calculate_complexity_score(self, alpha: Alpha) -> float:
        """计算复杂度得分"""
        complexity = alpha.get_complexity_score()
        
        # 适中复杂度得分最高
        if complexity < 0.3:
            return complexity / 0.3 * 0.8  # 太简单
        elif complexity > 0.7:
            return (1.0 - complexity) / 0.3 * 0.8  # 太复杂
        else:
            return 1.0  # 适中复杂度
    
    async def run_full_pipeline(self) -> Dict[str, Any]:
        """运行完整的Alpha挖掘流程"""
        self.logger.info("🚀 开始完整Alpha挖掘流程")
        self.logger.info("=" * 60)
        
        self.stats['start_time'] = datetime.now()
        all_alphas = []
        all_results = []
        
        try:
            # 逐步生成和回测
            for step in range(1, self.config.max_step + 1):
                self.logger.info(f"🎯 处理第 {step} 步Alpha")
                self.logger.info("-" * 40)
                
                # 生成Alpha
                if step == 1:
                    alphas = await self.generate_first_step_alphas()
                else:
                    # 使用上一步的高质量Alpha作为基础
                    base_alphas = [alpha for alpha in all_alphas 
                                  if alpha.quality_score and alpha.quality_score >= self.config.min_quality_score]
                    alphas = await self.generate_higher_step_alphas(base_alphas, step)
                
                if not alphas:
                    self.logger.warning(f"⚠️ 第 {step} 步没有生成Alpha，跳过")
                    continue
                
                # 批量回测
                results = await self.batch_backtest_alphas(alphas)
                
                # 质量评估
                self.logger.info(f"📊 开始质量评估...")
                for i, alpha in enumerate(alphas):
                    result = results[i] if i < len(results) else None
                    quality = await self.evaluate_alpha_quality(alpha, result)
                    
                    if quality.is_acceptable(self.config.min_quality_score):
                        self.logger.info(
                            f"✨ 高质量Alpha: {alpha.alpha_expression[:50]}... (分数: {quality.overall_score:.3f})")
                
                all_alphas.extend(alphas)
                all_results.extend(results)
                
                # 保存检查点
                if self.config.enable_checkpoint:
                    await self._save_checkpoint(all_alphas, all_results)
                
                # 统计信息
                quality_alphas = [alpha for alpha in alphas if alpha.quality_score and alpha.quality_score >= self.config.min_quality_score]
                self.logger.info(
                    f"📈 第 {step} 步统计: 生成 {len(alphas)}, 高质量 {len(quality_alphas)} 个")
            
            self.stats['end_time'] = datetime.now()
            
            # 最终统计
            total_duration = (self.stats['end_time'] - self.stats['start_time']).total_seconds()
            quality_alphas = [alpha for alpha in all_alphas if alpha.quality_score and alpha.quality_score >= self.config.min_quality_score]
            
            final_stats = {
                'total_alphas': len(all_alphas),
                'quality_alphas': len(quality_alphas),
                'success_rate': len(quality_alphas) / len(all_alphas) if all_alphas else 0,
                'total_duration': total_duration,
                'generation_time': self.stats['generation_time'],
                'backtest_time': self.stats['backtest_time'],
                'alphas_per_second': len(all_alphas) / total_duration if total_duration > 0 else 0
            }
            
            self.logger.info("🎉 Alpha挖掘流程完成！")
            self.logger.info("=" * 60)
            self.logger.info(f"📊 总计生成: {final_stats['total_alphas']} 个Alpha")
            self.logger.info(f"✨ 高质量Alpha: {final_stats['quality_alphas']} 个")
            self.logger.info(f"📈 成功率: {final_stats['success_rate']:.1%}")
            self.logger.info(f"⏱️ 总耗时: {total_duration:.1f} 秒")
            self.logger.info(
                f"🚀 生成效率: {final_stats['alphas_per_second']:.2f} Alpha/秒")
            
            return {
                'alphas': all_alphas,
                'results': all_results,
                'stats': final_stats
            }
            
        except Exception as e:
            self.logger.error(f"❌ Alpha挖掘流程异常: {str(e)}")
            raise
    
    async def resume_from_checkpoint(self, checkpoint_id: str) -> None:
        """从检查点恢复"""
        checkpoint_file = Path("checkpoints") / f"{checkpoint_id}.json"
        
        if not checkpoint_file.exists():
            raise FileNotFoundError(f"检查点文件不存在: {checkpoint_file}")
        
        self.logger.info(f"🔄 从检查点恢复: {checkpoint_file}")
        
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 恢复已完成的Alpha
            self.completed_alphas = set(data.get('completed_alphas', []))
            self.failed_alphas = set(data.get('failed_alphas', []))
            self.stats.update(data.get('stats', {}))
            
            self.logger.info(f"✅ 检查点恢复完成")
            self.logger.info(f"📊 已完成: {len(self.completed_alphas)} 个")
            self.logger.info(f"❌ 已失败: {len(self.failed_alphas)} 个")
            
        except Exception as e:
            self.logger.error(f"❌ 检查点恢复失败: {str(e)}")
            raise
    
    async def _save_checkpoint(self, alphas: List[Alpha], results: List[BacktestResult]):
        """保存检查点"""
        checkpoint_data = {
            'timestamp': datetime.now().isoformat(),
            'config': self.config.to_dict(),
            'completed_alphas': list(self.completed_alphas),
            'failed_alphas': list(self.failed_alphas),
            'stats': self.stats,
            'alphas_count': len(alphas),
            'results_count': len(results)
        }
        
        try:
            with open(self.checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"💾 检查点已保存: {self.checkpoint_file}")
            
        except Exception as e:
            self.logger.warning(f"⚠️ 检查点保存失败: {str(e)}")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            **self.stats,
            'current_alphas': len(self.current_alphas),
            'completed_alphas': len(self.completed_alphas),
            'failed_alphas': len(self.failed_alphas),
            'config': self.config.to_dict()
        }