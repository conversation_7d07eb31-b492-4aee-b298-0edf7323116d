"""
Alpha生成器

基于 src_xin/machine_lib.py 的成熟算法，实现：
- 操作符分类系统
- Alpha生成工厂
- 表达式验证和优化
"""

import random
import itertools
from typing import List, Dict, Set, Tuple, Optional, Any
from .alpha_models import Alpha, AlphaConfig, create_alpha


class OperatorLibrary:
    """操作符库管理"""
    
    # 基础操作符
    BASIC_OPS = [
        "log", "sqrt", "reverse", "inverse", "rank", "zscore", 
        "log_diff", "s_log_1p", "fraction", "quantile", "normalize", "scale_down"
    ]
    
    # 时间序列操作符
    TS_OPS = [
        "ts_rank", "ts_zscore", "ts_delta", "ts_sum", "ts_product",
        "ts_ir", "ts_std_dev", "ts_mean", "ts_arg_min", "ts_arg_max", 
        "ts_min_diff", "ts_max_diff", "ts_returns", "ts_scale"
    ]
    
    # 分组操作符
    GROUP_OPS = [
        "group_neutralize", "group_rank", "group_normalize", 
        "group_scale", "group_zscore"
    ]
    
    # 高级操作符 (Arsenal)
    ARSENAL_OPS = [
        "ts_moment", "ts_entropy", "ts_min_max_cps", "sigmoid",
        "ts_decay_exp_window", "ts_percentage", "vector_neut", "signed_power"
    ]
    
    # 数据字段 (基于 analyst4 数据集)
    DATA_FIELDS = [
        "open", "high", "low", "close", "volume", "vwap",
        "book_value_per_share", "cash_ratio", "current_ratio",
        "earnings_per_share", "enterprise_value", "ev_to_ebitda",
        "market_cap", "price_earnings_ratio", "price_to_book",
        "return_on_assets", "return_on_equity", "revenue_per_share"
    ]
    
    @classmethod
    def get_all_operators(cls) -> List[str]:
        """获取所有操作符"""
        return (cls.BASIC_OPS + cls.TS_OPS + 
                cls.GROUP_OPS + cls.ARSENAL_OPS)
    
    @classmethod
    def get_operators_by_type(cls, op_type: str) -> List[str]:
        """按类型获取操作符"""
        type_map = {
            'basic': cls.BASIC_OPS,
            'ts': cls.TS_OPS,
            'group': cls.GROUP_OPS,
            'arsenal': cls.ARSENAL_OPS
        }
        return type_map.get(op_type, [])
    
    @classmethod
    def is_time_series_op(cls, operator: str) -> bool:
        """判断是否为时间序列操作符"""
        return operator in cls.TS_OPS or operator in cls.ARSENAL_OPS


class ExpressionValidator:
    """表达式验证器"""
    
    def __init__(self, max_length: int = 500):
        self.max_length = max_length
        self.operators = set(OperatorLibrary.get_all_operators())
        self.fields = set(OperatorLibrary.DATA_FIELDS)
    
    def validate_expression(self, alpha_expression: str) -> Tuple[bool, List[str]]:
        """验证表达式
        
        Returns:
            (is_valid, error_messages)
        """
        errors = []
        
        # 基本检查
        if not alpha_expression or not alpha_expression.strip():
            errors.append("表达式不能为空")
            return False, errors
        
        # 长度检查
        if len(alpha_expression) > self.max_length:
            errors.append(f"表达式长度超过限制 ({self.max_length})")
        
        # 括号匹配检查
        if not self._check_parentheses(alpha_expression):
            errors.append("括号不匹配")
        
        # 语法基本检查
        if not self._basic_syntax_check(alpha_expression):
            errors.append("基本语法错误")
        
        return len(errors) == 0, errors
    
    def _check_parentheses(self, alpha_expression: str) -> bool:
        """检查括号是否匹配"""
        count = 0
        for char in alpha_expression:
            if char == '(':
                count += 1
            elif char == ')':
                count -= 1
                if count < 0:
                    return False
        return count == 0
    
    def _basic_syntax_check(self, alpha_expression: str) -> bool:
        """基本语法检查"""
        # 检查是否包含必要的字段或操作符
        has_field = any(field in alpha_expression for field in self.fields)
        has_operator = any(op in alpha_expression for op in self.operators)
        
        return has_field or has_operator
    
    def get_complexity_score(self, alpha_expression: str) -> float:
        """计算表达式复杂度"""
        if not alpha_expression:
            return 0.0
        
        # 长度复杂度
        length_score = min(len(alpha_expression) / 200, 1.0)
        
        # 操作符数量复杂度
        op_count = sum(1 for op in self.operators if op in alpha_expression)
        op_score = min(op_count / 5, 1.0)
        
        # 嵌套深度复杂度  
        max_depth = self._get_nesting_depth(alpha_expression)
        depth_score = min(max_depth / 10, 1.0)
        
        return (length_score + op_score + depth_score) / 3
    
    def _get_nesting_depth(self, alpha_expression: str) -> int:
        """计算嵌套深度"""
        max_depth = 0
        current_depth = 0
        
        for char in alpha_expression:
            if char == '(':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char == ')':
                current_depth -= 1
        
        return max_depth


class AlphaGenerator:
    """Alpha生成器核心类"""
    
    def __init__(self, config: AlphaConfig):
        self.config = config
        self.validator = ExpressionValidator(config.max_expression_length)
        self.operators = OperatorLibrary()
        self.generated_expressions: Set[str] = set()  # 去重用
        
    def generate_first_step_alphas(self, max_count: int = None) -> List[Alpha]:
        """生成第1步Alpha"""
        max_count = max_count or self.config.max_alphas_per_step
        alphas = []
        
        print(f"🔨 开始生成第1步Alpha，目标数量: {max_count}")
        
        # 1. 基础字段Alpha
        alphas.extend(self._generate_basic_field_alphas())
        
        # 2. 单操作符Alpha
        alphas.extend(self._generate_single_operator_alphas(max_count // 2))
        
        # 3. 时间序列Alpha
        alphas.extend(self._generate_time_series_alphas(max_count // 3))
        
        # 去重和验证
        alphas = self._deduplicate_and_validate(alphas)
        
        # 限制数量
        if len(alphas) > max_count:
            alphas = alphas[:max_count]
        
        print(f"✅ 第1步Alpha生成完成，实际生成: {len(alphas)} 个")
        return alphas
    
    def generate_higher_step_alphas(self, base_alphas: List[Alpha], 
                                    step: int, max_count: int = None) -> List[Alpha]:
        """生成高步Alpha"""
        max_count = max_count or self.config.max_alphas_per_step
        alphas = []
        
        print(f"🔨 开始生成第 {step} 步Alpha，基础Alpha数量: {len(base_alphas)}, 目标数量: {max_count}")
        
        if step < 2 or not base_alphas:
            return alphas
        
        # 随机选择基础Alpha进行组合
        base_expressions = [alpha.alpha_expression for alpha in base_alphas[:50]]  # 限制基础Alpha数量
        
        # 1. 二元操作符组合
        alphas.extend(self._generate_binary_combinations(base_expressions, step, max_count // 2))
        
        # 2. 复合时间序列Alpha
        alphas.extend(self._generate_composite_ts_alphas(base_expressions, step, max_count // 3))
        
        # 3. 分组操作Alpha
        alphas.extend(self._generate_group_operation_alphas(base_expressions, step, max_count // 4))
        
        # 去重和验证
        alphas = self._deduplicate_and_validate(alphas)
        
        # 限制数量
        if len(alphas) > max_count:
            alphas = alphas[:max_count]
        
        print(f"✅ 第 {step} 步Alpha生成完成，实际生成: {len(alphas)} 个")
        return alphas
    
    def _generate_basic_field_alphas(self) -> List[Alpha]:
        """生成基础字段Alpha"""
        alphas = []
        
        for field in self.operators.DATA_FIELDS:
            alpha_expression = field
            alpha = create_alpha(alpha_expression, 1, self.config)
            alpha.generation_method = "basic_field"
            alphas.append(alpha)
        
        return alphas
    
    def _generate_single_operator_alphas(self, max_count: int) -> List[Alpha]:
        """生成单操作符Alpha"""
        alphas = []
        count = 0
        
        fields = self.operators.DATA_FIELDS
        basic_ops = self.operators.BASIC_OPS
        
        for field in fields:
            if count >= max_count:
                break
                
            for op in basic_ops:
                if count >= max_count:
                    break
                
                alpha_expression = f"{op}({field})"
                alpha = create_alpha(alpha_expression, 1, self.config)
                alpha.generation_method = "single_operator"
                alphas.append(alpha)
                count += 1
        
        return alphas
    
    def _generate_time_series_alphas(self, max_count: int) -> List[Alpha]:
        """生成时间序列Alpha"""
        alphas = []
        count = 0
        
        fields = self.operators.DATA_FIELDS[:10]  # 限制字段数量
        ts_ops = self.operators.TS_OPS
        time_windows = self.config.time_windows
        
        for field in fields:
            if count >= max_count:
                break
                
            for op in ts_ops:
                if count >= max_count:
                    break
                    
                for window in time_windows:
                    if count >= max_count:
                        break
                    
                    if op in ["ts_rank", "ts_zscore", "ts_delta", "ts_mean"]:
                        alpha_expression = f"{op}({field}, {window})"
                    else:
                        alpha_expression = f"{op}({field})"
                    
                    alpha = create_alpha(alpha_expression, 1, self.config)
                    alpha.generation_method = "time_series"
                    alpha.time_window = window
                    alphas.append(alpha)
                    count += 1
        
        return alphas
    
    def _generate_binary_combinations(self, base_expressions: List[str], 
                                    step: int, max_count: int) -> List[Alpha]:
        """生成二元组合Alpha"""
        alphas = []
        count = 0
        
        # 二元操作符
        binary_ops = ["+", "-", "*", "/"]
        
        # 随机组合
        for _ in range(max_count * 2):  # 多生成一些再筛选
            if count >= max_count:
                break
            
            expr1 = random.choice(base_expressions)
            expr2 = random.choice(base_expressions)
            op = random.choice(binary_ops)
            
            if expr1 != expr2:  # 避免自己和自己运算
                alpha_expression = f"({expr1} {op} {expr2})"
                
                alpha = create_alpha(alpha_expression, step, self.config)
                alpha.generation_method = "binary_combination"
                alpha.base_alphas = [expr1, expr2]
                alphas.append(alpha)
                count += 1
        
        return alphas
    
    def _generate_composite_ts_alphas(self, base_expressions: List[str], 
                                     step: int, max_count: int) -> List[Alpha]:
        """生成复合时间序列Alpha"""
        alphas = []
        count = 0
        
        ts_ops = ["ts_rank", "ts_zscore", "ts_mean", "ts_sum"]
        time_windows = [5, 22, 66]
        
        for base_expr in base_expressions[:20]:  # 限制基础表达式数量
            if count >= max_count:
                break
                
            for op in ts_ops:
                if count >= max_count:
                    break
                    
                for window in time_windows:
                    if count >= max_count:
                        break
                    
                    alpha_expression = f"{op}({base_expr}, {window})"
                    
                    alpha = create_alpha(alpha_expression, step, self.config)
                    alpha.generation_method = "composite_ts"
                    alpha.base_alphas = [base_expr]
                    alpha.time_window = window
                    alphas.append(alpha)
                    count += 1
        
        return alphas
    
    def _generate_group_operation_alphas(self, base_expressions: List[str], 
                                        step: int, max_count: int) -> List[Alpha]:
        """生成分组操作Alpha"""
        alphas = []
        count = 0
        
        group_ops = ["group_rank", "group_neutralize", "group_zscore"]
        
        for base_expr in base_expressions[:30]:  # 限制基础表达式数量
            if count >= max_count:
                break
                
            for op in group_ops:
                if count >= max_count:
                    break
                
                alpha_expression = f"{op}({base_expr})"
                
                alpha = create_alpha(alpha_expression, step, self.config)
                alpha.generation_method = "group_operation"
                alpha.base_alphas = [base_expr]
                alphas.append(alpha)
                count += 1
        
        return alphas
    
    def _deduplicate_and_validate(self, alphas: List[Alpha]) -> List[Alpha]:
        """去重和验证Alpha"""
        if not self.config.enable_deduplication:
            return alphas
        
        validated_alphas = []
        
        for alpha in alphas:
            # 去重检查
            if alpha.alpha_expression in self.generated_expressions:
                continue
            
            # 验证表达式
            is_valid, errors = self.validator.validate_expression(alpha.alpha_expression)
            if is_valid:
                self.generated_expressions.add(alpha.alpha_expression)
                validated_alphas.append(alpha)
        
        return validated_alphas
    
    def get_generation_stats(self) -> Dict[str, Any]:
        """获取生成统计信息"""
        return {
            'total_generated': len(self.generated_expressions),
            'config': self.config.to_dict(),
            'operator_counts': {
                'basic': len(self.operators.BASIC_OPS),
                'ts': len(self.operators.TS_OPS),
                'group': len(self.operators.GROUP_OPS),
                'arsenal': len(self.operators.ARSENAL_OPS)
            }
        }