"""
Alpha数据模型

定义Alpha挖掘系统中的核心数据结构：
- Alpha: Alpha表达式和元数据
- AlphaConfig: Alpha生成配置
- BacktestResult: 回测结果数据
- QualityScore: Alpha质量评估
"""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from enum import Enum


class AlphaStatus(Enum):
    """Alpha状态枚举"""
    PENDING = "pending"           # 待生成
    GENERATED = "generated"       # 已生成
    BACKTESTING = "backtesting"   # 回测中
    COMPLETED = "completed"       # 回测完成
    FAILED = "failed"            # 失败
    SKIPPED = "skipped"          # 跳过


class QualityGrade(Enum):
    """Alpha质量等级"""
    A = "A"  # 优秀 (>0.8)
    B = "B"  # 良好 (0.6-0.8)
    C = "C"  # 一般 (0.4-0.6)
    D = "D"  # 较差 (<0.4)


@dataclass
class AlphaConfig:
    """Alpha生成配置类"""
    
    # 数据集配置
    dataset: str = "analyst4"  # analyst4, pv15等
    region: str = "USA"        # USA, EUR等
    universe: str = "TOP3000"  # TOP3000, TOP1200等
    
    # 生成参数
    max_step: int = 4              # 最大Alpha步数
    max_alphas_per_step: int = 1000  # 每步最大Alpha数
    
    # 时间窗口参数 (天)
    time_windows: List[int] = field(default_factory=lambda: [5, 22, 66, 120, 240])
    
    # 并发控制
    concurrent_backtests: int = 3   # 并发回测数量
    
    # 质量控制
    enable_quality_check: bool = True    # 启用质量检验
    min_quality_score: float = 0.3      # 最小质量分数
    
    # 断点续传
    enable_checkpoint: bool = True       # 启用断点续传
    checkpoint_interval: int = 100       # 检查点间隔
    
    # 表达式控制
    max_expression_length: int = 500     # 最大表达式长度
    enable_deduplication: bool = True    # 启用去重
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'dataset': self.dataset,
            'region': self.region,
            'universe': self.universe,
            'max_step': self.max_step,
            'max_alphas_per_step': self.max_alphas_per_step,
            'time_windows': self.time_windows,
            'concurrent_backtests': self.concurrent_backtests,
            'enable_quality_check': self.enable_quality_check,
            'min_quality_score': self.min_quality_score,
            'enable_checkpoint': self.enable_checkpoint,
            'checkpoint_interval': self.checkpoint_interval,
            'max_expression_length': self.max_expression_length,
            'enable_deduplication': self.enable_deduplication
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AlphaConfig':
        """从字典创建配置"""
        return cls(**data)


@dataclass
class Alpha:
    """Alpha数据模型"""
    
    # 基本信息
    alpha_expression: str                     # Alpha表达式
    step: int                         # Alpha步数 (1-4)
    alpha_id: Optional[str] = None    # Alpha ID
    
    # 元数据
    dataset: str = ""                  # 数据集
    region: str = ""                   # 地区
    universe: str = ""                 # 股票池
    
    # 状态管理
    status: AlphaStatus = AlphaStatus.PENDING
    created_at: datetime = field(default_factory=datetime.now)
    updated_at: datetime = field(default_factory=datetime.now)
    
    # 生成信息
    generation_method: str = ""        # 生成方法 (first_step, ts_comp, etc.)
    base_alphas: List[str] = field(default_factory=list)  # 基础Alpha（用于高步）
    time_window: Optional[int] = None  # 时间窗口
    
    # 回测相关
    backtest_id: Optional[str] = None   # 回测ID
    backtest_error: Optional[str] = None  # 回测错误信息
    
    # 质量信息
    quality_score: Optional[float] = None    # 质量分数
    quality_grade: Optional[QualityGrade] = None  # 质量等级
    
    def update_status(self, status: AlphaStatus, error: Optional[str] = None):
        """更新Alpha状态"""
        self.status = status
        self.updated_at = datetime.now()
        if error:
            self.backtest_error = error
    
    def is_valid_expression(self) -> bool:
        """检查表达式是否有效"""
        if not self.alpha_expression or len(self.alpha_expression.strip()) == 0:
            return False
        if len(self.alpha_expression) > 500:  # 表达式长度限制
            return False
        return True
    
    def get_complexity_score(self) -> float:
        """计算表达式复杂度分数"""
        if not self.alpha_expression:
            return 0.0
        
        # 简单的复杂度计算：基于长度和操作符数量
        length_score = min(len(self.alpha_expression) / 200, 1.0)  # 长度分数
        op_count = self.alpha_expression.count('(') + self.alpha_expression.count('ts_')
        op_score = min(op_count / 10, 1.0)  # 操作符分数
        
        return (length_score + op_score) / 2
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'alpha_expression': self.alpha_expression,
            'step': self.step,
            'alpha_id': self.alpha_id,
            'dataset': self.dataset,
            'region': self.region,
            'universe': self.universe,
            'status': self.status.value,
            'created_at': self.created_at.isoformat(),
            'updated_at': self.updated_at.isoformat(),
            'generation_method': self.generation_method,
            'base_alphas': self.base_alphas,
            'time_window': self.time_window,
            'backtest_id': self.backtest_id,
            'backtest_error': self.backtest_error,
            'quality_score': self.quality_score,
            'quality_grade': self.quality_grade.value if self.quality_grade else None
        }


@dataclass
class BacktestResult:
    """回测结果数据模型"""
    
    # 基本信息
    alpha_expression: str             # Alpha表达式
    backtest_id: str                   # 回测ID
    
    # 时间信息
    start_time: datetime
    end_time: datetime
    duration_seconds: float
    
    # 性能指标
    total_return: Optional[float] = None       # 总收益率
    annual_return: Optional[float] = None      # 年化收益率
    sharpe_ratio: Optional[float] = None       # 夏普比率
    information_ratio: Optional[float] = None  # 信息比率
    max_drawdown: Optional[float] = None       # 最大回撤
    volatility: Optional[float] = None         # 波动率
    
    # 统计信息
    win_rate: Optional[float] = None           # 胜率
    profit_loss_ratio: Optional[float] = None # 盈亏比
    
    # 状态信息
    status: str = "completed"                  # completed, failed, partial
    error_message: Optional[str] = None        # 错误信息
    
    # 原始数据
    raw_data: Dict[str, Any] = field(default_factory=dict)  # 原始回测数据
    
    def is_successful(self) -> bool:
        """判断回测是否成功"""
        return self.status == "completed" and self.error_message is None
    
    def get_performance_summary(self) -> Dict[str, float]:
        """获取性能摘要"""
        return {
            'total_return': self.total_return or 0.0,
            'annual_return': self.annual_return or 0.0,
            'sharpe_ratio': self.sharpe_ratio or 0.0,
            'information_ratio': self.information_ratio or 0.0,
            'max_drawdown': self.max_drawdown or 0.0,
            'volatility': self.volatility or 0.0,
            'win_rate': self.win_rate or 0.0
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'alpha_expression': self.alpha_expression,
            'backtest_id': self.backtest_id,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat(),
            'duration_seconds': self.duration_seconds,
            'total_return': self.total_return,
            'annual_return': self.annual_return,
            'sharpe_ratio': self.sharpe_ratio,
            'information_ratio': self.information_ratio,
            'max_drawdown': self.max_drawdown,
            'volatility': self.volatility,
            'win_rate': self.win_rate,
            'profit_loss_ratio': self.profit_loss_ratio,
            'status': self.status,
            'error_message': self.error_message,
            'raw_data': self.raw_data
        }


@dataclass
class QualityScore:
    """Alpha质量评估模型"""
    
    # 基本信息
    alpha_expression: str             # Alpha表达式
    evaluation_time: datetime = field(default_factory=datetime.now)
    
    # 质量指标
    overall_score: float = 0.0         # 总体质量分数 (0-1)
    grade: QualityGrade = QualityGrade.D  # 质量等级
    
    # 分项得分
    return_score: float = 0.0          # 收益指标得分
    risk_score: float = 0.0            # 风险指标得分
    stability_score: float = 0.0       # 稳定性得分
    complexity_score: float = 0.0      # 复杂度得分
    
    # 详细指标
    metrics: Dict[str, float] = field(default_factory=dict)
    
    # 评估状态
    is_valid: bool = True              # 是否为有效Alpha
    warnings: List[str] = field(default_factory=list)  # 警告信息
    
    def calculate_overall_score(self) -> float:
        """计算总体质量分数"""
        # 权重配置
        weights = {
            'return': 0.35,      # 收益权重35%
            'risk': 0.25,        # 风险权重25%
            'stability': 0.25,   # 稳定性权重25%
            'complexity': 0.15   # 复杂度权重15%
        }
        
        score = (
            self.return_score * weights['return'] +
            self.risk_score * weights['risk'] +
            self.stability_score * weights['stability'] +
            self.complexity_score * weights['complexity']
        )
        
        self.overall_score = max(0.0, min(1.0, score))  # 限制在0-1范围
        return self.overall_score
    
    def assign_grade(self) -> QualityGrade:
        """分配质量等级"""
        score = self.overall_score
        if score >= 0.8:
            self.grade = QualityGrade.A
        elif score >= 0.6:
            self.grade = QualityGrade.B
        elif score >= 0.4:
            self.grade = QualityGrade.C
        else:
            self.grade = QualityGrade.D
        
        return self.grade
    
    def add_warning(self, warning: str):
        """添加警告信息"""
        if warning not in self.warnings:
            self.warnings.append(warning)
    
    def is_acceptable(self, min_score: float = 0.3) -> bool:
        """判断Alpha是否可接受"""
        return self.is_valid and self.overall_score >= min_score
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'alpha_expression': self.alpha_expression,
            'evaluation_time': self.evaluation_time.isoformat(),
            'overall_score': self.overall_score,
            'grade': self.grade.value,
            'return_score': self.return_score,
            'risk_score': self.risk_score,
            'stability_score': self.stability_score,
            'complexity_score': self.complexity_score,
            'metrics': self.metrics,
            'is_valid': self.is_valid,
            'warnings': self.warnings
        }


# 工厂函数
def create_alpha(alpha_expression: str, step: int, config: AlphaConfig) -> Alpha:
    """创建Alpha实例"""
    return Alpha(
        alpha_expression=alpha_expression,
        step=step,
        dataset=config.dataset,
        region=config.region,
        universe=config.universe
    )


def create_backtest_result(alpha_expression: str, backtest_id: str) -> BacktestResult:
    """创建回测结果实例"""
    now = datetime.now()
    return BacktestResult(
        alpha_expression=alpha_expression,
        backtest_id=backtest_id,
        start_time=now,
        end_time=now,
        duration_seconds=0.0
    )


def create_quality_score(alpha_expression: str) -> QualityScore:
    """创建质量评分实例"""
    return QualityScore(
        alpha_expression=alpha_expression
    )