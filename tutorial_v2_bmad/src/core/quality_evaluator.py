"""
Alpha质量评估器

负责评估Alpha的质量，包括：
- 收益性指标评估
- 风险控制评估
- 稳定性分析
- 复杂度评估
- 综合质量分级
"""

import re
import math
from typing import Dict, List, Optional, Any
from datetime import datetime

from .alpha_models import Alpha, BacktestResult, QualityScore, QualityGrade, create_quality_score


class QualityEvaluator:
    """Alpha质量评估器"""
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or self._get_default_config()
        
        # 评估权重
        self.weights = self.config.get('weights', {
            'return': 0.35,      # 收益权重35%
            'risk': 0.25,        # 风险权重25%
            'stability': 0.25,   # 稳定性权重25%
            'complexity': 0.15   # 复杂度权重15%
        })
        
        # 基准值
        self.benchmarks = self.config.get('benchmarks', {
            'target_annual_return': 0.10,   # 目标年化收益率10%
            'target_sharpe_ratio': 2.0,     # 目标夏普比率2.0
            'max_drawdown_limit': 0.20,     # 最大回撤限制20%
            'target_win_rate': 0.55,        # 目标胜率55%
            'max_complexity_score': 50      # 最大复杂度分数
        })
        
        print(f"📊 QualityEvaluator初始化完成")
        print(f"   收益权重: {self.weights['return']:.0%}")
        print(f"   风险权重: {self.weights['risk']:.0%}")
        print(f"   稳定性权重: {self.weights['stability']:.0%}")
        print(f"   复杂度权重: {self.weights['complexity']:.0%}")
    
    def evaluate_alpha_quality(self, alpha: Alpha, 
                              backtest_result: BacktestResult) -> QualityScore:
        """评估Alpha质量"""
        score = create_quality_score(alpha.alpha_expression)
        
        print(f"🔍 开始评估Alpha质量: {alpha.alpha_expression[:50]}...")
        
        # 检查回测结果有效性
        if not self._validate_backtest_result(backtest_result, score):
            return score
        
        # 计算各项分数
        score.return_score = self._calculate_return_score(backtest_result)
        score.risk_score = self._calculate_risk_score(backtest_result)
        score.stability_score = self._calculate_stability_score(backtest_result)
        score.complexity_score = self._calculate_complexity_score(alpha)
        
        # 详细指标
        score.metrics = self._calculate_detailed_metrics(alpha, backtest_result)
        
        # 计算总分和等级
        score.calculate_overall_score()
        score.assign_grade()
        
        # 添加评估警告
        self._add_evaluation_warnings(score, alpha, backtest_result)
        
        # 更新Alpha质量信息
        alpha.quality_score = score.overall_score
        alpha.quality_grade = score.grade
        
        print(f"✅ Alpha质量评估完成: {score.grade.value}级 (分数: {score.overall_score:.3f})")
        
        return score
    
    def batch_evaluate(self, alphas_with_results: List[tuple]) -> List[QualityScore]:
        """批量评估Alpha质量"""
        results = []
        
        print(f"\n📊 开始批量质量评估: {len(alphas_with_results)} 个Alpha")
        
        for i, (alpha, backtest_result) in enumerate(alphas_with_results):
            try:
                score = self.evaluate_alpha_quality(alpha, backtest_result)
                results.append(score)
                
                if (i + 1) % 10 == 0:
                    print(f"   已完成: {i + 1}/{len(alphas_with_results)}")
                    
            except Exception as e:
                print(f"❌ Alpha评估失败: {alpha.alpha_expression[:30]}... - {str(e)}")
                # 创建失败的质量分数
                score = create_quality_score(alpha.alpha_expression)
                score.is_valid = False
                score.add_warning(f"评估失败: {str(e)}")
                results.append(score)
        
        self._print_batch_summary(results)
        return results
    
    def _validate_backtest_result(self, result: BacktestResult, score: QualityScore) -> bool:
        """验证回测结果"""
        if not result or not result.is_successful():
            score.is_valid = False
            score.add_warning("回测失败或无效")
            return False
        
        # 检查关键指标是否存在
        if result.sharpe_ratio is None and result.annual_return is None:
            score.is_valid = False
            score.add_warning("缺少关键性能指标")
            return False
        
        return True
    
    def _calculate_return_score(self, result: BacktestResult) -> float:
        """计算收益得分 (0-1)"""
        if not result.annual_return:
            return 0.0
        
        target_return = self.benchmarks['target_annual_return']
        
        # 基于年化收益率的评分
        return_score = max(0.0, min(1.0, result.annual_return / target_return))
        
        # 考虑收益率稳定性
        if result.win_rate:
            win_rate_bonus = (result.win_rate - 0.5) * 0.5  # 胜率超过50%的奖励
            return_score += max(0.0, win_rate_bonus)
        
        return max(0.0, min(1.0, return_score))
    
    def _calculate_risk_score(self, result: BacktestResult) -> float:
        """计算风险得分 (0-1)"""
        risk_score = 0.0
        risk_factors = 0
        
        # 夏普比率评分
        if result.sharpe_ratio is not None:
            target_sharpe = self.benchmarks['target_sharpe_ratio']
            sharpe_score = max(0.0, min(1.0, result.sharpe_ratio / target_sharpe))
            risk_score += sharpe_score
            risk_factors += 1
        
        # 最大回撤评分
        if result.max_drawdown is not None:
            max_dd_limit = self.benchmarks['max_drawdown_limit']
            # 回撤越小分数越高
            dd_score = max(0.0, 1.0 - (result.max_drawdown / max_dd_limit))
            risk_score += dd_score
            risk_factors += 1
        
        # 波动率评分 (相对于收益率)
        if result.volatility is not None and result.annual_return is not None:
            if result.volatility > 0:
                return_vol_ratio = abs(result.annual_return) / result.volatility
                vol_score = max(0.0, min(1.0, return_vol_ratio / 2.0))  # 2.0为目标比率
                risk_score += vol_score
                risk_factors += 1
        
        return risk_score / max(1, risk_factors)  # 平均分数
    
    def _calculate_stability_score(self, result: BacktestResult) -> float:
        """计算稳定性得分 (0-1)"""
        stability_score = 0.0
        
        # 信息比率评分
        if result.information_ratio is not None:
            ir_score = max(0.0, min(1.0, (result.information_ratio + 1.0) / 2.0))  # IR范围[-1,1]映射到[0,1]
            stability_score += ir_score * 0.4
        
        # 胜率评分
        if result.win_rate is not None:
            target_win_rate = self.benchmarks['target_win_rate']
            win_rate_score = max(0.0, min(1.0, result.win_rate / target_win_rate))
            stability_score += win_rate_score * 0.3
        
        # 盈亏比评分
        if result.profit_loss_ratio is not None:
            pl_ratio_score = max(0.0, min(1.0, result.profit_loss_ratio / 2.0))  # 2.0为目标盈亏比
            stability_score += pl_ratio_score * 0.3
        
        # 如果所有指标都缺失，使用基本稳定性评分
        if stability_score == 0.0:
            # 基于夏普比率和回撤的简单稳定性评分
            if result.sharpe_ratio and result.max_drawdown is not None:
                basic_stability = (result.sharpe_ratio + 2) / 4 * (1 - result.max_drawdown)
                stability_score = max(0.0, min(1.0, basic_stability))
        
        return stability_score
    
    def _calculate_complexity_score(self, alpha: Alpha) -> float:
        """计算复杂度得分 (0-1, 分数越高越简洁)"""
        expression = alpha.alpha_expression
        
        # 计算复杂度指标
        complexity_factors = {
            'length': len(expression),                           # 表达式长度
            'operator_count': self._count_operators(expression), # 操作符数量
            'parentheses_depth': self._calculate_parentheses_depth(expression), # 括号深度
            'function_count': self._count_functions(expression)   # 函数数量
        }
        
        # 复杂度分数计算
        max_complexity = self.benchmarks['max_complexity_score']
        
        # 长度惩罚
        length_penalty = min(1.0, complexity_factors['length'] / 200)  # 200字符为基准
        
        # 操作符惩罚
        operator_penalty = min(1.0, complexity_factors['operator_count'] / 20)  # 20个操作符为基准
        
        # 嵌套深度惩罚
        depth_penalty = min(1.0, complexity_factors['parentheses_depth'] / 10)  # 10层嵌套为基准
        
        # 函数数量惩罚
        function_penalty = min(1.0, complexity_factors['function_count'] / 15)  # 15个函数为基准
        
        # 总复杂度 (越小越好)
        total_complexity = (length_penalty + operator_penalty + depth_penalty + function_penalty) / 4
        
        # 转换为分数 (1 - 复杂度 = 简洁性分数)
        complexity_score = max(0.0, 1.0 - total_complexity)
        
        return complexity_score
    
    def _count_operators(self, expression: str) -> int:
        """统计操作符数量"""
        operators = ['+', '-', '*', '/', '>', '<', '>=', '<=', '==', '!=', '&', '|']
        count = 0
        for op in operators:
            count += expression.count(op)
        return count
    
    def _calculate_parentheses_depth(self, expression: str) -> int:
        """计算括号嵌套深度"""
        max_depth = 0
        current_depth = 0
        
        for char in expression:
            if char == '(':
                current_depth += 1
                max_depth = max(max_depth, current_depth)
            elif char == ')':
                current_depth -= 1
        
        return max_depth
    
    def _count_functions(self, expression: str) -> int:
        """统计函数调用数量"""
        # 使用正则表达式匹配函数模式
        function_pattern = r'\b[a-zA-Z_][a-zA-Z0-9_]*\s*\('
        matches = re.findall(function_pattern, expression)
        return len(matches)
    
    def _calculate_detailed_metrics(self, alpha: Alpha, result: BacktestResult) -> Dict[str, float]:
        """计算详细指标"""
        metrics = {}
        
        # 性能指标
        if result.annual_return is not None:
            metrics['annual_return'] = result.annual_return
        if result.sharpe_ratio is not None:
            metrics['sharpe_ratio'] = result.sharpe_ratio
        if result.max_drawdown is not None:
            metrics['max_drawdown'] = result.max_drawdown
        if result.win_rate is not None:
            metrics['win_rate'] = result.win_rate
        
        # 风险调整指标
        if result.annual_return and result.volatility:
            metrics['return_volatility_ratio'] = result.annual_return / result.volatility
        
        # Alpha特征
        metrics['expression_length'] = len(alpha.alpha_expression)
        metrics['alpha_step'] = alpha.step
        
        return metrics
    
    def _add_evaluation_warnings(self, score: QualityScore, alpha: Alpha, result: BacktestResult):
        """添加评估警告"""
        # 检查风险警告
        if result.max_drawdown and result.max_drawdown > self.benchmarks['max_drawdown_limit']:
            score.add_warning(f"最大回撤过大: {result.max_drawdown:.2%}")
        
        if result.sharpe_ratio and result.sharpe_ratio < 0:
            score.add_warning("夏普比率为负值")
        
        if result.win_rate and result.win_rate < 0.4:
            score.add_warning(f"胜率过低: {result.win_rate:.1%}")
        
        # 检查复杂度警告
        if len(alpha.alpha_expression) > 200:
            score.add_warning("Alpha表达式过于复杂")
        
        # 检查收益警告
        if result.annual_return and result.annual_return < 0:
            score.add_warning("年化收益率为负值")
    
    def _print_batch_summary(self, scores: List[QualityScore]):
        """打印批量评估摘要"""
        if not scores:
            return
        
        valid_scores = [s for s in scores if s.is_valid]
        grade_counts = {'A': 0, 'B': 0, 'C': 0, 'D': 0}
        
        for score in valid_scores:
            grade_counts[score.grade.value] += 1
        
        avg_score = sum(s.overall_score for s in valid_scores) / len(valid_scores) if valid_scores else 0
        
        print(f"\n📊 批量质量评估摘要:")
        print(f"   总数: {len(scores)}")
        print(f"   有效: {len(valid_scores)}")
        print(f"   平均分: {avg_score:.3f}")
        print(f"   等级分布: A={grade_counts['A']}, B={grade_counts['B']}, C={grade_counts['C']}, D={grade_counts['D']}")
        
        if valid_scores:
            best_score = max(valid_scores, key=lambda s: s.overall_score)
            print(f"   最高分: {best_score.overall_score:.3f} ({best_score.alpha_expression[:50]}...)")
    
    def get_quality_thresholds(self) -> Dict[str, float]:
        """获取质量阈值配置"""
        return {
            'min_acceptable_score': 0.3,     # 最低可接受分数
            'good_score_threshold': 0.6,     # 良好分数阈值
            'excellent_score_threshold': 0.8, # 优秀分数阈值
            'min_sharpe_ratio': 0.5,         # 最低夏普比率
            'max_acceptable_drawdown': 0.25   # 最大可接受回撤
        }
    
    def filter_quality_alphas(self, scored_alphas: List[tuple], 
                             min_score: float = 0.3) -> List[tuple]:
        """筛选高质量Alpha"""
        filtered = []
        
        for alpha, score in scored_alphas:
            if score.is_acceptable(min_score):
                filtered.append((alpha, score))
        
        print(f"🎯 质量筛选: {len(scored_alphas)} -> {len(filtered)} (阈值: {min_score:.2f})")
        
        return filtered
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'weights': {
                'return': 0.35,
                'risk': 0.25,
                'stability': 0.25,
                'complexity': 0.15
            },
            'benchmarks': {
                'target_annual_return': 0.10,
                'target_sharpe_ratio': 2.0,
                'max_drawdown_limit': 0.20,
                'target_win_rate': 0.55,
                'max_complexity_score': 50
            }
        }