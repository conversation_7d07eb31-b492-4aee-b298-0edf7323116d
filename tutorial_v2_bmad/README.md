# 🚀 WQ因子挖掘系统 (v2.0.0)

一个用于自动化因子挖掘、验证和提交到WorldQuant平台的智能系统。

## ✨ 核心特性

- 🔄 **自动化因子生成**: 多阶段因子挖掘，支持断点续传
- 🎯 **智能质量检验**: 自相关和生产相关性检验，质量分级
- 📊 **批量因子提交**: 智能重试机制，超时控制
- 💾 **本地数据管理**: SQLite数据库，自动备份
- 🎨 **友好CLI界面**: Rich组件，实时进度显示
- ⚙️ **配置驱动**: 完全基于配置文件，无硬编码

## 🛠️ 系统要求

- **Python**: 3.9.13 或更高版本（基于配置文件中的要求）
- **平台**: macOS / Linux / Windows
- **虚拟环境**: 建议使用 `.venv`

## 📦 安装依赖

```bash
# 创建虚拟环境
python3 -m venv .venv

# 激活虚拟环境 (macOS/Linux)
source .venv/bin/activate

# 安装依赖
pip3 install -r requirements.txt
```

## 🚀 快速开始

### 方法一：Python模块启动（推荐）

```bash
# 进入项目目录
cd tutorial_v2_bmad

# 启动系统
python3 -m src.app
```

### 方法二：脚本启动

```bash
# 使用启动脚本
./startup.sh
```

## ⚙️ 配置管理

### 📋 配置文件

系统严格依赖 `src/config/config.json` 配置文件，包含所有必需的系统参数：

- **系统配置**: Python版本要求、环境设置
- **快速开始**: 一键启动的默认配置  
- **因子生成**: 并发数、时间窗口、最大因子数
- **质量检验**: 相关性阈值、检验模式
- **因子提交**: 重试次数、超时设置
- **数据库**: 路径、备份设置
- **性能限制**: 内存、CPU使用限制

### 🔧 配置要求

- ✅ **必须存在**: 系统启动前必须确保 `config.json` 文件存在
- ✅ **格式正确**: 必须是有效的 JSON 格式
- ✅ **完整配置**: 所有必需的配置项都必须包含
- ❌ **不自动创建**: 系统不会自动创建默认配置文件

### 📖 配置指南

详细的配置说明请参考：[CONFIG_GUIDE.md](CONFIG_GUIDE.md)

## 🎯 使用流程

1. **启动系统**: 使用上述启动命令
2. **选择模式**: 
   - 🚀 快速开始（使用配置文件中的默认值）
   - ⚙️ 自定义配置（交互式配置参数）
3. **因子挖掘**: 系统自动生成、验证和提交因子
4. **进度监控**: 实时查看进度和结果统计

## 📁 项目结构

```
tutorial_v2_bmad/
├── src/                    # 源代码目录
│   ├── app.py             # 主程序入口
│   ├── cli/               # CLI界面组件
│   │   ├── config_manager.py  # 配置管理器
│   │   ├── menu_system.py     # 菜单系统
│   │   └── ...
│   └── config/            # 配置文件目录
│       └── config.json    # 主配置文件
├── data/                  # 数据目录
├── logs/                  # 日志目录
├── requirements.txt       # Python依赖
├── startup.sh            # 启动脚本
└── CONFIG_GUIDE.md       # 配置指南
```

## 🛠️ 故障排除

### 常见问题

1. **配置文件不存在**
   ```
   错误: 配置文件不存在: src/config/config.json
   解决: 确保配置文件存在并包含所有必需配置项
   ```

2. **Python版本不匹配**
   ```
   错误: Python版本要求不满足
   解决: 检查config.json中的min_python_version配置
   ```

3. **模块导入错误**
   ```
   错误: ModuleNotFoundError
   解决: 确保在项目根目录使用 python3 -m src.app 启动
   ```

### 配置恢复

如果配置文件损坏：

1. 从版本控制或备份中恢复 `config.json`
2. 检查配置文件格式和完整性
3. 重新启动系统

## 📞 技术支持

- 📖 详细配置指南: [CONFIG_GUIDE.md](CONFIG_GUIDE.md)
- 🏗️ 系统架构: `architecture/` 目录
- 📋 产品需求: `prd/` 目录

---

**💡 提示**: 系统采用配置驱动架构，通过修改 `config.json` 文件可以灵活调整系统行为，无需修改代码！