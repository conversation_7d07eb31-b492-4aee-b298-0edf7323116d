#!/bin/bash

# WQ因子挖掘系统启动脚本
# 提供一键启动功能，自动检查环境和依赖

echo "==============================================="
echo "🚀 WQ因子挖掘系统启动器"
echo "==============================================="
echo "版本: v2.0.0 MVP"
echo "功能: 自动化Alpha因子挖掘和提交平台"
echo "-----------------------------------------------"

# 检查Python版本
echo "🔍 检查Python环境..."
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误: 未找到python3命令"
    echo "请确保已安装Python（版本要求请查看config.json配置文件）"
    exit 1
fi

PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:3])))')

# 从配置文件读取最低Python版本要求
if [ -f "src/config/config.json" ]; then
    # 提取配置文件中的最低版本要求
    MIN_VERSION=$(python3 -c "
import json
try:
    with open('src/config/config.json', 'r') as f:
        config = json.load(f)
    min_ver = config.get('system', {}).get('min_python_version')
    if min_ver is None:
        raise ValueError('配置文件中缺少system.min_python_version')
    print('.'.join(map(str, min_ver)))
except Exception as e:
    print(f'ERROR: {e}', file=sys.stderr)
    exit(1)
")
    
    # 进行版本检查
    if python3 -c "
import sys
min_ver = '$MIN_VERSION'.split('.')
min_tuple = tuple(map(int, min_ver))
exit(0 if sys.version_info >= min_tuple else 1)
"; then
        echo "✅ Python版本检查通过: $PYTHON_VERSION (要求: $MIN_VERSION+)"
    else
        echo "❌ 错误: Python版本过低 ($PYTHON_VERSION)"
        echo "需要Python $MIN_VERSION+，请升级Python版本"
        exit 1
    fi
else
    echo "❌ 错误: 配置文件 src/config/config.json 不存在"
    echo "无法确定Python版本要求，请确保配置文件存在"
    exit 1
fi

# 检查虚拟环境（推荐但非必需）
if [[ "$VIRTUAL_ENV" != "" ]]; then
    echo "✅ 检测到虚拟环境: $(basename $VIRTUAL_ENV)"
elif [[ -d ".venv" ]]; then
    echo "🔄 发现.venv虚拟环境，尝试激活..."
    source .venv/bin/activate
    echo "✅ 虚拟环境已激活"
else
    echo "⚠️  警告: 未使用虚拟环境（推荐使用）"
fi

# 检查依赖包
echo "📦 检查必需依赖..."
MISSING_DEPS=()

for package in "rich" "aiohttp" "pandas" "numpy" "aiosqlite"; do
    if ! python3 -c "import $package" &> /dev/null; then
        MISSING_DEPS+=($package)
    fi
done

if [ ${#MISSING_DEPS[@]} -gt 0 ]; then
    echo "❌ 缺少以下依赖包: ${MISSING_DEPS[*]}"
    echo "🔧 正在自动安装依赖..."
    
    if python3 -m pip install -r requirements.txt; then
        echo "✅ 依赖安装完成"
    else
        echo "❌ 依赖安装失败，请手动执行:"
        echo "   pip3 install -r requirements.txt"
        exit 1
    fi
else
    echo "✅ 所有依赖已安装"
fi

# 创建必要目录
echo "📁 检查项目目录..."
for dir in "data" "logs"; do
    if [ ! -d "$dir" ]; then
        mkdir -p "$dir"
        echo "✅ 创建目录: $dir"
    fi
done

# 检查配置文件
if [ ! -f "src/config/config.json" ]; then
    echo "⚠️  配置文件不存在，程序将自动创建默认配置"
fi

echo "-----------------------------------------------"
echo "🚀 启动WQ因子挖掘系统..."
echo "==============================================="

# 启动程序
if python3 -m src.app; then
    echo ""
    echo "==============================================="
    echo "✅ 程序正常退出"
    echo "==============================================="
else
    echo ""
    echo "==============================================="
    echo "❌ 程序异常退出"
    echo "请检查错误信息或联系技术支持"
    echo "==============================================="
    exit 1
fi