# Story CLI-006: CLI界面系统

## 📋 Story 信息

**作为** 量化研究员和系统用户  
**我希望** 有一个简洁友好的命令行界面  
**以便** 轻松操作Alpha挖掘系统，无需复杂的技术知识即可完成所有操作

**Epic**: 用户交互层  
**优先级**: P0 (必须有)  
**估算**: 5 Story Points  
**Status**: 🚧 Ready for Development  
**依赖**: 所有核心模块 (SM-001, FE-002, FV-003, FS-004, DM-005)

## ✅ 验收条件

### 核心功能验收
- [ ] **一键启动**: 提供简单的启动命令，无需复杂参数配置
- [ ] **友好界面**: 使用Rich组件提供美观的控制台输出
- [ ] **操作简洁**: 针对无技术基础用户设计，操作步骤简化
- [ ] **结果展示**: 清晰显示执行结果，包括生成Alpha数、回测结果、提交状态
- [ ] **进度监控**: 实时显示任务执行进度和状态

### 技术验收
- [ ] **JSON配置**: 通过配置文件管理所有参数，避免命令行复杂性
- [ ] **环境检查**: 启动时自动检查环境配置和依赖
- [ ] **错误处理**: 友好的错误提示和处理建议
- [ ] **日志集成**: 与Rich组件集成的日志显示
- [ ] **异步支持**: 支持异步任务的进度显示

### 业务验收
- [ ] **全流程支持**: 支持完整的Alpha挖掘、检验、提交流程
- [ ] **状态查询**: 提供任务状态和历史记录查询
- [ ] **配置管理**: 支持查看和修改系统配置
- [ ] **统计报告**: 显示性能统计和成功率分析

## 🔧 技术任务

### 任务 1: 实现主应用入口和CLI框架

**基于**: MVP原则和用户友好性要求

- [ ] **子任务 1.1**: 创建主应用入口
  ```python
  # src/app.py - 应用主入口
  class WQAlphaMiningApp:
"""WQ Alpha挖掘系统主应用"""
      
      def __init__(self):
          self.console = Console()
          self.data_manager = None
          self.session_manager = None
          self.alpha_engine = None
          
      async def initialize(self) -> bool:
          """初始化系统"""
          await self.check_environment()
          await self.load_configuration()
          await self.initialize_modules()
          return True
          
      async def start_mining_workflow(self, config: dict) -> None:
          """启动Alpha挖掘工作流"""
          pass
          
      async def show_status(self) -> None:
          """显示系统状态"""
          pass
          
      async def show_statistics(self) -> None:
          """显示统计信息"""
          pass
  ```

- [ ] **子任务 1.2**: 实现Rich控制台界面
  ```python
  class ConsoleUI:
      """控制台用户界面"""
      
      def __init__(self):
          self.console = Console()
          
      def show_welcome_banner(self):
          """显示欢迎横幅"""
          self.console.print(Panel(
              "[bold blue]🚀 WQ Alpha挖掘系统[/bold blue]\n"
"[green]自动化Alpha挖掘和提交平台[/green]",
              title="欢迎使用",
              border_style="blue"
          ))
          
      def show_progress(self, task_name: str, progress: int, total: int):
          """显示进度条"""
          with Progress() as progress_bar:
              task = progress_bar.add_task(task_name, total=total)
              progress_bar.update(task, completed=progress)
              
      def show_results_table(self, results: List[dict]):
          """显示结果表格"""
          table = Table(show_header=True, header_style="bold magenta")
          # 添加表格内容...
  ```

- [ ] **子任务 1.3**: 实现配置管理系统
  ```python
  class ConfigManager:
      """配置管理器"""
      
      def __init__(self, config_path: str = "src/config/config.json"):
          self.config_path = config_path
          self.config = {}
          
      async def load_config(self) -> dict:
          """加载配置文件"""
          try:
              with open(self.config_path, 'r', encoding='utf-8') as f:
                  self.config = json.load(f)
              return self.config
          except FileNotFoundError:
              await self.create_default_config()
              return await self.load_config()
              
      async def create_default_config(self) -> None:
          """创建默认配置"""
          default_config = {
              "datasets": ["analyst4", "pv15"],
              "regions": ["USA", "EUR"],
              "universes": ["TOP3000", "TOP1200"],
              "alpha_generation": {
"max_alphas_per_step": 1000,
                  "time_windows": [5, 22, 66, 120, 240]
              },
              "validation": {
                  "self_correlation_threshold": 0.7,
                  "prod_correlation_threshold": 0.7
              }
          }
          # 保存默认配置...
  ```

### 任务 2: 实现简化的用户交互流程

**基于**: "不要提供复杂的 cli 命令和选项"的要求

- [ ] **子任务 2.1**: 实现一键启动功能
  ```python
  async def main():
      """主函数 - 一键启动"""
      app = WQAlphaMiningApp()
      
      try:
          # 显示欢迎界面
          app.ui.show_welcome_banner()
          
          # 初始化系统
          app.ui.show_info("🔧 正在初始化系统...")
          await app.initialize()
          app.ui.show_success("✅ 系统初始化完成")
          
          # 显示主菜单
          choice = await app.ui.show_main_menu()
          
          if choice == "start":
              await app.start_mining_workflow()
          elif choice == "status":
              await app.show_status()
          elif choice == "statistics":
              await app.show_statistics()
              
      except Exception as e:
          app.ui.show_error(f"❌ 系统错误: {e}")
          
  if __name__ == "__main__":
      asyncio.run(main())
  ```

- [ ] **子任务 2.2**: 实现交互式菜单系统
  ```python
  class InteractiveMenu:
      """交互式菜单"""
      
      def __init__(self, console: Console):
          self.console = console
          
      async def show_main_menu(self) -> str:
          """显示主菜单"""
          menu_options = [
              "🚀 开始Alpha挖掘",
              "📊 查看系统状态", 
              "📈 查看统计报告",
              "⚙️  系统配置",
              "❌ 退出系统"
          ]
          
          choice = Prompt.ask(
              "请选择操作",
              choices=["1", "2", "3", "4", "5"],
              default="1"
          )
          
          return {
              "1": "start_mining",
              "2": "show_status", 
              "3": "show_statistics",
              "4": "configure",
              "5": "exit"
          }.get(choice, "exit")
          
      async def show_workflow_options(self) -> dict:
          """显示工作流选项"""
          workflow_config = {}
          
          # 选择数据集
          dataset = Prompt.ask(
              "选择数据集",
              choices=["analyst4", "pv15", "both"],
              default="analyst4"
          )
          workflow_config["dataset"] = dataset
          
          # 选择地区
          region = Prompt.ask(
              "选择地区", 
              choices=["USA", "EUR", "both"],
              default="USA"
          )
          workflow_config["region"] = region
          
          return workflow_config
  ```

- [ ] **子任务 2.3**: 实现结果展示系统
  ```python
  class ResultsDisplay:
      """结果展示系统"""
      
      def __init__(self, console: Console):
          self.console = console
          
      def show_alpha_generation_results(self, results: dict):
"""显示Alpha生成结果"""
table = Table(title="Alpha生成结果", show_header=True)
          table.add_column("阶数", style="cyan")
          table.add_column("生成数量", style="green")
          table.add_column("回测完成", style="yellow")
          table.add_column("平均Sharpe", style="magenta")
          
          for order, data in results.items():
              table.add_row(
                  f"第{order}步Alpha",
                  str(data["generated"]),
                  str(data["backtested"]),
                  f"{data['avg_sharpe']:.3f}"
              )
              
          self.console.print(table)
          
      def show_validation_results(self, results: dict):
          """显示检验结果"""
          # 创建检验结果表格...
          
      def show_submission_results(self, results: dict):
          """显示提交结果"""
          # 创建提交结果表格...
  ```

### 任务 3: 实现实时进度监控

**基于**: Rich组件的进度显示功能

- [ ] **子任务 3.1**: 实现任务进度追踪
  ```python
  class ProgressTracker:
      """进度追踪器"""
      
      def __init__(self, console: Console):
          self.console = console
          self.progress = Progress(
              SpinnerColumn(),
              TextColumn("[progress.description]{task.description}"),
              BarColumn(),
              MofNCompleteColumn(),
              TimeElapsedColumn(),
              console=console
          )
          
      async def track_alpha_generation(self, config: dict):
"""追踪Alpha生成进度"""
with self.progress:
task = self.progress.add_task("生成Alpha中...", total=config["total_alphas"])

async for completed in self.alpha_engine.generate_alphas_async(config):
                  self.progress.update(task, completed=completed)
                  
      async def track_validation_progress(self, alphas: List):
"""追踪检验进度"""
with self.progress:
task = self.progress.add_task("检验Alpha中...", total=len(alphas))

async for completed in self.validator.validate_alphas_async(alphas):
                  self.progress.update(task, completed=completed)
  ```

- [ ] **子任务 3.2**: 实现状态监控面板
  ```python
  class StatusMonitor:
      """状态监控面板"""
      
      def __init__(self, console: Console):
          self.console = console
          
      async def show_live_status(self):
          """显示实时状态"""
          layout = Layout()
          layout.split_column(
              Layout(name="header", size=3),
              Layout(name="main", ratio=1),
              Layout(name="footer", size=3)
          )
          
          # 定期更新状态...
          
      def create_status_panel(self, data: dict) -> Panel:
          """创建状态面板"""
          status_text = f"""
          🔄 当前任务: {data['current_task']}
          📊 已生成Alpha: {data['total_alphas']}
✅ 检验通过: {data['validated_alphas']}
📤 提交成功: {data['submitted_alphas']}
          ⏱️  运行时间: {data['runtime']}
          """
          
          return Panel(
              status_text,
              title="系统状态",
              border_style="green"
          )
  ```

- [ ] **子任务 3.3**: 实现错误处理和用户指导
  ```python
  class ErrorHandler:
      """错误处理器"""
      
      def __init__(self, console: Console):
          self.console = console
          
      def handle_authentication_error(self, error: Exception):
          """处理认证错误"""
          self.console.print(Panel(
              "[red]❌ 认证失败[/red]\n\n"
              "可能的原因:\n"
              "1. user_info.txt文件不存在或格式错误\n"
              "2. 用户名或密码不正确\n"
              "3. 网络连接问题\n\n"
              "[yellow]解决方案:[/yellow]\n"
              "1. 检查user_info.txt文件\n"
              "2. 确认WQ平台凭证\n"
              "3. 检查网络连接",
              title="错误处理",
              border_style="red"
          ))
          
      def handle_configuration_error(self, error: Exception):
          """处理配置错误"""
          # 配置错误处理...
          
      def handle_general_error(self, error: Exception):
          """处理一般错误"""
          # 一般错误处理...
  ```

### 任务 4: 实现系统集成和工作流编排

- [ ] **子任务 4.1**: 集成所有核心模块
  ```python
  class WorkflowOrchestrator:
      """工作流编排器"""
      
      def __init__(self, app: WQAlphaMiningApp):
          self.app = app
          self.ui = app.ui
          
      async def execute_full_workflow(self, config: dict):
          """执行完整工作流"""
          try:
              # 1. 会话管理
              self.ui.show_info("🔐 正在进行身份认证...")
              await self.app.session_manager.authenticate()
              self.ui.show_success("✅ 身份认证成功")
              
              # 2. Alpha生成
self.ui.show_info("🏭 开始Alpha生成...")
alphas = await self.app.alpha_engine.generate_alphas(config)
self.ui.show_success(f"✅ 生成了 {len(alphas)} 个Alpha")

# 3. Alpha检验
self.ui.show_info("🔍 开始Alpha检验...")
validated = await self.app.alpha_validator.validate_alphas(alphas)
self.ui.show_success(f"✅ {len(validated)} 个Alpha通过检验")

# 4. Alpha提交
if validated:
self.ui.show_info("📤 开始提交Alpha...")
results = await self.app.submit_manager.submit_alphas(validated)
success_count = sum(1 for r in results if r.success)
self.ui.show_success(f"✅ 成功提交 {success_count} 个Alpha")
                  
              return True
              
          except Exception as e:
              self.app.error_handler.handle_general_error(e)
              return False
  ```

- [ ] **子任务 4.2**: 实现配置验证和环境检查
  ```python
  class EnvironmentChecker:
      """环境检查器"""
      
      def __init__(self, console: Console):
          self.console = console
          
      async def check_environment(self) -> bool:
          """检查运行环境"""
          checks = [
              ("Python版本", self.check_python_version),
              ("虚拟环境", self.check_virtual_env),
              ("配置文件", self.check_config_files),
              ("用户凭证", self.check_user_credentials),
              ("网络连接", self.check_network),
              ("数据目录", self.check_directories)
          ]
          
          all_passed = True
          for name, check_func in checks:
              try:
                  await check_func()
                  self.console.print(f"✅ {name}")
              except Exception as e:
                  self.console.print(f"❌ {name}: {e}")
                  all_passed = False
                  
          return all_passed
          
      async def check_python_version(self):
          """检查Python版本"""
          import sys
          if sys.version_info < (3, 9, 13):
              raise RuntimeError("需要Python 3.9.13+")
              
      async def check_virtual_env(self):
          """检查虚拟环境"""
          if not os.path.exists(".venv"):
              raise RuntimeError("虚拟环境不存在")
  ```

## 📂 实现文件清单

### 新建文件
- [ ] `src/app.py` - 主应用入口
- [ ] `src/cli/console_ui.py` - Rich控制台界面
- [ ] `src/cli/menu_system.py` - 交互式菜单
- [ ] `src/cli/progress_tracker.py` - 进度追踪器
- [ ] `src/cli/results_display.py` - 结果展示
- [ ] `src/cli/error_handler.py` - 错误处理器
- [ ] `src/cli/__init__.py` - CLI模块初始化

### 修改文件
- [ ] `src/config/config.json` - 添加CLI相关配置
- [ ] `requirements.txt` - 确认rich依赖

### 启动脚本
- [ ] `startup.sh` - 系统启动脚本 (参考已有脚本)
- [ ] `README.md` - 项目说明和启动指南

## 🔗 依赖和集成

### 上游依赖
- **所有核心模块**: SessionManager, AlphaEngine, AlphaValidator, SubmitManager, DataManager
- **配置系统**: 系统配置和用户凭证
- **Rich组件**: 控制台美化和交互

### 下游影响
- **用户体验**: 提供完整的用户交互入口
- **系统运维**: 简化系统操作和监控
- **错误处理**: 统一的错误处理和用户指导

### 服务提供
```python
# CLI作为系统的用户界面层
用户输入 → CLI界面 → 工作流编排 → 核心模块 → 结果展示
    ↑         ↓         ↓          ↓         ↓
  配置文件  Rich界面   进度追踪    业务逻辑   统计报告
```

## 📊 性能要求

### 界面响应
- **启动时间**: ≤5秒完成系统初始化
- **菜单响应**: ≤200ms界面响应时间
- **进度更新**: 实时更新，无明显延迟
- **错误提示**: 即时显示错误信息

### 用户体验
- **操作简洁**: 最多3步完成主要操作
- **信息清晰**: 所有状态和结果清晰展示
- **错误友好**: 提供具体的解决建议
- **进度可见**: 长时间任务有明确进度显示

## 🧪 测试验证

### 功能测试 (使用真实环境)
- [ ] **启动测试**: 验证一键启动功能
- [ ] **菜单测试**: 验证交互式菜单操作
- [ ] **工作流测试**: 验证完整工作流执行
- [ ] **配置测试**: 验证配置管理功能

### 用户体验测试
- [ ] **新手测试**: 无技术背景用户的使用测试
- [ ] **错误场景**: 各种错误情况的处理测试
- [ ] **中断恢复**: 任务中断后的界面状态
- [ ] **长时间运行**: 长时间任务的界面稳定性

### 界面测试
- [ ] **Rich组件**: 验证所有Rich组件正常显示
- [ ] **进度条**: 验证进度条的准确性
- [ ] **表格显示**: 验证结果表格的格式
- [ ] **颜色主题**: 验证界面颜色和样式

### 集成测试
- [ ] **模块集成**: 与所有核心模块的集成测试
- [ ] **配置集成**: 配置文件的读取和应用
- [ ] **错误传播**: 底层错误的正确传播和显示
- [ ] **状态同步**: 界面状态与系统状态的同步

## 📋 Definition of Done

### 代码完成标准
- [ ] 所有验收条件实现并通过测试
- [ ] 遵循MVP原则，简洁友好的用户界面
- [ ] 基于Rich组件的美观控制台输出
- [ ] 与所有核心模块的无缝集成

### 用户体验标准
- [ ] 无技术背景用户能轻松使用
- [ ] 操作步骤简化，不超过3步
- [ ] 错误提示友好，提供解决建议
- [ ] 结果展示清晰，信息完整

### 集成标准
- [ ] 支持完整的Alpha挖掘工作流
- [ ] 提供实时的进度监控
- [ ] 统一的错误处理机制
- [ ] 完整的状态和统计信息展示

### 文档标准
- [ ] 用户操作指南
- [ ] 配置文件说明
- [ ] 错误处理参考
- [ ] 界面功能说明

---

## 🎯 开发指导

### 关键实现要点
1. **MVP原则**: 功能简洁，避免复杂的CLI命令和选项
2. **用户友好**: 面向无技术背景用户设计
3. **Rich组件**: 充分利用Rich的美观界面功能
4. **一键启动**: 最小化用户操作复杂度

### 开发阶段建议
**阶段1** (1-2天): 主应用入口和基础框架  
**阶段2** (1-2天): 交互式菜单和用户界面  
**阶段3** (1-2天): 进度监控和结果展示  
**阶段4** (1天): 错误处理和系统集成  

### 避免的陷阱
- 不要设计复杂的命令行参数 (需求明确要求避免)
- 不要忽略用户体验 (面向非技术用户)
- 不要过度设计 (MVP原则)
- 不要忽略错误提示的友好性

### 成功标准
- **3步内完成**: 主要操作不超过3步
- **5秒启动**: 系统初始化≤5秒
- **实时进度**: 所有长时间任务有进度显示
- **友好错误**: 所有错误都有解决建议

**Next Story建议**: 完成此Story后，MVP核心功能已完整。可以考虑"监控统计系统"来增强系统的可观测性。