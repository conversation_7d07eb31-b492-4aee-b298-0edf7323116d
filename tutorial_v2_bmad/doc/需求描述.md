# 需求描述

## 1.系统目标

通过程序调用WQ平台提供的接口，生成高质量的因子来获取WQ平台奖励，因子的质量越高，奖励越高。
没有程序直接使用WQ平台提供的 web 界面也可以实现模拟、回测、提交因子的功能，但时间久，需要大量人工操作。

## 2.需求

### alpha因子

#### 1.什么是因子

α 因子的目标是捕捉那些尚未被市场充分定价的信息，从而在不同市场环境下（无论上涨、下跌或震荡）都能产生正收益

#### 2.程序目标

生成高质量的因子来获取，因子的质量越高，奖励越高

#### 3.生成因子的关键要素

数据集操作符，数据字段

#### 4.因子是怎么来的？

一阶因子的生成过程是一个系统性的、基于规则的暴力搜索过程：
输入: 一组基础数据字段（动态获取或预定义）。
变换: 使用一个包含多种算子的工具箱。
参数: 为每种算子应用一组预先定义好、硬编码的参数（主要是时间窗口）。
组合: 通过将字段、算子和参数进行笛卡尔积式的组合，生成海量的一阶因子候选表达式。
回测: 对生成的因子使用固定的回测参数进行模拟。

这种方法的优点是能够系统性地探索大量可能的因子，缺点是生成的因子数量巨大，需要大量的计算资源进行回测和筛选。
参数的确定依赖于开发者的先验知识和经验，而不是通过数据驱动的优化过程来动态确定。

> 如何更加高效的获取先验知识和经验，更少的依赖人工？
> 是否可以提前使用机器学习算法来提高备选因子质量，减少回测数量？

1. 确定基础数据字段 (Fields)

- 生成因子的第一步是确定要使用哪些基础数据字段。这些字段是构建Alpha表达式的基石。
- 调用WQ平台接口动态获取指定数据集（dataset_id，例如 'analyst4'）下的可用数据字段。
- 获取到的字段列表经过处理，筛选出类型为 "matrix" 和 "vector" 的字段作为备选。
- 预定义字段: 预先筛选和定义好的字段列表（如 recommended_fields_1），可以直接加载使用，而不是每次都从服务器动态获取。

2. 确定操作符 (Operators)

- 选定基础字段后，需要一系列操作符（Operators）来对这些字段进行变换，从而生成因子表达式。
- 操作符库: 预定义多种操作符，比如：
    - ts_ops: 时间序列（Time-Series）操作符，例如 ts_delay, ts_delta, ts_rank 等，用于处理数据在时间维度上的变化。
    - basic_ops: 基础数学和横截面（Cross-Sectional）操作符，例如 rank, scale, log, sigmoid 等，用于对数据进行数学变换或在截面上进行处理。
- 操作符组合: 组合选定的操作符在一起，形成一个完整的操作符集合，用于后续的因子生成。     

3. 生成一阶因子表达式

- 笛卡尔积: 核心思想是，将每一个基础字段（pc_fields）与每一个操作符（ts_ops + basic_ops）进行组合，生成大量的候选Alpha表达式。
- 表达式构建: 遍历所有字段和操作符，应用模板生成具体的表达式字符串。例如，如果字段是 close, 操作符是 ts_rank(20)，则会生成类似 ts_rank(close, 20) 的表达式。
- 遍历所有提供的字段和操作符，并根据操作符的类型应用不同的参数生成策略。

4. 参数确定

- 对于时间序列操作符 (ts_ops):为操作符应用一组预定义的时间窗口（days）参数，这里的时间窗口 [5, 22, 66, 120, 240] 分别大致对应一周、一月、一季、半年和一年，是量化研究中常用的周期。
- 对于需要额外参数的复杂操作符:
    - ts_comp_factory 函数被调用，它会组合时间窗口和另一组指定的参数。例如，ts_moment 会依次尝试 k 等于 2, 3, 4，并与 days 列表中的每个时间窗口组合。
    - 而 ts_percentage 和 ts_entropy 则使用了固定的参数值 [0.5] 和 [10]。
- 对于回测（Simulation）参数: 回测时使用的 decay (衰减期) 和 delay (延迟) 是固定值。

#### 5.因子回测

生成因子以后，需要调用WQ的接口（/simulations）回测，回测后可以验证因子的质量，接口会返回一系列属性和属性值，根据不同的属性来判断因子的质量

### 处理基本流程

#### 1.会话管理

首先就是要获取会话，通过WQ平台的接口（/authentication）进行登录，登录后会获取用户权限列表，不同的权限有不同访问数据的限制，用接口的速率限制所以需要将会话保存起来，后续根据会话返回的信息来执行后续操作

#### 2.数据集

通过数据集来组合因子，组合出来的因子去调用WQ平台提供的回测接口。

#### 3.关键要素

生成因子的几个关键要素包括数据集、操作符、数据字段。不同的数据集对应的操作符和数据字段可能是不同的。

数据集对应了哪些操作符和数据字段按常理来讲是可以通过WQ平台的接口获取的，也可以根据经验提前预定义一批最常用的。

#### 4.接口容错

我们的目标是获取高质量的因子，但是由于WQ平台提供的接口限制，每天可以调用接口的次数是有限的，这就意味着我们每天可以执行的任务次数是有限的。那么我就需要界面可以查看生成了哪些因子，这些因子质量如何，为了生成这些因子要用平台接口消耗了多长时间，状态是否正常，成功的比率，这样上述的统计信息我就可以清楚的了解到当前任务的执行状态，以方便调整

#### 5. 因子回测、标记

旧的代码当中，把提交因子的过程分为了不同的阶段，可能会有1234阶段、一二34阶段生成的因子之后才会调用check功能去校验因子生成的质量。通过校验后的因子才去提交。那么提交的时候，提交接口也会对因子进行其他的校验，比如说自相关性、平台相关性。如果提交接口的校验没有通过，提交的状态也会是失败的

由于整个流程比较长，每一个流程当中的节点都有可能产生失败，需要记录每一个任务执行的状态，每一个因子详细信息和状态。这样我们就需要引入数据库来去保存各个阶段的信息，同时通过数据库查询也可以进行各个维度的统计

#### 6.因子提交

另外一个功能就是将生成的高质量因子进行提交，提交到WQ平台的因子才会计算奖励，所以还需要一个任务是提交因子

#### 7.事件机制

因为整个流程是围绕着因子来运行的，所以流程看起来更像是一个事件出发的机制。比如通过数据集定义的数据字段和操作符生成的因子，生成的因子本身就是一个事件（生成一阶因子），
一阶因子完成事件就应该触发生成二阶因子。
二阶因子生成完成事件就应该触发生成三阶因子。三阶因子完成的事件就应该触发生成四阶因子，四阶因子完成的时间就应该出发查时间，Check成功的因子就应该触发提交时间
这里的一、二、三、四阶因子生成的阶段不是固定的，有可能会增加或者减少，需要在配置中定义。在执行整个流程之前，就需要提前定义需要执行哪些阶段

## 3.程序设计原则

 - 一定要按照MVP的原则，最小化生成功能，不要过度设计
 - 不要生成单元测试
 - 不要 mock 数据
 - 不需要数据迁移、不需要数据备份
 - 所有生成的文档需要既能满足需求又没有冗余，设计结构清晰，语言精简
 - 用户操作便捷
    - 对于无技术基础的用户友好，操作简洁
    - 只需要定义好执行参数，一键启动即可
    - 方便查看执行结果，比如生成多少备选因子，回测了多少，因子质量如何，提交成功了哪些因子，提交失败的原因
    - 不要提供复杂的 cli 命令和选项
 - 用户配置，使用 json 配置文件管理，包括身份认证信息，用户选择的执行参数 
 - 循序渐进
    - 一开始只实现最基本功能即可，保证整体流程可以跑通，不必追求完美
    - 逐步骤、逐个模块实现，现有基本功能再执行优化
 - 如果需要控制台打印日志，使用 rich 组件实现
 - 如果需要 web 实现，使用 vue
 - 如果需要数据库，使用 sqllite 本地文件，直接使用 sql，不要使用 orm 框架
    - sqllite 文件路径 `data/wq`
    - 提供数据库初始化脚本
    - 用户新用户使用，自动初始化数据库，不要让用户手工操作
    - 不要做数据迁移
    - 不要做数据清理    
    - 不需要做数据备份
 - Python 版本3.9.13+，必须使用虚拟环境
 - 日志管理
    - 存储日志文件 `logs/app.log`
    - 日志文件按日期分割，不要按文件大小分割
    - 日志格式：[yyyy-MM-dd HH:MM:ss][thread][class][method][lineNo]-[level]- message
 - 命名规则
    - 因子：alpha
    - 因子挖掘步骤：step
    - 因子表达式：alpha_expression
    - 数据集：dataset
    - 操作符：operator
    - 数字字段：field
    -


## 4.WQ平台

世坤量化：https://www.worldquant.com/

### 交易比赛示例

```
第 1 阶段：预选赛

所提交的alpha在给定周内， Sharpe-Margin表现最好的*，
第一名队伍：200美元
第二名队伍：100美元
第三名队伍：50美元

在给定周内提交最多alpha的*，
第一名队伍：200美元
第二名队伍：100美元
第三名队伍：50美元

*在第1阶段，每支队伍只可获得上述每一类别的奖项一次。

第 2 阶段：全国半决赛
第一名队伍：3,000 美元
第二名队伍：2,000 美元
第三名队伍：1,000 美元

第 3 阶段：全球总决赛
冠军：20,000 美元
亚军：12,000 美元
季军：8,000 美元

表现优异的参赛者有机会赢得WorldQuant实习或全职工作。表现优异的队伍有资格赢取上述现金奖励（或在法律禁止或限制现金奖励的司法管辖区获得现金等价物），奖金将平均分配给符合资格的队伍成员。
```

### 平台接口

WQ平台提供了一系列的接口，每个接口的作用

| HTTP 方法 | 接口路径 | 描述 |
| :--- | :--- | :--- |
| `POST` | `/authentication` | 用户登录认证 |
| `GET` | `/operators` | 获取可用的操作操作符 |
| `GET` | `/alphas/{alpha_id}` | 获取单个Alpha的详细信息 |
| `PATCH` | `/alphas/{alpha_id}` | 更新Alpha的属性（如名称、颜色、标签） |
| `/alphas/{alpha_id}/check` | 检查Alpha的提交状态和合规性 |
| `POST` | `/simulations` | 提交Alpha进行模拟回测 |
| `GET` | `/data-sets` | 获取数据集列表 |
| `GET` | `/data-fields` | 获取数据字段 |
| `GET` | `/users/self/alphas` | 获取用户自己的Alpha列表 |
| `/alphas/{alpha_id}/correlations/self` | 获取Alpha的自相关性 |
| `/alphas/{alpha_id}/correlations/prod` | 获取Alpha与产品库中其他Alpha的相关性 |
| `POST`, `GET` | `/alphas/{alpha_id}/submit` | 提交Alpha到平台 |

#### 接口调用

调用接口本质上是一个任务，那么就需要记录任务的开始结束时间，任务的类型，任务的参数，任务执行的状态，是成功还是失败，任务返回的数据结果，同时由于WQ平台的接口限制，有可能调用接口是失败的。对于失败的任务根据失败的原因来决定这个任务是结束还是进行重试

旧代码(src_xin)只是对个流程的一个简单实现，并不完善。我们需要实现一个新的完善的系统来生成高质量、高质量的因子，从而获得高额的奖励。

## 5.旧代码 `src_xin`

#### 📁 现有脚本功能详解

##### 1. 第一轮挖掘 (digging_1step.py)

**功能特性**:

- 基于数据集字段生成一阶因子表达式
- 异步并发提交因子进行回测，支持并发控制
- 支持断点续传机制，避免重复计算
- 使用文件记录已完成的因子状态
- 灵活的参数配置（region, universe, delay 等）

##### 2. 第二轮挖掘 (digging_2step.py)

**功能特性**:

- 基于第一轮结果生成二阶因子
- 根据 turnover 智能调整 decay 参数
- 支持多种组合操作生成复杂因子
- 渐进式优化策略，提升因子质量

##### 3. 因子检查 (check.py)

**功能特性**:

- 自动检查因子的自相关性和产品相关性
- 支持多线程并发检查
- 生成可提交因子列表
- 多维度质量评估机制

##### 4. 第三阶段挖掘 (digging_3step.py)

**功能特性**:

- 基于第二阶段结果生成交易条件因子
- 使用 trade_when_factory 创建复杂的交易逻辑
- 支持异步并发处理，可控制并发数量
- SessionManager 会话管理和自动刷新机制
- 断点续传：读取已完成的 alpha 表达式避免重复处理
- 动态配置：从 user_info.txt 读取 dataset_id
- 多参数支持：region、universe、delay、decay 等

##### 5. 智能提交系统 (s7.py)

**功能特性**:

- 30 分钟超时的智能提交机制
- 持续监控模式：自动检测 submitable_alpha.csv 的新因子
- 按自相关性排序优化提交顺序
- 服务器超时自动重连和会话管理
- 详细的提交统计和进度报告
- 错误分类处理：403 永久拒绝、408 超时重试
- 实时状态显示和累计成功率统计

##### 6. 传统提交 (submit_alpha.py)

**功能特性**:

- 批量提交通过检查的因子
- 重试机制处理提交失败
- 状态跟踪和结果记录
- 自动化提交流程

#### ✅ 优点

1. **功能完整性**：包含完整的 1-2-3 阶段因子挖掘流程
2. **异步并发**：使用 aiohttp 实现高效的并发模拟
3. **断点续传**：支持从中断处继续执行
4. **质量检测**：有自相关性和生产相关性检测机制
5. **自动提交**：支持批量因子提交功能
6. **丰富字段**：包含多种推荐字段和操作符组合
7. **智能参数调整**：decay 参数根据 turnover 自动调整
8. **多维度评估**：自相关性和产品相关性双重检测
9. **交易条件因子**：第三阶段支持复杂的 trade_when 交易逻辑
10. **会话管理**：SessionManager 自动处理会话过期和刷新
11. **智能提交**：30 分钟超时机制和持续监控模式
12. **错误分类**：针对不同错误类型采用不同处理策略
13. **实时监控**：详细的进度报告和统计信息
14. **优化排序**：按自相关性排序提高提交成功率

#### ❌ 存在问题

##### 🏗️ 架构层面问题

1. **多脚本独立运行**：需要手动协调 digging_1step.py → digging_2step.py → digging_3step.py → check.py → s7.py/submit_alpha.py 的执行顺序
2. **状态管理分散**：各个阶段的状态信息分散在不同的 txt/csv 文件中
3. **缺乏统一调度**：无法实现自动化的端到端流程，需要人工干预

##### 💾 数据存储问题

4. **文件存储临时数据**：使用 txt/csv 文件存储状态，缺乏结构化管理
5. **数据一致性风险**：多个进程同时读写文件可能导致数据不一致
6. **查询效率低**：无法高效查询和统计历史数据

##### 🛡️ 容错性问题

7. **异常捕获不完善**：网络异常、API 限制、会话过期等错误处理不够完善
8. **恢复机制简单**：异常中断后需要手工分析和恢复，缺乏自动重试机制
9. **会话管理脆弱**：会话过期处理机制不够健壮
10. **第三阶段依赖性**：digging_3step.py 严重依赖第二阶段结果，无数据时会阻塞
11. **提交超时处理**：s7.py 虽有 30 分钟超时但缺乏智能退避策略

##### 📊 监控统计问题

10. **缺乏实时监控**：无法实时了解挖掘进度和各阶段处理状态
11. **统计信息缺失**：缺乏整体的性能统计、成功率分析和资源利用情况
12. **资源使用不透明**：无法合理分配和调整并发数、请求频率等资源参数

##### 📱 通知机制问题

13. **通知功能有限**：只有基础的微信通知，缺乏多渠道通知支持
14. **事件驱动不足**：缺乏基于事件的自动通知机制（如高质量因子发现、系统异常等）
15. **提交结果通知缺失**：s7.py 虽有统计但缺乏及时的成功/失败通知

##### 👥 多账户管理问题

15. **单账户限制**：只支持单个账户执行，无法利用多账户并行处理
16. **负载均衡缺失**：无法在多账户间智能分配任务负载

##### 🎯 参数优化问题

17. **缺乏优化反馈**：没有记录各账户执行情况和参数效果，无法进行数据驱动的调优
18. **参数配置静态**：decay、delay 等参数配置固定，缺乏自适应调整机制
