# WQ因子挖掘系统 - 产品需求文档(PRD)

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**产品经理**: PM团队  
**最后更新**: 2024年12月  

---

## 1. 产品概述

### 1.1 产品定位

WQ因子挖掘系统是一个专业的量化投研自动化平台，通过程序化调用WorldQuant(WQ)平台接口，自动生成、回测、验证和提交高质量的α因子，从而获取平台奖励。该系统将替代传统的手工操作方式，大幅提升因子研发效率。

### 1.2 业务背景

- **传统痛点**: 使用WQ平台web界面进行因子模拟、回测、提交需要大量人工操作，效率低下
- **市场机会**: WQ平台提供丰富的奖励机制，高质量因子可获得高额回报
- **技术优势**: 通过程序化接口调用，可实现自动化的因子挖掘流程

### 1.3 目标用户

- **量化研究员**: 需要批量生成和测试α因子的专业人员
- **量化团队**: 参与WQ竞赛和因子研发的团队
- **个人投资者**: 希望通过自动化工具参与量化竞赛的个人用户

### 1.4 核心价值

- 🚀 **效率提升**: 自动化替代手工操作，提升10倍以上因子研发效率
- 🎯 **质量保证**: 基于规则的系统化因子生成和质量检验
- 💰 **收益最大化**: 优化因子质量，提高平台奖励获得概率
- 🛡️ **稳定可靠**: 7x24小时无人值守运行，强健的容错机制

---

## 2. 业务需求

### 2.1 核心业务流程

#### 2.1.1 α因子基础知识

**因子定义**: α因子的目标是捕捉那些尚未被市场充分定价的信息，从而在不同市场环境下（无论上涨、下跌或震荡）都能产生正收益。

**因子生成要素**:
- 数据集操作符 (Operators)
- 数据字段 (Fields)
- 参数配置 (Parameters)

#### 2.1.2 因子生成流程

**一阶因子生成**:
1. **确定基础数据字段**: 调用WQ平台接口动态获取指定数据集下的可用字段，筛选"matrix"和"vector"类型字段
2. **确定操作符**: 包括时间序列操作符(ts_ops)和基础数学操作符(basic_ops)
3. **生成表达式**: 通过笛卡尔积将字段与操作符组合，生成大量候选α表达式
4. **参数确定**: 应用预定义的时间窗口参数([5, 22, 66, 120, 240]天)

**多阶因子生成**:
- 基于一阶因子结果生成二阶因子
- 根据turnover智能调整decay参数
- 支持复杂的trade_when交易逻辑

### 2.2 业务处理流程

#### 2.2.1 会话管理
- 通过WQ平台`/authentication`接口进行登录认证
- 获取用户权限列表和访问限制
- 保存会话信息，支持会话续传和自动刷新

#### 2.2.2 因子回测
- 调用WQ平台`/simulations`接口进行因子回测
- 获取因子质量评估指标
- 支持并发回测，提高处理效率

#### 2.2.3 因子检验
- 自动检查因子的自相关性和产品相关性
- 支持多线程并发检验
- 生成可提交因子列表

#### 2.2.4 因子提交
- 将高质量因子提交到WQ平台
- 跟踪提交状态和结果
- 处理提交失败和重试逻辑

---

## 3. 功能需求

### 3.1 核心功能模块

#### 3.1.1 会话管理系统 ⭐⭐⭐
**优先级**: P0（必须有）

**功能描述**:
- WQ平台身份认证和登录
- 会话状态管理和自动刷新
- 权限验证和访问控制
- 接口调用频率控制

**验收标准**:
- 自动处理会话过期和重新登录
- 支持多账户管理
- 提供会话状态监控

#### 3.1.2 因子挖掘引擎 ⭐⭐⭐
**优先级**: P0（必须有）

**功能描述**:
- 多步骤因子生成（1-4阶渐进式挖掘）
- 支持多数据集（analyst4, pv15等）
- 支持多地区、多股票池配置
- 基于规则的系统化因子组合
- 表达式复杂度控制和过滤

**验收标准**:
- 支持analyst4、pv15等主流数据集
- 单次可生成1000+因子候选
- 支持1-4阶因子递进生成
- 支持断点续传机制

#### 3.1.3 因子检验系统 ⭐⭐⭐
**优先级**: P0（必须有）

**功能描述**:
- 自动相关性检验（自相关、生产相关）
- 因子质量评估和评级
- 批量检验和并发处理
- 可配置的检验阈值

**验收标准**:
- 自相关阈值默认0.7，可配置
- 生产相关阈值默认0.7，可配置
- 支持批量检验进度显示
- 提供检验结果统计

#### 3.1.4 因子提交系统 ⭐⭐⭐
**优先级**: P0（必须有）

**功能描述**:
- 批量因子提交管理
- 提交状态跟踪和管理
- 智能重试机制
- 提交结果统计

**验收标准**:
- 支持单个和批量因子提交
- 提交失败时自动重试机制
- 完整记录提交历史和状态
- 提供提交成功率统计

#### 3.1.5 数据管理系统 ⭐⭐⭐
**优先级**: P0（必须有）

**功能描述**:
- SQLite数据库存储
- 因子信息和状态管理
- 任务执行历史记录
- 数据查询和统计分析

**验收标准**:
- 自动初始化数据库结构
- 记录所有因子生成和处理状态
- 支持多维度数据查询
- 提供性能统计报告

#### 3.1.6 监控统计系统 ⭐⭐⭐
**优先级**: P0（必须有）

**功能描述**:
- 实时任务进度监控
- 系统性能统计
- 因子质量分析
- 资源利用率监控

**验收标准**:
- 提供实时进度显示
- 生成性能分析报告
- 支持多维度统计查询
- 资源使用优化建议

### 3.2 事件驱动机制

**事件流程**:
1. 一阶因子生成完成 → 触发二阶因子生成
2. 二阶因子生成完成 → 触发三阶因子生成  
3. 三阶因子生成完成 → 触发四阶因子生成
4. 四阶因子生成完成 → 触发因子检验
5. 检验成功 → 触发因子提交

**配置化支持**:
- 支持可配置的处理阶段数量
- 灵活的事件触发条件设置
- 可自定义的处理流程编排

---

## 4. 非功能需求

### 4.1 性能要求
- **并发处理**: 支持1000+因子并发回测
- **响应时间**: 单个接口调用响应时间<5秒
- **吞吐量**: 每小时处理因子数量≥500个
- **资源占用**: 内存使用<4GB，CPU使用率<80%

### 4.2 可靠性要求
- **可用性**: 系统可用性≥99.5%
- **容错性**: 网络异常自动重试，支持断点续传
- **数据完整性**: 所有操作日志完整记录，数据不丢失
- **恢复能力**: 异常中断后可自动恢复到中断点

### 4.3 易用性要求
- **操作简便**: 一键启动，最小化人工干预
- **界面友好**: 清晰的进度显示和状态反馈
- **文档完善**: 提供详细的使用文档和FAQ
- **错误处理**: 友好的错误提示和处理建议

### 4.4 安全性要求
- **身份认证**: 安全的用户认证和会话管理
- **数据安全**: 敏感信息加密存储
- **访问控制**: 基于权限的功能访问控制
- **审计日志**: 完整的操作审计记录

---

## 5. 技术规格

### 5.1 技术架构

**开发环境**:
- Python 3.9.13+
- 必须使用虚拟环境(.venv)
- zsh shell环境

**核心技术栈**:
- **数据库**: SQLite本地文件存储
- **异步处理**: aiohttp异步HTTP客户端
- **日志系统**: rich组件实现控制台输出
- **配置管理**: JSON配置文件
- **任务调度**: 事件驱动的任务编排

### 5.2 系统集成

**WQ平台接口集成**:
| HTTP方法 | 接口路径 | 描述 |
|---------|---------|------|
| POST | /authentication | 用户登录认证 |
| GET | /operators | 获取可用操作符 |
| GET | /alphas/{alpha_id} | 获取Alpha详细信息 |
| POST | /simulations | 提交Alpha进行回测 |
| GET | /data-sets | 获取数据集列表 |
| GET | /data-fields | 获取数据字段 |
| GET | /users/self/alphas | 获取用户Alpha列表 |
| POST | /alphas/{alpha_id}/submit | 提交Alpha到平台 |

### 5.3 数据模型

**核心数据表**:
- 因子信息表(factors)
- 任务执行表(tasks)
- 会话管理表(sessions)
- 配置参数表(configurations)
- 统计报告表(statistics)

**数据存储路径**:
- 数据库文件: `data/wq.db`
- 日志文件: `logs/app.log`
- 配置文件: `src/config/config.json`

---

## 6. 设计原则

### 6.1 开发原则
- **MVP原则**: 最小化可行功能，避免过度设计
- **循序渐进**: 先实现基本功能，再逐步优化
- **模块化设计**: 清晰的模块划分，便于维护扩展
- **事件驱动**: 基于事件的自动化流程编排

### 6.2 用户体验原则
- **操作便捷**: 对无技术基础用户友好
- **一键启动**: 配置好参数后一键启动
- **结果可视**: 方便查看执行结果和统计信息
- **简化配置**: 避免复杂的CLI命令和选项

### 6.3 技术原则
- **容错优先**: 强健的异常处理和恢复机制
- **性能优化**: 充分利用并发和异步处理
- **日志完善**: 完整的操作日志和状态跟踪
- **配置灵活**: JSON配置文件，支持运行时调整

---

## 7. 项目范围

### 7.1 项目包含功能
✅ **已包含**:
- 多阶因子自动生成
- WQ平台接口集成
- 因子质量检验
- 批量因子提交
- 数据库状态管理
- 日志监控系统
- 会话管理
- 事件驱动编排

### 7.2 项目不包含功能
❌ **不包含**:
- 单元测试（按需求明确排除）
- Mock数据（使用真实数据）
- 数据迁移功能
- 数据备份功能
- Web界面（专注CLI）
- ORM框架（直接使用SQL）
- 数据清理功能

---

## 8. 验收标准

### 8.1 功能验收
- [ ] 能够成功连接WQ平台并完成身份认证
- [ ] 能够自动生成1-4阶因子表达式
- [ ] 能够批量提交因子进行回测
- [ ] 能够自动检验因子质量
- [ ] 能够将合格因子提交到平台
- [ ] 能够提供完整的执行统计报告

### 8.2 性能验收
- [ ] 系统可连续稳定运行24小时以上
- [ ] 因子生成效率比手工操作提升10倍以上
- [ ] 网络异常时能自动重试并恢复
- [ ] 所有操作状态能完整记录到数据库

### 8.3 易用性验收
- [ ] 新用户能在30分钟内完成环境配置
- [ ] 配置文件修改后能一键启动系统
- [ ] 执行过程能实时显示进度和状态
- [ ] 异常情况能提供明确的错误提示

---

## 9. 风险与限制

### 9.1 技术风险
- **API限制**: WQ平台接口调用频率限制
- **网络稳定性**: 网络异常可能影响任务执行
- **会话管理**: 会话过期需要及时处理

### 9.2 业务风险
- **因子质量**: 生成的因子质量依赖于参数配置
- **平台变更**: WQ平台接口可能发生变化
- **竞争激烈**: 市场竞争可能影响收益

### 9.3 缓解措施
- 实现强健的重试和容错机制
- 提供灵活的配置参数调整
- 建立完善的监控和告警系统
- 保持与WQ平台接口的兼容性

---

## 10. 项目计划

### 10.1 开发阶段

**阶段一: 核心功能开发** (预计4周)
- 会话管理系统
- 因子生成引擎
- WQ平台接口集成
- 基础数据库结构

**阶段二: 质量和提交系统** (预计3周)
- 因子检验系统
- 因子提交系统
- 事件驱动机制
- 错误处理优化

**阶段三: 监控和优化** (预计2周)
- 监控统计系统
- 性能优化
- 用户体验改进
- 文档完善

### 10.2 里程碑
- [ ] **里程碑1**: 完成核心因子生成功能
- [ ] **里程碑2**: 完成端到端自动化流程
- [ ] **里程碑3**: 完成系统优化和文档

---

## 附录

### A. 术语表
- **α因子**: 用于捕捉超额收益的量化投资因子
- **WQ平台**: WorldQuant量化投资竞赛平台
- **因子回测**: 使用历史数据验证因子有效性
- **自相关性**: 因子与自身历史表现的相关度
- **产品相关性**: 因子与平台现有产品的相关度

### B. 参考资料
- WorldQuant平台官方文档
- 现有代码库分析(src_xin)
- 量化投资因子工程最佳实践

---

**文档状态**: ✅ 已完成  
**审核状态**: 待审核  
**版本控制**: Git管理