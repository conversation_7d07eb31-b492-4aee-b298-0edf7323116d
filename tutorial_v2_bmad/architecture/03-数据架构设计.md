# 数据架构设计 - 技术架构分片

**分片版本**: v1.0  
**所属模块**: 数据设计  
**维护团队**: 架构团队  
**最后更新**: 2024年12月

## 1. 数据库架构设计

### 1.1 数据库选型

| 特性 | SQLite | 选型理由 |
|------|--------|---------|
| **轻量级** | ✅ | 零配置，无需安装和维护 |
| **事务支持** | ✅ | ACID特性保证数据一致性 |
| **并发读取** | ✅ | 支持多读者并发访问 |
| **文件存储** | ✅ | 便于备份和迁移 |
| **Python集成** | ✅ | Python内置sqlite3模块 |
| **适用场景** | ✅ | 单机应用，中小规模数据 |

### 1.2 数据库Schema设计

#### 核心数据表结构

**因子信息表 (factors)**
```sql
CREATE TABLE factors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    expression TEXT NOT NULL UNIQUE,           -- 因子表达式
    factor_type VARCHAR(20) NOT NULL,          -- 因子类型 (1st, 2nd, 3rd, 4th)
    dataset_id VARCHAR(50) NOT NULL,           -- 数据集ID
    region VARCHAR(10) NOT NULL,               -- 地区
    universe VARCHAR(20) NOT NULL,             -- 股票池
    
    -- 回测结果
    sharpe REAL,                               -- 夏普比率
    returns REAL,                              -- 年化收益率
    turnover REAL,                             -- 换手率
    fitness REAL,                              -- 适应度分数
    
    -- 检验结果
    self_correlation REAL,                     -- 自相关性
    prod_correlation REAL,                     -- 产品相关性
    is_submittable BOOLEAN DEFAULT FALSE,      -- 是否可提交
    
    -- 提交状态
    submit_status VARCHAR(20) DEFAULT 'PENDING', -- 提交状态
    submit_attempt_count INTEGER DEFAULT 0,    -- 提交尝试次数
    submit_result TEXT,                        -- 提交结果详情
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP,
    
    -- 索引
    INDEX idx_factor_type_region (factor_type, region),
    INDEX idx_submit_status (submit_status),
    INDEX idx_created_at (created_at)
);
```

**会话管理表 (sessions)**
```sql
CREATE TABLE sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id VARCHAR(100) NOT NULL,            -- 用户ID
    session_token TEXT NOT NULL,              -- 会话令牌
    refresh_token TEXT,                       -- 刷新令牌
    expires_at TIMESTAMP NOT NULL,            -- 过期时间
    permissions TEXT,                         -- 用户权限(JSON)
    is_active BOOLEAN DEFAULT TRUE,           -- 是否活跃
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, is_active),
    INDEX idx_expires_at (expires_at),
    INDEX idx_user_active (user_id, is_active)
);
```

**任务执行表 (workflow_tasks)**
```sql
CREATE TABLE workflow_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_name VARCHAR(100) NOT NULL,          -- 任务名称
    task_type VARCHAR(50) NOT NULL,           -- 任务类型
    parent_task_id INTEGER,                   -- 父任务ID
    dataset_id VARCHAR(50),                   -- 关联数据集
    region VARCHAR(10),                       -- 地区
    universe VARCHAR(20),                     -- 股票池
    
    -- 任务配置
    task_config TEXT,                         -- 任务配置(JSON)
    priority INTEGER DEFAULT 5,              -- 优先级
    
    -- 进度信息
    status VARCHAR(20) DEFAULT 'PENDING',     -- 任务状态
    progress INTEGER DEFAULT 0,              -- 进度百分比
    total_items INTEGER DEFAULT 0,           -- 总项目数
    completed_items INTEGER DEFAULT 0,       -- 已完成项目数
    failed_items INTEGER DEFAULT 0,          -- 失败项目数
    
    -- 时间信息
    estimated_duration INTEGER,              -- 预估持续时间(秒)
    started_at TIMESTAMP,                    -- 开始时间
    completed_at TIMESTAMP,                 -- 完成时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY(parent_task_id) REFERENCES workflow_tasks(id),
    INDEX idx_task_status (status),
    INDEX idx_task_type_created (task_type, created_at),
    INDEX idx_parent_task (parent_task_id)
);
```

**系统配置表 (configurations)**
```sql
CREATE TABLE configurations (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) NOT NULL UNIQUE, -- 配置键
    config_value TEXT NOT NULL,              -- 配置值
    config_type VARCHAR(20) NOT NULL,        -- 配置类型
    description TEXT,                        -- 配置说明
    is_encrypted BOOLEAN DEFAULT FALSE,      -- 是否加密
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_config_type (config_type)
);
```

**统计报告表 (statistics)**
```sql
CREATE TABLE statistics (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    metric_name VARCHAR(100) NOT NULL,       -- 指标名称
    metric_value REAL NOT NULL,              -- 指标值
    metric_type VARCHAR(50) NOT NULL,        -- 指标类型
    dimension_tags TEXT,                     -- 维度标签(JSON)
    collected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_metric_name_time (metric_name, collected_at),
    INDEX idx_metric_type (metric_type)
);
```

### 1.3 数据访问层设计

#### Repository模式实现

```python
class BaseRepository:
    """基础数据仓库类"""
    
    def __init__(self, db_manager: DatabaseManager):
        self.db = db_manager
        
    async def create(self, entity: dict) -> int:
        """创建实体"""
        pass
        
    async def get_by_id(self, entity_id: int) -> Optional[dict]:
        """根据ID获取实体"""
        pass
        
    async def update(self, entity_id: int, updates: dict) -> bool:
        """更新实体"""
        pass
        
    async def delete(self, entity_id: int) -> bool:
        """删除实体"""
        pass

class FactorRepository(BaseRepository):
    """因子数据仓库"""
    
    async def create_factor(self, factor: FactorEntity) -> int:
        """创建因子记录"""
        query = """
        INSERT INTO factors (expression, factor_type, dataset_id, region, universe)
        VALUES (?, ?, ?, ?, ?)
        """
        return await self.db.execute_update(query, (
            factor.expression, factor.factor_type, factor.dataset_id,
            factor.region, factor.universe
        ))
        
    async def get_factors_by_status(self, status: str) -> List[FactorEntity]:
        """根据状态获取因子列表"""
        query = "SELECT * FROM factors WHERE submit_status = ? ORDER BY created_at DESC"
        rows = await self.db.execute_query(query, (status,))
        return [FactorEntity.from_dict(row) for row in rows]
        
    async def update_backtest_result(self, factor_id: int, result: BacktestResult) -> bool:
        """更新回测结果"""
        query = """
        UPDATE factors SET 
            sharpe = ?, returns = ?, turnover = ?, fitness = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
        """
        affected = await self.db.execute_update(query, (
            result.sharpe, result.returns, result.turnover, result.fitness, factor_id
        ))
        return affected > 0
        
    async def get_submittable_factors(self) -> List[FactorEntity]:
        """获取可提交的因子"""
        query = """
        SELECT * FROM factors 
        WHERE is_submittable = TRUE AND submit_status = 'PENDING'
        ORDER BY self_correlation ASC
        """
        rows = await self.db.execute_query(query)
        return [FactorEntity.from_dict(row) for row in rows]
```

#### 数据库服务层

```python
class DatabaseService:
    """数据库服务"""
    
    def __init__(self, db_path: str = "data/wq.db"):
        self.db_path = db_path
        self.connection_pool = None
        
    async def execute_query(self, query: str, params: tuple = ()) -> List[dict]:
        """执行查询"""
        async with aiosqlite.connect(self.db_path) as db:
            db.row_factory = aiosqlite.Row
            async with db.execute(query, params) as cursor:
                rows = await cursor.fetchall()
                return [dict(row) for row in rows]
        
    async def execute_update(self, query: str, params: tuple = ()) -> int:
        """执行更新"""
        async with aiosqlite.connect(self.db_path) as db:
            await db.execute(query, params)
            await db.commit()
            return db.total_changes
        
    async def begin_transaction(self) -> 'Transaction':
        """开始事务"""
        return Transaction(self.db_path)
        
    async def health_check(self) -> bool:
        """健康检查"""
        try:
            await self.execute_query("SELECT 1")
            return True
        except Exception:
            return False

class Transaction:
    """数据库事务管理"""
    
    def __init__(self, db_path: str):
        self.db_path = db_path
        self.connection = None
        
    async def __aenter__(self):
        self.connection = await aiosqlite.connect(self.db_path)
        await self.connection.execute("BEGIN TRANSACTION")
        return self
        
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            await self.connection.execute("COMMIT")
        else:
            await self.connection.execute("ROLLBACK")
        await self.connection.close()
        
    async def execute(self, query: str, params: tuple = ()):
        """在事务中执行SQL"""
        return await self.connection.execute(query, params)
```

## 2. 数据流设计

### 2.1 数据流架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                            数据流图                              │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  配置文件 ─────┐                                                 │
│              │                                                 │
│  用户输入 ─────┼─────► 配置服务 ─────► 事件调度器                │
│              │         │               │                       │
│  命令行参数 ───┘         │               ▼                       │
│                        ▼         因子挖掘引擎                    │
│  WQ平台数据 ◄──── 会话管理器 ◄──────┘                           │
│       │              │                                         │
│       ▼              ▼                                         │
│  数据缓存 ──────► 数据库服务 ◄──── 监控收集器                    │
│       │              │               │                         │
│       ▼              ▼               ▼                         │
│  因子生成器 ──────► 文件系统 ◄──── 日志服务                      │
│       │              │               │                         │
│       ▼              ▼               ▼                         │
│  回测结果 ──────► 统计报告 ◄──── 指标聚合                        │
│       │                                                        │
│       ▼                                                        │
│  提交队列 ──────► WQ平台提交                                     │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 数据生命周期管理

#### 数据创建流程
```python
# 因子数据创建流程
async def create_factor_data_flow(expression: str, config: dict):
    """因子数据创建流程"""
    
    # 1. 创建因子记录
    factor_repo = FactorRepository(db_service)
    factor_id = await factor_repo.create_factor(FactorEntity(
        expression=expression,
        factor_type=config['factor_type'],
        dataset_id=config['dataset_id'],
        region=config['region'],
        universe=config['universe']
    ))
    
    # 2. 提交回测任务
    task_repo = TaskRepository(db_service)
    task_id = await task_repo.create_task(
        task_type='BACKTEST',
        name=f'Backtest_Factor_{factor_id}',
        config={'factor_id': factor_id, 'expression': expression}
    )
    
    # 3. 记录统计指标
    metrics_collector = MetricsCollector(db_service)
    await metrics_collector.record_metric(
        name='factor_created',
        value=1,
        metric_type='counter',
        tags={'factor_type': config['factor_type'], 'region': config['region']}
    )
    
    return factor_id
```

#### 数据更新流程
```python
# 因子状态更新流程
async def update_factor_status_flow(factor_id: int, backtest_result: dict):
    """因子状态更新流程"""
    
    async with db_service.begin_transaction() as tx:
        # 1. 更新因子回测结果
        factor_repo = FactorRepository(db_service)
        await factor_repo.update_backtest_result(factor_id, backtest_result)
        
        # 2. 更新任务状态
        task_repo = TaskRepository(db_service)
        await task_repo.update_task_status(task_id, 'COMPLETED')
        
        # 3. 记录性能指标
        await metrics_collector.record_metric(
            name='factor_backtest_completed',
            value=1,
            metric_type='counter',
            tags={'sharpe': backtest_result.get('sharpe', 0)}
        )
```

## 3. 配置管理

### 3.1 配置文件结构

```json
{
  "database": {
    "path": "data/wq.db",
    "auto_create": true,
    "backup_enabled": false,
    "connection_timeout": 30
  },
  "wq_platform": {
    "base_url": "https://api.worldquantbrain.com",
    "timeout": 30,
    "max_retries": 3,
    "retry_backoff": [1, 2, 4, 8]
  },
  "factor_generation": {
    "time_windows": [5, 22, 66, 120, 240],
    "max_factors_per_batch": 1000,
    "concurrent_simulations": 10,
    "checkpoint_interval": 100
  },
  "validation": {
    "self_correlation_threshold": 0.7,
    "prod_correlation_threshold": 0.7,
    "validation_modes": ["USER", "CONSULTANT"],
    "batch_size": 50
  },
  "logging": {
    "level": "INFO",
    "file": "logs/app.log",
    "rotation": "daily",
    "retention": 30,
    "max_size": "100MB"
  }
}
```

### 3.2 配置服务实现

```python
class ConfigService:
    """配置管理服务"""
    
    def __init__(self, config_path: str = "src/config/config.json"):
        self.config_path = config_path
        self.config = self.load_config()
        self.watchers = []
    
    def load_config(self) -> dict:
        """加载配置文件"""
        with open(self.config_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get(self, key: str, default=None):
        """获取配置值，支持点分隔键"""
        keys = key.split('.')
        value = self.config
        for k in keys:
            value = value.get(k, default)
            if value is None:
                return default
        return value
    
    async def set_config(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        target = self.config
        for k in keys[:-1]:
            target = target.setdefault(k, {})
        target[keys[-1]] = value
        
        # 持久化到数据库
        config_repo = ConfigRepository(db_service)
        await config_repo.upsert_config(key, value)
        
    async def reload_config(self) -> None:
        """重新加载配置"""
        new_config = self.load_config()
        changes = self._detect_changes(self.config, new_config)
        self.config = new_config
        
        # 通知监听器
        for watcher in self.watchers:
            await watcher.on_config_changed(changes)
```

## 4. 数据备份和恢复

### 4.1 备份策略

```python
class BackupManager:
    """数据备份管理器"""
    
    def __init__(self, db_path: str, backup_dir: str = "backups"):
        self.db_path = db_path
        self.backup_dir = backup_dir
        os.makedirs(backup_dir, exist_ok=True)
    
    async def create_backup(self) -> str:
        """创建数据库备份"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(self.backup_dir, f"wq_backup_{timestamp}.db")
        
        # SQLite数据库备份
        async with aiosqlite.connect(self.db_path) as source:
            async with aiosqlite.connect(backup_path) as backup:
                await source.backup(backup)
        
        # 压缩备份文件
        compressed_path = f"{backup_path}.gz"
        with open(backup_path, 'rb') as f_in:
            with gzip.open(compressed_path, 'wb') as f_out:
                f_out.writelines(f_in)
        
        os.remove(backup_path)
        return compressed_path
    
    def cleanup_old_backups(self, retention_days: int = 30):
        """清理旧备份文件"""
        cutoff_time = time.time() - (retention_days * 24 * 3600)
        
        for filename in os.listdir(self.backup_dir):
            file_path = os.path.join(self.backup_dir, filename)
            if os.path.getctime(file_path) < cutoff_time:
                os.remove(file_path)
```

---

**维护说明**: 此分片定义了系统的完整数据架构，包括数据库设计、数据访问层、配置管理和备份策略。