# 系统架构概述 - 技术架构分片

**分片版本**: v1.0  
**所属模块**: 架构设计  
**维护团队**: 架构团队  
**最后更新**: 2024年12月

## 1. 架构概述

### 1.1 系统概览

WQ因子挖掘系统是一个基于事件驱动的量化投研自动化平台，采用分层架构设计，实现高并发、高可用的因子挖掘和管理服务。系统通过异步编程模型，与WorldQuant平台无缝集成，提供从因子生成到提交的完整自动化流程。

### 1.2 核心设计原则

- **事件驱动架构**: 基于发布-订阅模式的松耦合组件设计
- **异步优先**: 全面采用异步编程，提升并发处理能力
- **容错为先**: 多层次容错机制，确保系统稳定性
- **数据中心化**: 基于SQLite的集中式状态管理
- **可观测性**: 完整的日志、监控和指标体系
- **MVP导向**: 避免过度设计，专注核心功能

### 1.3 技术栈选型

| 层次 | 技术选型 | 版本要求 | 选型理由 |
|------|---------|---------|----------|
| **运行时** | Python | 3.9.13+ | 丰富的量化分析生态 |
| **环境管理** | venv | - | 官方推荐，简单可靠 |
| **Shell环境** | zsh | - | 项目规范要求 |
| **HTTP客户端** | aiohttp | 3.8+ | 高性能异步HTTP处理 |
| **数据库** | SQLite | 3.31+ | 轻量级，无需维护 |
| **日志输出** | rich | 13.0+ | 美观的控制台输出 |
| **配置管理** | JSON | - | 简单直观，易于修改 |
| **任务调度** | asyncio | - | Python内置异步框架 |

## 2. 整体架构设计

### 2.1 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        WQ因子挖掘系统                            │
├─────────────────────────────────────────────────────────────────┤
│                      应用层 (Application Layer)                  │
├───────────────────┬─────────────────┬───────────────────────────┤
│   CLI命令行界面    │   配置管理接口   │     监控统计接口           │
│   (CLI Interface) │  (Config API)   │   (Monitor API)           │
└───────────────────┴─────────────────┴───────────────────────────┘
├─────────────────────────────────────────────────────────────────┤
│                      业务层 (Business Layer)                     │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐     │
│ │   会话管理器     │ │   因子挖掘引擎   │ │   检验提交器     │     │
│ │ SessionManager  │ │ FactorEngine    │ │ SubmitManager   │     │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘     │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐     │
│ │   事件调度器     │ │   数据管理器     │ │   监控收集器     │     │
│ │ EventScheduler  │ │  DataManager    │ │ MetricsCollector│     │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘     │
├─────────────────────────────────────────────────────────────────┤
│                      服务层 (Service Layer)                      │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐     │
│ │   WQ API客户端   │ │   数据库服务     │ │   日志服务       │     │
│ │  WQApiClient    │ │ DatabaseService │ │ LoggingService  │     │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘     │
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐     │
│ │   缓存服务       │ │   队列服务       │ │   配置服务       │     │
│ │  CacheService   │ │  QueueService   │ │ ConfigService   │     │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘     │
├─────────────────────────────────────────────────────────────────┤
│                      基础设施层 (Infrastructure Layer)            │
├─────────────────────────────────────────────────────────────────┤
│ ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐     │
│ │   SQLite数据库   │ │   文件系统       │ │   网络通信       │     │
│ │  data/wq.db     │ │ logs/,config/   │ │  aiohttp        │     │
│ └─────────────────┘ └─────────────────┘ └─────────────────┘     │
└─────────────────────────────────────────────────────────────────┘
                                 │
                                 ▼
┌─────────────────────────────────────────────────────────────────┐
│                     外部依赖 (External Dependencies)              │
├─────────────────────────────────────────────────────────────────┤
│                    WorldQuant Brain Platform                    │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │  认证接口        │ │  因子回测接口    │ │  因子提交接口    │   │
│  │ /authentication│ │ /simulations    │ │ /submit         │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
└─────────────────────────────────────────────────────────────────┘
```

### 2.2 模块依赖关系

```
┌─────────────────────────────────────────────────────────────────┐
│                         依赖关系图                               │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│    CLI Interface                                                │
│         │                                                       │
│         ▼                                                       │
│    EventScheduler ◄──────────────┐                             │
│         │                        │                             │
│         ▼                        │                             │
│    ┌──────────────┐               │                             │
│    │FactorEngine  │               │                             │
│    │   ┌──────────▼─────────┐     │                             │
│    │   │SessionManager      │     │                             │
│    │   └────────────────────┘     │                             │
│    │   ┌────────────────────┐     │                             │
│    │   │DataManager         │     │                             │
│    │   └────────────────────┘     │                             │
│    └──────────────┘               │                             │
│         │                        │                             │
│         ▼                        │                             │
│    SubmitManager ─────────────────┘                             │
│         │                                                       │
│         ▼                                                       │
│    MetricsCollector                                             │
│                                                                 │
│    所有模块依赖：                                                │
│    ├── ConfigService (配置管理)                                 │
│    ├── LoggingService (日志服务)                                │
│    ├── DatabaseService (数据持久化)                             │
│    └── WQApiClient (外部API调用)                                │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 3. 设计模式

### 3.1 事件驱动架构

#### 事件流设计
```
┌─────────────────────────────────────────────────────────────────┐
│                         事件驱动流程                             │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  开始事件 (WorkflowStarted)                                      │
│     │                                                           │
│     ▼                                                           │
│  一阶因子生成 (FirstOrderFactorGeneration)                        │
│     │                                                           │
│     │ 完成事件: FirstOrderFactorsGenerated                        │
│     ▼                                                           │
│  二阶因子生成 (SecondOrderFactorGeneration)                       │
│     │                                                           │
│     │ 完成事件: SecondOrderFactorsGenerated                       │
│     ▼                                                           │
│  三阶因子生成 (ThirdOrderFactorGeneration)                        │
│     │                                                           │
│     │ 完成事件: ThirdOrderFactorsGenerated                        │
│     ▼                                                           │
│  四阶因子生成 (FourthOrderFactorGeneration)                       │
│     │                                                           │
│     │ 完成事件: FourthOrderFactorsGenerated                       │
│     ▼                                                           │
│  因子检验 (FactorValidation)                                     │
│     │                                                           │
│     │ 完成事件: FactorValidationCompleted                         │
│     ▼                                                           │
│  因子提交 (FactorSubmission)                                     │
│     │                                                           │
│     │ 完成事件: FactorSubmissionCompleted                         │
│     ▼                                                           │
│  流程结束 (WorkflowCompleted)                                    │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 3.2 异步编程模式

#### 并发控制策略
- **Semaphore控制**: 使用asyncio.Semaphore控制并发数量
- **ThreadPoolExecutor**: 用于CPU密集型任务的并发处理
- **Session管理**: aiohttp.ClientSession复用连接
- **资源池管理**: 连接池和资源复用机制

### 3.3 工厂模式

#### 因子生成工厂
基于旧代码验证的工厂函数模式：
- `first_order_factory`: 一阶因子生成工厂
- `ts_comp_factory`: 复合时间序列因子工厂
- `trade_when_factory`: 交易条件因子工厂
- `group_factory`: 分组操作因子工厂

## 4. 性能架构

### 4.1 并发模型
- **异步IO**: 基于asyncio的非阻塞IO模型
- **混合并发**: asyncio + ThreadPoolExecutor的混合模式
- **连接复用**: HTTP连接池复用和会话管理
- **资源控制**: 内存、CPU和网络资源的动态控制

### 4.2 缓存策略
- **会话缓存**: 内存缓存活跃会话信息
- **配置缓存**: 热加载配置文件缓存
- **结果缓存**: 因子生成结果的临时缓存
- **断点缓存**: 任务断点信息的持久化缓存

---

**维护说明**: 此分片提供系统架构的整体概览和设计原则，是理解整个系统技术架构的基础。