# 核心模块设计 - 技术架构分片

**分片版本**: v1.0  
**所属模块**: 核心组件  
**维护团队**: 架构团队  
**最后更新**: 2024年12月

## 1. 会话管理系统 (SessionManager)

### 1.1 功能职责

```python
class SessionManager:
    """WQ平台会话管理器
    
    职责:
    - WorldQuant平台身份认证
    - 会话生命周期管理
    - 自动令牌刷新
    - 多账户会话管理
    - 接口调用频率控制
    """
    
    async def authenticate(self, username: str, password: str) -> Session
    async def refresh_session(self, session: Session) -> Session
    async def validate_session(self, session: Session) -> bool
    async def get_active_session(self, user_id: str) -> Session
    async def rate_limit_check(self, session: Session, endpoint: str) -> bool
```

### 1.2 技术设计

**认证响应数据结构**:
```json
{
  "user": {"id": "KY19421"},
  "token": {"expiry": 14400.0},
  "permissions": [
    "BEFORE_AND_AFTER_PERFORMANCE_V2",
    "BRAIN_LABS", 
    "CONSULTANT",
    "MULTI_SIMULATION",
    "PROD_ALPHAS",
    "VISUALIZATION",
    "WORKDAY"
  ]
}
```

**基于旧代码的实现**:
```python
# 基于machine_lib.py的SessionManager实现
class SessionManager:
    def __init__(self, session, start_time, expiry_time=14400):
        self.session = session
        self.start_time = start_time
        self.expiry_time = expiry_time  # 4小时过期
    
    async def refresh_session(self):
        """会话刷新机制"""
        await self.session.close()
        self.session = await async_login()
        self.start_time = time.time()
    
    def is_expired(self):
        """检查会话是否过期"""
        return time.time() - self.start_time > self.expiry_time
```

**技术实现**:
- **会话存储**: 内存存储会话信息，支持本地文件持久化
- **令牌管理**: 基于expiry时间的自动刷新机制(默认14400秒=4小时)
- **权限管理**: 基于permissions数组的功能访问控制
- **多账户支持**: SessionManager类管理多个用户会话
- **异常处理**: 会话过期自动重新认证，支持aiohttp异步客户端

## 2. 因子挖掘引擎 (FactorEngine)

### 2.1 功能职责

```python
class FactorEngine:
    """因子挖掘核心引擎
    
    职责:
    - 多阶因子生成算法
    - 表达式组合优化
    - 并发回测管理
    - 断点续传机制
    - 因子质量评估
    """
    
    async def generate_first_order_factors(self, config: FactorConfig) -> List[Factor]
    async def generate_higher_order_factors(self, base_factors: List[Factor], order: int) -> List[Factor]
    async def batch_backtest_factors(self, factors: List[Factor]) -> List[BacktestResult]
    async def resume_from_checkpoint(self, checkpoint_id: str) -> None
    async def evaluate_factor_quality(self, factor: Factor) -> QualityScore
```

### 2.2 因子生成流程

```
┌─────────────────────────────────────────────────────────────────┐
│                         因子生成流程图                           │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  数据集配置 ──┐                                                  │
│             │                                                  │
│  操作符库 ────┼──► 因子表达式生成器 ──► 表达式验证器 ──► 因子队列  │
│             │        │                      │           │      │
│  字段列表 ────┘        │                      │           │      │
│                       ▼                      │           ▼      │
│             参数组合引擎 ◄─────────────────────┘      回测调度器   │
│                       │                                 │      │
│                       ▼                                 ▼      │
│             表达式优化器                          并发回测池      │
│                       │                                 │      │
│                       ▼                                 ▼      │
│             断点检查器 ◄─────────────────────────── 结果收集器   │
│                       │                                 │      │
│                       ▼                                 ▼      │
│             持久化存储 ◄─────────────────────────── 质量评估器   │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 2.3 算法设计

**基于旧代码的实际算法实现**:

**操作符分类** (基于machine_lib.py):
```python
# 基础操作符
basic_ops = ["log", "sqrt", "reverse", "inverse", "rank", "zscore", 
             "log_diff", "s_log_1p", "fraction", "quantile", "normalize", "scale_down"]

# 时间序列操作符  
ts_ops = ["ts_rank", "ts_zscore", "ts_delta", "ts_sum", "ts_product",
          "ts_ir", "ts_std_dev", "ts_mean", "ts_arg_min", "ts_arg_max", 
          "ts_min_diff", "ts_max_diff", "ts_returns", "ts_scale"]

# 分组操作符
group_ops = ["group_neutralize", "group_rank", "group_normalize", 
             "group_scale", "group_zscore"]

# 高级工具箱
arsenal = ["ts_moment", "ts_entropy", "ts_min_max_cps", "sigmoid",
           "ts_decay_exp_window", "ts_percentage", "vector_neut", "signed_power"]
```

**因子生成工厂函数**:
```python
def first_order_factory(fields, ops_set):
    """一阶因子生成工厂"""
    # 时间窗口参数
    days = [5, 22, 66, 120, 240]
    
    for field in fields:
        for op in ops_set:
            if op in ts_ops:
                for day in days:
                    yield f"{op}({field}, {day})"
            elif op in basic_ops:
                yield f"{op}({field})"
            # 其他操作符组合...

def ts_comp_factory(op, field, factor, paras):
    """复合时间序列因子工厂"""
    for para in paras:
        yield f"{op}({field}, {factor}, {para})"

def trade_when_factory(op, field, region, delay=1):
    """交易条件因子工厂"""
    # 生成trade_when类型的复杂因子
    # 基于地区和延迟参数生成条件表达式
```

**断点续传机制**:
```python
def read_completed_alphas(filepath):
    """读取已完成的因子表达式，支持断点续传"""
    completed_alphas = set()
    try:
        with open(filepath, mode='r') as f:
            for line in f:
                completed_alphas.add(line.strip())
    except FileNotFoundError:
        pass
    return completed_alphas
```

## 3. 因子检验系统 (FactorValidator)

### 3.1 功能职责

**基于check.py的实际实现**:

```python
def check_self_corr_test(s, alpha_id, threshold: float = 0.7):
    """检查因子自相关性
    
    Args:
        s: requests session
        alpha_id: 因子ID  
        threshold: 相关性阈值，默认0.7
    
    Returns:
        DataFrame包含检验结果
    """
    self_corr_df = get_self_corr(s, alpha_id)
    if self_corr_df.empty:
        result = {"test": "SELF_CORRELATION", "result": "PASS", "value": 0}
    else:
        value = self_corr_df["correlation"].max()
        result = {
            "test": "SELF_CORRELATION",
            "result": "PASS" if value < threshold else "FAIL",
            "value": value
        }
    return pd.DataFrame([result])

def check_prod_corr_test(s, alpha_id, threshold: float = 0.7):
    """检查因子生产相关性"""
    prod_corr_df = get_prod_corr(s, alpha_id)
    value = prod_corr_df[prod_corr_df.alphas > 0]["max"].max()
    result = {
        "test": "PROD_CORRELATION", 
        "result": "PASS" if value <= threshold else "FAIL",
        "value": value
    }
    return pd.DataFrame([result])

def check_alpha_by_self_prod(s, alpha, submitable_alpha_file, mode):
    """综合检验因子质量
    
    检验流程:
    1. 检查是否已经检验过
    2. 执行自相关性检验  
    3. 执行生产相关性检验(非USER模式)
    4. 通过检验的因子标记为GREEN，失败标记为RED
    5. 可提交因子保存到CSV文件
    """
    # 具体实现逻辑见旧代码check.py
```

**并发检验实现**:
```python
# 使用ThreadPoolExecutor实现并发检验
with ThreadPoolExecutor(max_workers=n_jobs) as executor:
    for alpha in chunk:
        executor.submit(check_alpha_by_self_prod, s, alpha, submitable_alpha_file, mode)
```

### 3.2 检验流程设计

```
┌─────────────────────────────────────────────────────────────────┐
│                         因子检验流程                             │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  待检验因子队列                                                  │
│        │                                                        │
│        ▼                                                        │
│  ┌─────────────────┐    ┌─────────────────┐                     │
│  │  并发任务分发    │────│  检验任务池      │                     │
│  └─────────────────┘    └─────────────────┘                     │
│        │                        │                               │
│        ▼                        ▼                               │
│  ┌─────────────────┐    ┌─────────────────┐                     │
│  │  自相关性检验    │    │  产品相关性检验  │                     │
│  └─────────────────┘    └─────────────────┘                     │
│        │                        │                               │
│        └────────┬───────────────┘                               │
│                 ▼                                               │
│  ┌─────────────────────────────────────┐                       │
│  │        结果聚合和评级                │                       │
│  └─────────────────────────────────────┘                       │
│                 │                                               │
│                 ▼                                               │
│  ┌─────────────────────────────────────┐                       │
│  │    可提交因子列表生成                │                       │
│  └─────────────────────────────────────┘                       │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

## 4. 因子提交系统 (SubmitManager)

### 4.1 功能职责

```python
class SubmitManager:
    """因子提交管理器
    
    职责:
    - 批量因子提交
    - 提交状态跟踪
    - 失败重试机制
    - 提交队列管理
    - 结果统计分析
    """
    
    async def submit_factor(self, factor: Factor, session: Session) -> SubmitResult
    async def batch_submit(self, factors: List[Factor]) -> List[SubmitResult]
    async def retry_failed_submissions(self, failed_submissions: List[SubmitResult]) -> List[SubmitResult]
    async def get_submission_statistics(self, date_range: DateRange) -> SubmissionStats
    def configure_retry_policy(self, max_retries: int, backoff_strategy: str) -> None
```

### 4.2 提交策略设计

- **智能排序**: 按自相关性升序排序，提高成功率
- **重试策略**: 指数退避算法，针对不同错误类型采用不同策略
- **并发控制**: 控制并发提交数量，避免触发平台限制
- **状态追踪**: 完整记录提交过程的每个状态变迁

## 5. 事件调度系统 (EventScheduler)

### 5.1 功能职责

```python
class EventScheduler:
    """事件驱动调度器
    
    职责:
    - 事件注册和监听
    - 工作流编排
    - 任务依赖管理
    - 异常传播处理
    - 流程状态管理
    """
    
    async def register_event_handler(self, event_type: str, handler: Callable) -> None
    async def emit_event(self, event: Event) -> None
    async def start_workflow(self, workflow_config: WorkflowConfig) -> WorkflowInstance
    async def handle_task_failure(self, task: Task, error: Exception) -> None
    def get_workflow_status(self, workflow_id: str) -> WorkflowStatus
```

### 5.2 基于旧代码的并发实现

```python
# 基于machine_lib.py的并发回测实现
async def simulate_multiple_alphas(alpha_list, region_list, n_jobs=5):
    """并发回测多个因子"""
    semaphore = asyncio.Semaphore(n_jobs)
    tasks = []
    
    for alpha, region in zip(alpha_list, region_list):
        task = simulate_single(session_manager, alpha, region, semaphore=semaphore)
        tasks.append(task)
    
    results = await asyncio.gather(*tasks)
    return results

async def simulate_single(session_manager, alpha_expression, region_info, 
                          semaphore=None):
    """单个因子回测"""
    async with semaphore:
        # 检查会话是否过期
        if session_manager.is_expired():
            await session_manager.refresh_session()
        
        # 执行回测逻辑
        result = await submit_simulation(alpha_expression, region_info)
        return result
```

---

**维护说明**: 此分片详细定义了系统的6大核心模块设计和实现方案，是开发团队的主要技术参考。