# 部署运维架构 - 技术架构分片

**分片版本**: v1.0  
**所属模块**: 部署运维  
**维护团队**: 运维团队  
**最后更新**: 2024年12月

## 1. 部署架构

### 1.1 单机部署架构

```
┌─────────────────────────────────────────────────────────────────┐
│                         单机部署架构                             │
├─────────────────────────────────────────────────────────────────┤
│                                                                 │
│  ┌─────────────────────────────────────────────────────────────┐ │
│  │                    应用进程空间                              │ │
│  │                                                             │ │
│  │  ┌─────────────────┐  ┌─────────────────┐                   │ │
│  │  │   主应用进程     │  │   工作进程池     │                   │ │
│  │  │   (main.py)     │  │ (asyncio tasks) │                   │ │
│  │  └─────────────────┘  └─────────────────┘                   │ │
│  │           │                     │                           │ │
│  │           └─────────┬───────────┘                           │ │
│  │                     │                                       │ │
│  └─────────────────────┼───────────────────────────────────────┘ │
│                        │                                         │
│  ┌─────────────────────┼───────────────────────────────────────┐ │
│  │                     │        文件系统                        │ │
│  │  ┌──────────────────▼─────┐   ┌─────────────────────────────┐ │ │
│  │  │     SQLite数据库       │   │         日志文件             │ │ │
│  │  │    data/wq.db          │   │       logs/app.log          │ │ │
│  │  └────────────────────────┘   └─────────────────────────────┘ │ │
│  │  ┌────────────────────────┐   ┌─────────────────────────────┐ │ │
│  │  │      配置文件           │   │       临时文件               │ │ │
│  │  │  src/config/config.json│   │      /tmp/wq_*              │ │ │
│  │  └────────────────────────┘   └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                        │                                         │
│  ┌─────────────────────┼───────────────────────────────────────┐ │
│  │                     │        网络层                          │ │
│  │  ┌──────────────────▼─────┐   ┌─────────────────────────────┐ │ │
│  │  │    aiohttp客户端       │───│    WQ平台API               │ │ │
│  │  │   (HTTP连接池)         │   │ (外部依赖)                  │ │ │
│  │  └────────────────────────┘   └─────────────────────────────┘ │ │
│  └─────────────────────────────────────────────────────────────┘ │
│                                                                 │
└─────────────────────────────────────────────────────────────────┘
```

### 1.2 环境配置

```bash
# 环境要求
OS: macOS 24.5.0 (Darwin)
Shell: zsh
Python: 3.9.13+

# 虚拟环境设置
python3 -m venv .venv
source .venv/bin/activate

# 依赖安装
pip3 install -r requirements.txt

# 目录结构
wq/
├── data/                # 数据存储目录
│   └── wq.db           # SQLite数据库
├── logs/               # 日志文件目录
│   └── app.log         # 应用日志
├── src/                # 源代码目录
│   ├── config/         # 配置文件
│   ├── lib/           # 核心库
│   └── app.py         # 应用入口
└── .venv/             # 虚拟环境
```

### 1.3 依赖管理

#### requirements.txt
```txt
# 核心依赖
aiohttp>=3.8.0
rich>=13.0.0
pandas>=1.5.0
numpy>=1.21.0

# 数据库
aiosqlite>=0.17.0

# 工具库
click>=8.0.0
pydantic>=1.10.0
python-dateutil>=2.8.0

# 开发依赖 (可选)
black>=22.0.0
isort>=5.10.0
```

## 2. 运维策略

### 2.1 启动脚本

```bash
#!/bin/zsh
# startup.sh - 系统启动脚本

set -e

# 函数定义
log_info() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [INFO] $1"
}

log_error() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [ERROR] $1" >&2
}

# 检查环境
check_environment() {
    log_info "🔍 检查运行环境..."
    
    # 检查Python版本
    python_version=$(python3 --version | cut -d' ' -f2)
    if [[ "$(printf '%s\n' "3.9.13" "$python_version" | sort -V | head -n1)" != "3.9.13" ]]; then
        log_error "Python版本过低，需要3.9.13+，当前版本: $python_version"
        exit 1
    fi
    
    # 检查虚拟环境
    if [ ! -d ".venv" ]; then
        log_error "虚拟环境不存在，请先创建虚拟环境"
        exit 1
    fi
    
    # 检查Shell环境
    if [ "$SHELL" != "/bin/zsh" ]; then
        log_error "请使用zsh shell环境"
        exit 1
    fi
    
    log_info "✅ 环境检查通过"
}

# 初始化系统
initialize_system() {
    log_info "🚀 初始化系统..."
    
    # 激活虚拟环境
    source .venv/bin/activate
    
    # 创建必要目录
    mkdir -p data logs backups
    
    # 检查配置文件
    if [ ! -f "src/config/config.json" ]; then
        log_error "配置文件不存在，请先创建配置文件"
        exit 1
    fi
    
    # 检查用户凭证
    if [ ! -f "user_info.txt" ]; then
        log_error "用户凭证文件不存在，请先配置user_info.txt"
        exit 1
    fi
    
    log_info "✅ 系统初始化完成"
}

# 初始化数据库
initialize_database() {
    log_info "🗄️ 初始化数据库..."
    
    if [ ! -f "data/wq.db" ]; then
        python3 -c "
from src.lib.db import DatabaseService
import asyncio

async def init_db():
    db = DatabaseService('data/wq.db')
    await db.initialize_schema()
    print('数据库初始化完成')

asyncio.run(init_db())
"
        log_info "✅ 数据库创建完成"
    else
        log_info "✅ 数据库已存在"
    fi
}

# 启动应用
start_application() {
    log_info "🎯 启动WQ因子挖掘系统..."
    
    # 设置环境变量
    export PYTHONPATH="$PWD:$PYTHONPATH"
    export WQ_ENV="production"
    
    # 启动主应用
    python3 src/app.py "$@"
}

# 主流程
main() {
    log_info "🌟 WQ因子挖掘系统启动中..."
    
    check_environment
    initialize_system
    initialize_database
    start_application "$@"
}

# 执行主流程
main "$@"
```

### 2.2 监控脚本

```bash
#!/bin/zsh
# monitor.sh - 系统监控脚本

# 配置参数
PROCESS_NAME="python3 src/app.py"
LOG_FILE="logs/app.log"
DB_FILE="data/wq.db"
MAX_LOG_SIZE=100  # MB
MAX_DB_SIZE=1000  # MB

# 日志函数
log_monitor() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') [MONITOR] $1"
}

# 检查进程状态
check_process() {
    log_monitor "检查应用进程状态..."
    
    if pgrep -f "$PROCESS_NAME" > /dev/null; then
        local pid=$(pgrep -f "$PROCESS_NAME")
        local cpu_usage=$(ps -p $pid -o %cpu --no-headers)
        local mem_usage=$(ps -p $pid -o %mem --no-headers)
        
        log_monitor "✅ 应用进程运行正常 (PID: $pid, CPU: ${cpu_usage}%, MEM: ${mem_usage}%)"
        return 0
    else
        log_monitor "❌ 应用进程未运行"
        return 1
    fi
}

# 检查数据库状态
check_database() {
    log_monitor "检查数据库状态..."
    
    if [ -f "$DB_FILE" ]; then
        local db_size=$(du -m "$DB_FILE" | cut -f1)
        
        if [ $db_size -lt $MAX_DB_SIZE ]; then
            log_monitor "✅ 数据库正常 (大小: ${db_size}MB)"
            return 0
        else
            log_monitor "⚠️ 数据库文件过大: ${db_size}MB"
            return 1
        fi
    else
        log_monitor "❌ 数据库文件不存在"
        return 1
    fi
}

# 检查日志文件
check_logs() {
    log_monitor "检查日志文件状态..."
    
    if [ -f "$LOG_FILE" ]; then
        local log_size=$(du -m "$LOG_FILE" | cut -f1)
        local error_count=$(tail -n 1000 "$LOG_FILE" | grep -c "ERROR" || echo 0)
        
        log_monitor "✅ 日志文件正常 (大小: ${log_size}MB, 最近错误: ${error_count}条)"
        
        if [ $log_size -gt $MAX_LOG_SIZE ]; then
            log_monitor "⚠️ 日志文件过大，建议轮转"
            return 1
        fi
        
        return 0
    else
        log_monitor "❌ 日志文件不存在"
        return 1
    fi
}

# 检查网络连接
check_network() {
    log_monitor "检查网络连接..."
    
    if curl -s --connect-timeout 5 https://api.worldquantbrain.com/health > /dev/null; then
        log_monitor "✅ WQ平台连接正常"
        return 0
    else
        log_monitor "❌ WQ平台连接失败"
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_monitor "检查磁盘空间..."
    
    local disk_usage=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ $disk_usage -lt 90 ]; then
        log_monitor "✅ 磁盘空间充足 (使用率: ${disk_usage}%)"
        return 0
    else
        log_monitor "⚠️ 磁盘空间不足 (使用率: ${disk_usage}%)"
        return 1
    fi
}

# 系统健康检查
health_check() {
    log_monitor "🔍 开始系统健康检查..."
    
    local failed_checks=0
    
    check_process || ((failed_checks++))
    check_database || ((failed_checks++))
    check_logs || ((failed_checks++))
    check_network || ((failed_checks++))
    check_disk_space || ((failed_checks++))
    
    if [ $failed_checks -eq 0 ]; then
        log_monitor "✅ 系统运行正常"
        return 0
    else
        log_monitor "❌ 系统存在 $failed_checks 个问题"
        return 1
    fi
}

# 性能报告
performance_report() {
    log_monitor "📊 生成性能报告..."
    
    # 系统负载
    local load_avg=$(uptime | awk -F'load average:' '{print $2}')
    log_monitor "系统负载: $load_avg"
    
    # 内存使用
    local memory_info=$(free -h | grep Mem)
    log_monitor "内存使用: $memory_info"
    
    # 进程信息
    if pgrep -f "$PROCESS_NAME" > /dev/null; then
        local pid=$(pgrep -f "$PROCESS_NAME")
        local process_info=$(ps -p $pid -o pid,ppid,pcpu,pmem,etime,cmd --no-headers)
        log_monitor "进程信息: $process_info"
    fi
}

# 日志轮转
rotate_logs() {
    log_monitor "🔄 执行日志轮转..."
    
    if [ -f "$LOG_FILE" ]; then
        local timestamp=$(date '+%Y%m%d_%H%M%S')
        local archive_file="logs/app_${timestamp}.log"
        
        cp "$LOG_FILE" "$archive_file"
        > "$LOG_FILE"  # 清空当前日志文件
        
        # 压缩归档日志
        gzip "$archive_file"
        
        log_monitor "✅ 日志轮转完成: ${archive_file}.gz"
        
        # 清理旧日志文件
        find logs -name "app_*.log.gz" -mtime +30 -delete
    fi
}

# 主函数
main() {
    case "${1:-health}" in
        "health")
            health_check
            ;;
        "report")
            performance_report
            ;;
        "rotate")
            rotate_logs
            ;;
        "all")
            health_check
            performance_report
            ;;
        *)
            echo "使用方法: $0 {health|report|rotate|all}"
            exit 1
            ;;
    esac
}

main "$@"
```

### 2.3 部署清单

#### 部署前检查
```bash
# 部署前检查脚本
#!/bin/zsh
# pre_deploy_check.sh

echo "🔍 部署前环境检查..."

# 检查Python版本
python_version=$(python3 --version | cut -d' ' -f2)
echo "Python版本: $python_version"

# 检查Shell环境
echo "Shell环境: $SHELL"

# 检查操作系统
echo "操作系统: $(uname -a)"

# 检查磁盘空间
echo "磁盘空间:"
df -h .

# 检查网络连接
echo "网络连接测试:"
curl -s --connect-timeout 5 https://api.worldquantbrain.com/health && echo "WQ平台连接正常" || echo "WQ平台连接失败"

# 检查依赖包
echo "检查核心依赖..."
python3 -c "
import sys
required_packages = ['aiohttp', 'rich', 'pandas', 'sqlite3']
missing = []
for pkg in required_packages:
    try:
        __import__(pkg)
        print(f'✅ {pkg}')
    except ImportError:
        print(f'❌ {pkg}')
        missing.append(pkg)

if missing:
    print(f'缺少依赖: {missing}')
    sys.exit(1)
else:
    print('✅ 所有依赖包已安装')
"

echo "✅ 部署前检查完成"
```

## 3. 日志管理

### 3.1 日志架构

```python
class LoggingService:
    """日志服务"""
    
    def setup_loggers(self, config: LoggingConfig):
        """设置日志器"""
        
        # 应用日志
        self.app_logger = logging.getLogger("wq.app")
        self.app_logger.setLevel(logging.INFO)
        
        # API调用日志
        self.api_logger = logging.getLogger("wq.api")
        self.api_logger.setLevel(logging.DEBUG)
        
        # 性能日志
        self.perf_logger = logging.getLogger("wq.performance")
        self.perf_logger.setLevel(logging.INFO)
        
        # 错误日志
        self.error_logger = logging.getLogger("wq.error")
        self.error_logger.setLevel(logging.ERROR)
        
        # 设置处理器
        self._setup_handlers(config)
        
    def _setup_handlers(self, config: LoggingConfig):
        """设置日志处理器"""
        
        # 文件处理器 - 按日期轮转
        file_handler = TimedRotatingFileHandler(
            filename=config.log_file,
            when='midnight',
            interval=1,
            backupCount=30,
            encoding='utf-8'
        )
        
        # 控制台处理器 - Rich美化输出
        console_handler = RichHandler(
            console=Console(stderr=True),
            show_time=True,
            show_level=True,
            show_path=True
        )
        
        # 设置格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        file_handler.setFormatter(formatter)
        
        # 添加处理器到所有日志器
        for logger_name in ["wq.app", "wq.api", "wq.performance", "wq.error"]:
            logger = logging.getLogger(logger_name)
            logger.addHandler(file_handler)
            logger.addHandler(console_handler)
```

### 3.2 监控与告警

```python
class SystemMonitor:
    """系统监控器"""
    
    def __init__(self, config: MonitorConfig):
        self.config = config
        self.metrics = {}
        
    async def collect_system_metrics(self):
        """收集系统指标"""
        
        # CPU使用率
        cpu_usage = psutil.cpu_percent(interval=1)
        
        # 内存使用率
        memory = psutil.virtual_memory()
        memory_usage = memory.percent
        
        # 磁盘使用率
        disk = psutil.disk_usage('.')
        disk_usage = disk.percent
        
        # 进程信息
        process = psutil.Process()
        process_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        self.metrics.update({
            'cpu_usage': cpu_usage,
            'memory_usage': memory_usage,
            'disk_usage': disk_usage,
            'process_memory': process_memory,
            'timestamp': datetime.now()
        })
        
        # 检查告警阈值
        await self._check_alerts()
    
    async def _check_alerts(self):
        """检查告警条件"""
        
        alerts = []
        
        if self.metrics['cpu_usage'] > 80:
            alerts.append(f"CPU使用率过高: {self.metrics['cpu_usage']}%")
            
        if self.metrics['memory_usage'] > 80:
            alerts.append(f"内存使用率过高: {self.metrics['memory_usage']}%")
            
        if self.metrics['disk_usage'] > 90:
            alerts.append(f"磁盘使用率过高: {self.metrics['disk_usage']}%")
        
        if alerts:
            await self._send_alerts(alerts)
    
    async def _send_alerts(self, alerts: List[str]):
        """发送告警信息"""
        alert_message = f"系统告警:\n" + "\n".join(alerts)
        
        # 记录到错误日志
        error_logger = logging.getLogger("wq.error")
        error_logger.error(alert_message)
        
        # 可扩展: 发送邮件、短信等
```

## 4. 安全运维

### 4.1 安全配置

```python
class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.encryption_key = self._load_or_create_key()
    
    def _load_or_create_key(self) -> bytes:
        """加载或创建加密密钥"""
        key_path = "data/.encryption_key"
        
        if os.path.exists(key_path):
            with open(key_path, 'rb') as f:
                return f.read()
        else:
            key = Fernet.generate_key()
            with open(key_path, 'wb') as f:
                f.write(key)
            os.chmod(key_path, 0o600)  # 仅所有者可读写
            return key
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        cipher = Fernet(self.encryption_key)
        return cipher.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        cipher = Fernet(self.encryption_key)
        return cipher.decrypt(encrypted_data.encode()).decode()
```

### 4.2 访问控制

```python
class AccessController:
    """访问控制器"""
    
    def __init__(self, permissions: List[str]):
        self.permissions = set(permissions)
    
    def check_permission(self, required_permission: str) -> bool:
        """检查权限"""
        return required_permission in self.permissions
    
    def require_permission(self, permission: str):
        """权限装饰器"""
        def decorator(func):
            def wrapper(*args, **kwargs):
                if not self.check_permission(permission):
                    raise PermissionError(f"需要权限: {permission}")
                return func(*args, **kwargs)
            return wrapper
        return decorator
```

---

**维护说明**: 此分片提供完整的部署运维方案，包括环境配置、启动脚本、监控告警和安全管理。