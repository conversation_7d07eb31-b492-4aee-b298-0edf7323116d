# WQ因子挖掘系统 - 技术架构文档分片

**文档版本**: v1.0  
**创建日期**: 2024年12月  
**系统架构师**: Winston  
**最后更新**: 2024年12月

## 🏗️ 分片索引

本目录包含 WQ因子挖掘系统的技术架构文档分片，提供完整的系统设计和实现指南。

### 📖 分片列表

| 序号 | 分片文件 | 主要内容 | 负责团队 |
|-----|---------|---------|---------|
| 01 | [系统架构概述](01-系统架构概述.md) | 整体架构、设计原则、技术栈选型、分层设计 | 架构团队 |
| 02 | [核心模块设计](02-核心模块设计.md) | 6大核心模块详细设计、基于旧代码的实现方案 | 架构团队 |
| 03 | [数据架构设计](03-数据架构设计.md) | 数据库设计、数据访问层、配置管理、备份策略 | 架构团队 |
| 04 | [部署运维架构](04-部署运维架构.md) | 部署策略、监控脚本、日志管理、安全运维 | 运维团队 |

## 🎯 架构特性概览

### 核心设计原则
- **事件驱动架构**: 基于发布-订阅模式的松耦合组件设计
- **异步优先**: 全面采用异步编程，提升并发处理能力
- **容错为先**: 多层次容错机制，确保系统稳定性
- **MVP导向**: 避免过度设计，专注核心功能
- **数据中心化**: 基于SQLite的集中式状态管理
- **可观测性**: 完整的日志、监控和指标体系

### 技术栈验证

| 技术组件 | 版本要求 | 旧代码验证 | 使用场景 |
|---------|---------|-----------|---------|
| **Python** | 3.9.13+ | ✅ machine_lib.py | 主要开发语言 |
| **aiohttp** | 3.8+ | ✅ async_login() | 异步HTTP客户端 |
| **SQLite** | 3.31+ | ✅ 配合pandas使用 | 轻量级数据库 |
| **rich** | 13.0+ | ✅ logger输出 | 控制台美化 |
| **asyncio** | 内置 | ✅ simulate_multiple_alphas | 异步编程框架 |
| **ThreadPoolExecutor** | 内置 | ✅ check.py并发检验 | 线程池并发 |

## 🔄 系统流程概览

### 事件驱动流程
```
一阶因子生成 → 二阶因子生成 → 三阶因子生成 → 四阶因子生成 → 因子检验 → 因子提交
```

### 分层架构
```
应用层 (CLI Interface, Config API, Monitor API)
    ↓
业务层 (SessionManager, FactorEngine, SubmitManager, EventScheduler)
    ↓  
服务层 (WQApiClient, DatabaseService, LoggingService, ConfigService)
    ↓
基础设施层 (SQLite, FileSystem, aiohttp)
    ↓
外部依赖 (WorldQuant Brain Platform)
```

## 📊 核心模块功能

### 1. 会话管理系统 (SessionManager)
- **认证管理**: WQ平台身份认证和令牌管理
- **会话续传**: 4小时自动刷新机制
- **权限控制**: 基于permissions数组的访问控制
- **多账户**: 支持多用户会话管理

### 2. 因子挖掘引擎 (FactorEngine)
- **多阶生成**: 1-4阶因子递进生成
- **工厂模式**: 基于旧代码验证的工厂函数
- **并发回测**: asyncio + semaphore并发控制
- **断点续传**: 基于文件记录的续传机制

### 3. 因子检验系统 (FactorValidator)
- **相关性检验**: 自相关性和产品相关性检验
- **并发处理**: ThreadPoolExecutor并发检验
- **阈值管理**: 可配置的检验阈值(默认0.7)
- **结果评级**: 自动生成可提交因子列表

### 4. 因子提交系统 (SubmitManager)
- **批量提交**: 智能排序和批量提交管理
- **重试策略**: 指数退避的智能重试机制
- **状态跟踪**: 完整的提交状态记录
- **成功率统计**: 提交结果统计分析

### 5. 数据管理系统 (DataManager)
- **SQLite存储**: 轻量级数据库设计
- **Repository模式**: 数据访问层抽象
- **事务管理**: 完整的事务处理机制
- **备份恢复**: 自动备份和数据恢复

### 6. 监控统计系统 (MetricsCollector)
- **实时监控**: 系统资源和任务进度监控
- **性能指标**: CPU、内存、磁盘使用监控
- **告警机制**: 可配置的阈值告警
- **统计报告**: 多维度性能分析报告

## 🗄️ 数据架构

### 核心数据表
- **factors**: 因子信息和状态管理
- **sessions**: 会话管理和令牌存储
- **workflow_tasks**: 任务执行和进度跟踪
- **configurations**: 系统配置参数
- **statistics**: 监控指标和统计数据

### 数据流设计
```
配置文件 → 配置服务 → 事件调度器 → 因子挖掘引擎
    ↓           ↓           ↓            ↓
用户输入 → 会话管理器 → 数据库服务 → 监控收集器
    ↓           ↓           ↓            ↓
WQ平台 → API客户端 → 文件系统 → 日志服务
```

## 🚀 部署架构

### 单机部署
- **环境要求**: macOS 24.5.0, Python 3.9.13+, zsh
- **虚拟环境**: 必须使用.venv虚拟环境
- **目录结构**: data/, logs/, src/, .venv/
- **启动方式**: 一键启动脚本

### 运维脚本
- **startup.sh**: 环境检查和系统启动
- **monitor.sh**: 健康检查和性能监控
- **backup.sh**: 数据备份和日志轮转

### 监控告警
- **系统监控**: CPU、内存、磁盘使用率
- **应用监控**: 进程状态、API响应时间
- **业务监控**: 因子生成成功率、提交成功率

## 🔒 安全架构

### 身份认证
- **WQ平台认证**: 基于真实认证接口
- **会话安全**: 自动令牌过期和刷新
- **权限控制**: 基于permissions的功能访问

### 数据安全
- **敏感信息加密**: 用户凭证本地加密存储
- **传输安全**: 所有API调用使用HTTPS
- **访问控制**: 文件权限和目录访问限制

## 📈 性能架构

### 并发模型
- **异步IO**: 基于asyncio的非阻塞IO
- **混合并发**: asyncio + ThreadPoolExecutor
- **连接复用**: HTTP连接池和会话管理
- **资源控制**: 内存和CPU资源动态控制

### 性能指标
- **并发处理**: 支持1000+因子并发回测
- **响应时间**: API调用<5秒
- **吞吐量**: >500因子/小时
- **资源使用**: 内存<4GB，CPU<80%

## 🔧 开发指南

### 模块开发
1. **阅读架构分片**: 理解模块设计和依赖关系
2. **参考旧代码**: 基于src_xin/的实现模式
3. **遵循设计原则**: MVP、异步优先、容错为先
4. **测试验证**: 使用真实数据进行验证

### 扩展指南
1. **配置驱动**: 通过JSON配置扩展功能
2. **工厂模式**: 使用工厂函数扩展因子生成
3. **事件驱动**: 通过事件机制扩展流程
4. **插件化**: 简单的模块化扩展方案

---

## 📞 联系方式

- **架构问题**: 联系架构团队
- **部署问题**: 联系运维团队
- **性能问题**: 联系架构团队
- **安全问题**: 联系安全团队

## 🔄 变更记录

| 版本 | 日期 | 变更内容 | 变更人 |
|-----|------|---------|--------|
| v1.0 | 2024年12月 | 初始版本，完成架构分片化，基于旧代码验证 | 架构团队 |

---

**维护说明**: 此索引文件提供技术架构分片的整体导航和使用指南，是开发团队理解系统架构的入口点。