# 产品概述 - PRD分片

**分片版本**: v1.0  
**所属模块**: 产品定义  
**维护团队**: 产品团队  
**最后更新**: 2024年12月

## 1. 产品定位

WQ因子挖掘系统是一个专业的量化投研自动化平台，通过程序化调用WorldQuant(WQ)平台接口，自动生成、回测、验证和提交高质量的α因子，从而获取平台奖励。该系统将替代传统的手工操作方式，大幅提升因子研发效率。

## 2. 业务背景

- **传统痛点**: 使用WQ平台web界面进行因子模拟、回测、提交需要大量人工操作，效率低下
- **市场机会**: WQ平台提供丰富的奖励机制，高质量因子可获得高额回报
- **技术优势**: 通过程序化接口调用，可实现自动化的因子挖掘流程

## 3. 目标用户

- **量化研究员**: 需要批量生成和测试α因子的专业人员
- **量化团队**: 参与WQ竞赛和因子研发的团队
- **个人投资者**: 希望通过自动化工具参与量化竞赛的个人用户

## 4. 核心价值

- 🚀 **效率提升**: 自动化替代手工操作，提升10倍以上因子研发效率
- 🎯 **质量保证**: 基于规则的系统化因子生成和质量检验
- 💰 **收益最大化**: 优化因子质量，提高平台奖励获得概率
- 🛡️ **稳定可靠**: 7x24小时无人值守运行，强健的容错机制

## 5. 业务流程核心

### 5.1 α因子基础知识

**因子定义**: α因子的目标是捕捉那些尚未被市场充分定价的信息，从而在不同市场环境下（无论上涨、下跌或震荡）都能产生正收益。

**因子生成要素**:
- 数据集操作符 (Operators)
- 数据字段 (Fields)
- 参数配置 (Parameters)

### 5.2 因子生成流程

**一阶因子生成**:
1. **确定基础数据字段**: 调用WQ平台接口动态获取指定数据集下的可用字段，筛选"matrix"和"vector"类型字段
2. **确定操作符**: 包括时间序列操作符(ts_ops)和基础数学操作符(basic_ops)
3. **生成表达式**: 通过笛卡尔积将字段与操作符组合，生成大量候选α表达式
4. **参数确定**: 应用预定义的时间窗口参数([5, 22, 66, 120, 240]天)

**多阶因子生成**:
- 基于一阶因子结果生成二阶因子
- 根据turnover智能调整decay参数
- 支持复杂的trade_when交易逻辑

### 5.3 业务处理流程

#### 会话管理
- 通过WQ平台`/authentication`接口进行登录认证
- 获取用户权限列表和访问限制
- 保存会话信息，支持会话续传和自动刷新

#### 因子回测
- 调用WQ平台`/simulations`接口进行因子回测
- 获取因子质量评估指标
- 支持并发回测，提高处理效率

#### 因子检验
- 自动检查因子的自相关性和产品相关性
- 支持多线程并发检验
- 生成可提交因子列表

#### 因子提交
- 将高质量因子提交到WQ平台
- 跟踪提交状态和结果
- 处理提交失败和重试逻辑

---

**维护说明**: 此分片包含产品的核心定义和业务流程概述，是理解整个系统的基础。