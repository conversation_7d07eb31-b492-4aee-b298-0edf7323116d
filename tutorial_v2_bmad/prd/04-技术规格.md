# 技术规格 - PRD分片

**分片版本**: v1.0  
**所属模块**: 技术规范  
**维护团队**: 架构团队  
**最后更新**: 2024年12月

## 1. 技术架构

### 1.1 开发环境
- **Python版本**: 3.9.13+
- **虚拟环境**: 必须使用.venv
- **Shell环境**: zsh
- **操作系统**: macOS 24.5.0 (Darwin)

### 1.2 核心技术栈

| 技术组件 | 版本要求 | 使用场景 |
|---------|---------|---------|
| **Python** | 3.9.13+ | 主要开发语言 |
| **aiohttp** | 3.8+ | 异步HTTP客户端 |
| **SQLite** | 3.31+ | 轻量级数据库 |
| **rich** | 13.0+ | 控制台美化输出 |
| **pandas** | 1.5+ | 数据处理和分析 |
| **asyncio** | 内置 | 异步编程框架 |

### 1.3 架构模式
- **异步优先**: 全面采用异步编程模型
- **事件驱动**: 基于事件的流程编排
- **分层架构**: 清晰的应用层、业务层、服务层划分
- **配置驱动**: JSON配置文件管理系统参数

## 2. 系统集成

### 2.1 WQ平台接口集成

| HTTP方法 | 接口路径 | 描述 | 使用频率 |
|---------|---------|------|---------|
| POST | /authentication | 用户登录认证 | 每4小时 |
| GET | /operators | 获取可用操作符 | 启动时 |
| GET | /alphas/{alpha_id} | 获取Alpha详细信息 | 高频 |
| POST | /simulations | 提交Alpha进行回测 | 高频 |
| GET | /data-sets | 获取数据集列表 | 启动时 |
| GET | /data-fields | 获取数据字段 | 启动时 |
| GET | /users/self/alphas | 获取用户Alpha列表 | 中频 |
| POST | /alphas/{alpha_id}/submit | 提交Alpha到平台 | 中频 |

### 2.2 认证集成
**认证响应格式**:
```json
{
  "user": {"id": "KY19421"},
  "token": {"expiry": 14400.0},
  "permissions": [
    "BEFORE_AND_AFTER_PERFORMANCE_V2",
    "BRAIN_LABS",
    "CONSULTANT", 
    "MULTI_SIMULATION",
    "PROD_ALPHAS",
    "VISUALIZATION",
    "WORKDAY"
  ]
}
```

**权限映射**:
- `CONSULTANT`: 因子检验权限
- `MULTI_SIMULATION`: 批量回测权限
- `PROD_ALPHAS`: 生产环境访问权限

## 3. 数据模型

### 3.1 核心数据表

#### 因子信息表 (factors)
```sql
CREATE TABLE factors (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    expression TEXT NOT NULL UNIQUE,           -- 因子表达式
    factor_type VARCHAR(20) NOT NULL,          -- 因子类型 (1st, 2nd, 3rd, 4th)
    dataset_id VARCHAR(50) NOT NULL,           -- 数据集ID
    region VARCHAR(10) NOT NULL,               -- 地区
    universe VARCHAR(20) NOT NULL,             -- 股票池
    
    -- 回测结果
    sharpe REAL,                               -- 夏普比率
    returns REAL,                              -- 年化收益率
    turnover REAL,                             -- 换手率
    fitness REAL,                              -- 适应度分数
    
    -- 检验结果
    self_correlation REAL,                     -- 自相关性
    prod_correlation REAL,                     -- 产品相关性
    is_submittable BOOLEAN DEFAULT FALSE,      -- 是否可提交
    
    -- 提交状态
    submit_status VARCHAR(20) DEFAULT 'PENDING', -- 提交状态
    submit_attempt_count INTEGER DEFAULT 0,    -- 提交尝试次数
    submit_result TEXT,                        -- 提交结果详情
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    submitted_at TIMESTAMP
);
```

#### 会话管理表 (sessions)
```sql
CREATE TABLE sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    user_id VARCHAR(100) NOT NULL,            -- 用户ID
    session_token TEXT NOT NULL,              -- 会话令牌
    refresh_token TEXT,                       -- 刷新令牌
    expires_at TIMESTAMP NOT NULL,            -- 过期时间
    permissions TEXT,                         -- 用户权限(JSON)
    is_active BOOLEAN DEFAULT TRUE,           -- 是否活跃
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE(user_id, is_active)
);
```

#### 任务执行表 (workflow_tasks)
```sql
CREATE TABLE workflow_tasks (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    task_name VARCHAR(100) NOT NULL,          -- 任务名称
    task_type VARCHAR(50) NOT NULL,           -- 任务类型
    parent_task_id INTEGER,                   -- 父任务ID
    dataset_id VARCHAR(50),                   -- 关联数据集
    region VARCHAR(10),                       -- 地区
    universe VARCHAR(20),                     -- 股票池
    
    -- 任务配置
    task_config TEXT,                         -- 任务配置(JSON)
    priority INTEGER DEFAULT 5,              -- 优先级
    
    -- 进度信息
    status VARCHAR(20) DEFAULT 'PENDING',     -- 任务状态
    progress INTEGER DEFAULT 0,              -- 进度百分比
    total_items INTEGER DEFAULT 0,           -- 总项目数
    completed_items INTEGER DEFAULT 0,       -- 已完成项目数
    failed_items INTEGER DEFAULT 0,          -- 失败项目数
    
    -- 时间信息
    estimated_duration INTEGER,              -- 预估持续时间(秒)
    started_at TIMESTAMP,                    -- 开始时间
    completed_at TIMESTAMP,                 -- 完成时间
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY(parent_task_id) REFERENCES workflow_tasks(id)
);
```

### 3.2 数据存储路径
- **数据库文件**: `data/wq.db`
- **日志文件**: `logs/app.log`
- **配置文件**: `src/config/config.json`
- **用户凭证**: `user_info.txt` (加密存储)

### 3.3 配置文件结构
```json
{
  "database": {
    "path": "data/wq.db",
    "auto_create": true
  },
  "wq_platform": {
    "base_url": "https://api.worldquantbrain.com",
    "timeout": 30,
    "max_retries": 3
  },
  "factor_generation": {
    "time_windows": [5, 22, 66, 120, 240],
    "max_factors_per_batch": 1000,
    "concurrent_simulations": 10
  },
  "validation": {
    "self_correlation_threshold": 0.7,
    "prod_correlation_threshold": 0.7,
    "validation_modes": ["USER", "CONSULTANT"]
  },
  "logging": {
    "level": "INFO",
    "file": "logs/app.log",
    "rotation": "daily",
    "retention": 30
  }
}
```

## 4. 设计原则

### 4.1 开发原则
- **MVP原则**: 最小化可行功能，避免过度设计
- **循序渐进**: 先实现基本功能，再逐步优化
- **模块化设计**: 清晰的模块划分，便于维护扩展
- **事件驱动**: 基于事件的自动化流程编排

### 4.2 用户体验原则
- **操作便捷**: 对无技术基础用户友好
- **一键启动**: 配置好参数后一键启动
- **结果可视**: 方便查看执行结果和统计信息
- **简化配置**: 避免复杂的CLI命令和选项

### 4.3 技术原则
- **容错优先**: 强健的异常处理和恢复机制
- **性能优化**: 充分利用并发和异步处理
- **日志完善**: 完整的操作日志和状态跟踪
- **配置灵活**: JSON配置文件，支持运行时调整

---

**维护说明**: 此分片定义了系统的技术规格和实现标准，是开发团队的核心技术参考。