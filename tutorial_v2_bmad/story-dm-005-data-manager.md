# Story DM-005: 数据管理系统

## 📋 Story 信息

**作为** 系统管理员和量化研究员  
**我希望** 系统能提供可靠的数据持久化和查询服务  
**以便** 所有Alpha数据、会话信息、任务状态都能安全存储和高效访问

**Epic**: 基础架构模块  
**优先级**: P0 (必须有)  
**估算**: 8 Story Points  
**Status**: 🚧 Ready for Development  
**依赖**: 无依赖 (基础设施模块)

## ✅ 验收条件

### 核心功能验收
- [ ] **自动初始化**: 系统启动时自动创建数据库结构，无需手动设置
- [ ] **Alpha数据管理**: 完整记录Alpha生成、回测、检验、提交的全生命周期
- [ ] **会话状态管理**: 安全存储和管理用户会话信息
- [ ] **任务执行记录**: 记录所有工作流任务的执行状态和进度
- [ ] **配置参数存储**: 系统配置的持久化和版本管理

### 技术验收
- [ ] **Repository模式**: 实现数据访问层抽象，支持异步操作
- [ ] **事务管理**: 提供完整的事务控制，保证数据一致性
- [ ] **连接池管理**: 高效的数据库连接管理和复用
- [ ] **索引优化**: 关键查询字段的索引优化
- [ ] **错误处理**: 数据库异常的优雅处理和恢复

### 业务验收
- [ ] **多维度查询**: 支持按状态、时间、类型等多维度数据查询
- [ ] **性能统计**: 提供数据库操作的性能监控和统计
- [ ] **数据完整性**: 数据约束和验证规则确保数据质量
- [ ] **备份恢复**: 自动备份机制和数据恢复功能

## 🔧 技术任务

### 任务 1: 实现数据库架构和Schema

**基于**: `tutorial_v2_bmad/architecture/03-数据架构设计.md` 第8-148行

- [ ] **子任务 1.1**: 创建数据库Schema
  ```sql
  -- Alpha信息表
CREATE TABLE alphas (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    alpha_expression TEXT NOT NULL UNIQUE,           -- Alpha表达式
    step INTEGER NOT NULL,          -- Alpha步数 (1, 2, 3, 4)
      dataset_id VARCHAR(50) NOT NULL,           -- 数据集ID
      region VARCHAR(10) NOT NULL,               -- 地区
      universe VARCHAR(20) NOT NULL,             -- 股票池
      
      -- 回测结果
      sharpe REAL,                               -- 夏普比率
      returns REAL,                              -- 年化收益率
      turnover REAL,                             -- 换手率
      fitness REAL,                              -- 适应度分数
      
      -- 检验结果
      self_correlation REAL,                     -- 自相关性
      prod_correlation REAL,                     -- 产品相关性
      is_submittable BOOLEAN DEFAULT FALSE,      -- 是否可提交
      
      -- 提交状态
      submit_status VARCHAR(20) DEFAULT 'PENDING', -- 提交状态
      submit_attempt_count INTEGER DEFAULT 0,    -- 提交尝试次数
      submit_result TEXT,                        -- 提交结果详情
      
      -- 时间戳
      created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
      submitted_at TIMESTAMP
  );
  ```

- [ ] **子任务 1.2**: 创建索引和约束
  - 为高频查询字段创建索引
  - 实现数据完整性约束
  - 设置外键关系

- [ ] **子任务 1.3**: 实现数据库初始化管理器
  ```python
  class DatabaseInitializer:
      def __init__(self, db_path: str):
          self.db_path = db_path
          
      async def init_database(self) -> bool:
          """初始化数据库结构"""
          await self.create_tables()
          await self.create_indexes()
          await self.create_triggers()
          return True
          
      async def migrate_schema(self, target_version: int) -> bool:
          """数据库Schema迁移"""
          pass
  ```

### 任务 2: 实现Repository模式数据访问层

**基于**: `tutorial_v2_bmad/architecture/03-数据架构设计.md` 第151-220行

- [ ] **子任务 2.1**: 实现基础Repository类
  ```python
  class BaseRepository:
      """基础数据仓库类"""
      
      def __init__(self, db_manager: DatabaseManager):
          self.db = db_manager
          
      async def create(self, entity: dict) -> int:
          """创建实体"""
          pass
          
      async def get_by_id(self, entity_id: int) -> Optional[dict]:
          """根据ID获取实体"""
          pass
          
      async def update(self, entity_id: int, updates: dict) -> bool:
          """更新实体"""
          pass
          
      async def delete(self, entity_id: int) -> bool:
          """删除实体"""
          pass
  ```

- [ ] **子任务 2.2**: 实现FactorRepository
  ```python
  class FactorRepository(BaseRepository):
      """因子数据仓库"""
      
      async def create_factor(self, factor: FactorEntity) -> int:
          """创建因子记录"""
          query = """
          INSERT INTO factors (expression, factor_type, dataset_id, region, universe)
          VALUES (?, ?, ?, ?, ?)
          """
          return await self.db.execute_update(query, (
              factor.expression, factor.factor_type, factor.dataset_id,
              factor.region, factor.universe
          ))
          
      async def get_factors_by_status(self, status: str) -> List[FactorEntity]:
          """根据状态获取因子列表"""
          query = "SELECT * FROM factors WHERE submit_status = ? ORDER BY created_at DESC"
          rows = await self.db.execute_query(query, (status,))
          return [FactorEntity.from_dict(row) for row in rows]
          
      async def update_backtest_result(self, factor_id: int, result: BacktestResult) -> bool:
          """更新回测结果"""
          pass
          
      async def get_submittable_factors(self) -> List[FactorEntity]:
          """获取可提交的因子"""
          query = """
          SELECT * FROM factors 
          WHERE is_submittable = TRUE AND submit_status = 'PENDING'
          ORDER BY self_correlation ASC
          """
          rows = await self.db.execute_query(query)
          return [FactorEntity.from_dict(row) for row in rows]
  ```

- [ ] **子任务 2.3**: 实现SessionRepository和TaskRepository
  - SessionRepository: 会话管理数据访问
  - TaskRepository: 任务执行记录管理
  - StatisticsRepository: 统计数据管理

### 任务 3: 实现DatabaseService核心服务

**基于**: `tutorial_v2_bmad/architecture/03-数据架构设计.md` 第222-250行

- [ ] **子任务 3.1**: 实现异步数据库服务
  ```python
  class DatabaseService:
      """数据库服务"""
      
      def __init__(self, db_path: str = "data/wq.db"):
          self.db_path = db_path
          self.connection_pool = None
          
      async def execute_query(self, query: str, params: tuple = ()) -> List[dict]:
          """执行查询"""
          async with aiosqlite.connect(self.db_path) as db:
              db.row_factory = aiosqlite.Row
              async with db.execute(query, params) as cursor:
                  rows = await cursor.fetchall()
                  return [dict(row) for row in rows]
          
      async def execute_update(self, query: str, params: tuple = ()) -> int:
          """执行更新"""
          async with aiosqlite.connect(self.db_path) as db:
              await db.execute(query, params)
              await db.commit()
              return db.total_changes
          
      async def begin_transaction(self) -> 'Transaction':
          """开始事务"""
          return Transaction(self.db_path)
  ```

- [ ] **子任务 3.2**: 实现事务管理
  ```python
  class Transaction:
      """数据库事务管理器"""
      
      def __init__(self, db_path: str):
          self.db_path = db_path
          self.connection = None
          
      async def __aenter__(self):
          self.connection = await aiosqlite.connect(self.db_path)
          await self.connection.execute("BEGIN")
          return self
          
      async def __aexit__(self, exc_type, exc_val, exc_tb):
          if exc_type:
              await self.connection.rollback()
          else:
              await self.connection.commit()
          await self.connection.close()
  ```

- [ ] **子任务 3.3**: 实现连接池和性能优化
  - 数据库连接池管理
  - 查询性能监控
  - 慢查询优化
  - 内存使用控制

### 任务 4: 实现数据实体和业务模型

- [ ] **子任务 4.1**: 实现数据实体类
  ```python
  @dataclass
  class FactorEntity:
      """因子实体类"""
      id: Optional[int] = None
      expression: str = ""
      factor_type: str = ""
      dataset_id: str = ""
      region: str = ""
      universe: str = ""
      
      # 回测结果
      sharpe: Optional[float] = None
      returns: Optional[float] = None
      turnover: Optional[float] = None
      fitness: Optional[float] = None
      
      # 检验结果
      self_correlation: Optional[float] = None
      prod_correlation: Optional[float] = None
      is_submittable: bool = False
      
      # 提交状态
      submit_status: str = "PENDING"
      submit_attempt_count: int = 0
      submit_result: Optional[str] = None
      
      # 时间戳
      created_at: Optional[datetime] = None
      updated_at: Optional[datetime] = None
      submitted_at: Optional[datetime] = None
      
      @classmethod
      def from_dict(cls, data: dict) -> 'FactorEntity':
          """从字典创建实体"""
          pass
          
      def to_dict(self) -> dict:
          """转换为字典"""
          pass
  ```

- [ ] **子任务 4.2**: 实现SessionEntity和TaskEntity
  - SessionEntity: 会话实体
  - TaskEntity: 任务实体
  - ConfigurationEntity: 配置实体

- [ ] **子任务 4.3**: 实现业务模型验证
  - 数据有效性验证
  - 业务规则检查
  - 数据转换和格式化

## 📂 实现文件清单

### 新建文件
- [ ] `src/data/database_manager.py` - 数据库管理器
- [ ] `src/data/repositories.py` - Repository实现
- [ ] `src/data/entities.py` - 数据实体类
- [ ] `src/data/database_service.py` - 数据库服务
- [ ] `src/data/migrations.py` - 数据库迁移管理
- [ ] `src/data/__init__.py` - 数据模块初始化

### 修改文件
- [ ] `src/config/config.json` - 添加数据库配置
- [ ] `requirements.txt` - 添加aiosqlite依赖

### 配置文件
- [ ] `database_config.json` - 数据库连接配置
- [ ] `schema_migrations/` - 数据库迁移脚本目录

### 数据文件
- [ ] `data/wq.db` - SQLite数据库文件
- [ ] `data/backup/` - 数据备份目录

## 🔗 依赖和集成

### 上游依赖
- **无直接依赖**: 作为基础设施模块，被其他模块依赖

### 下游影响
- **所有核心模块**: SessionManager, FactorEngine, FactorValidator, SubmitManager
- **监控系统**: 需要数据库统计和性能数据
- **CLI系统**: 需要数据查询和管理接口

### 服务提供
```python
# DataManager作为中心服务提供者
class DataManager:
    def __init__(self, db_path: str = "data/wq.db"):
        self.db_service = DatabaseService(db_path)
        self.factor_repo = FactorRepository(self.db_service)
        self.session_repo = SessionRepository(self.db_service)
        self.task_repo = TaskRepository(self.db_service)
        
    async def initialize(self) -> bool:
        """初始化数据管理器"""
        pass
        
    async def get_factor_repository(self) -> FactorRepository:
        """获取因子数据仓库"""
        return self.factor_repo
```

## 📊 性能要求

### 数据库性能
- **查询响应时间**: 简单查询≤100ms，复杂查询≤1s
- **并发连接**: 支持≥10个并发数据库连接
- **事务处理**: 事务提交时间≤200ms
- **数据库大小**: 支持≥10万条因子记录

### 存储要求
- **磁盘占用**: 数据库文件≤1GB (10万因子)
- **写入性能**: ≥1000条记录/秒
- **查询性能**: ≥5000条记录/秒
- **备份效率**: 全量备份≤30秒

## 🧪 测试验证

### 功能测试 (使用真实数据)
- [ ] **初始化测试**: 验证数据库自动创建和表结构
- [ ] **CRUD测试**: 验证基本的增删改查操作
- [ ] **事务测试**: 验证事务的原子性和一致性
- [ ] **查询测试**: 验证复杂查询和索引性能

### 性能测试
- [ ] **批量操作**: 1000条记录的批量插入和更新
- [ ] **并发测试**: 10个并发连接的稳定性
- [ ] **大数据量**: 10万条记录的查询性能
- [ ] **长期运行**: 24小时连续运行的稳定性

### 可靠性测试
- [ ] **异常恢复**: 数据库文件损坏后的恢复
- [ ] **磁盘满**: 磁盘空间不足时的处理
- [ ] **并发冲突**: 并发写入的冲突处理
- [ ] **数据完整性**: 约束违反和数据验证

### 集成测试
- [ ] **模块集成**: 与其他核心模块的集成测试
- [ ] **API集成**: Repository接口的完整性测试
- [ ] **配置测试**: 不同配置下的数据库行为
- [ ] **迁移测试**: Schema版本升级的兼容性

## 📋 Definition of Done

### 代码完成标准
- [ ] 所有验收条件实现并通过测试
- [ ] 严格按照 `architecture/03-数据架构设计.md` 实现
- [ ] 提供完整的Repository模式抽象
- [ ] 异步操作和事务管理完善

### 集成标准
- [ ] 提供清晰的数据访问API
- [ ] 支持其他模块的数据存储需求
- [ ] 错误处理和异常管理完善
- [ ] 性能监控和日志记录完整

### 业务标准
- [ ] 数据完整性和一致性保证
- [ ] 支持多维度数据查询需求
- [ ] 自动备份和恢复机制
- [ ] 高性能和高并发支持

### 文档标准
- [ ] 完整的API文档和使用示例
- [ ] 数据库Schema设计文档
- [ ] 性能调优指南
- [ ] 故障排除和维护手册

---

## 🎯 开发指导

### 关键实现要点
1. **严格按照架构设计**: 实现必须遵循 `architecture/03-数据架构设计.md` 的完整设计
2. **异步优先**: 所有数据库操作必须是异步的，避免阻塞
3. **Repository模式**: 提供清晰的数据访问层抽象
4. **性能优化**: 关注索引设计和查询优化

### 开发阶段建议
**阶段1** (2-3天): 数据库架构和Schema设计  
**阶段2** (2-3天): Repository模式和数据访问层  
**阶段3** (2-3天): DatabaseService和事务管理  
**阶段4** (1-2天): 数据实体和业务模型  

### 避免的陷阱
- 不要过度设计数据模型 (MVP原则，满足当前需求即可)
- 不要忽略索引设计 (关键查询字段必须有索引)
- 不要忽略事务管理 (数据一致性的关键)
- 不要忽略异常处理 (数据库操作的健壮性)

### 成功标准
- **10万条记录**: 查询性能≤1秒
- **并发连接**: 10个连接稳定运行
- **事务处理**: 100%ACID保证
- **API完整性**: 支持所有模块的数据需求

**Next Story建议**: 完成此Story后，建议下一个是"CLI界面系统"，为用户提供友好的命令行交互界面。