# Story AV-003: Alpha检验系统

## 📋 Story 信息

**作为** 量化研究员  
**我希望** 系统能自动检验Alpha质量，包括自相关性和生产相关性  
**以便** 快速识别出可提交的优质Alpha，避免手动逐个检查的低效率

**Epic**: 核心业务功能  
**优先级**: P0 (必须有)  
**估算**: 8 Story Points  
**Status**: 🚧 Ready for Development  
**依赖**: Story AE-002 (Alpha挖掘引擎核心框架)

## ✅ 验收条件

### 核心功能验收
- [ ] **自相关性检验**: 自动检查Alpha自相关性，默认阈值0.7，可配置
- [ ] **生产相关性检验**: 检查与现有生产Alpha的相关性，默认阈值0.7，可配置
- [ ] **综合质量评估**: 通过检验的Alpha标记为GREEN，失败标记为RED
- [ ] **批量并发检验**: 支持大批量Alpha的并发检验处理
- [ ] **模式支持**: 支持USER和CONSULTANT两种检验模式

### 技术验收
- [ ] **异步并发**: 基于ThreadPoolExecutor的并发检验机制
- [ ] **进度监控**: 实时显示批量检验进度和统计信息
- [ ] **错误处理**: 单个Alpha检验失败不影响整体流程
- [ ] **结果持久化**: 检验结果保存到文件，支持断点续传
- [ ] **性能优化**: 避免重复检验已处理的Alpha

### 业务验收
- [ ] **阈值可配置**: 支持通过配置文件调整相关性阈值
- [ ] **检验统计**: 提供检验通过率、失败原因分析等统计
- [ ] **可提交列表**: 自动生成可提交Alpha的CSV文件
- [ ] **质量分级**: 根据检验结果对Alpha进行质量分级（A/B/C/D）

## 🔧 技术任务

### 任务 1: 实现AlphaValidator核心类

**基于**: `tutorial_v2_bmad/architecture/02-核心模块设计.md` 第192-253行

- [ ] **子任务 1.1**: 创建AlphaValidator基础架构
  ```python
  class AlphaValidator:
      def __init__(self, session_manager: SessionManager, config: ValidatorConfig)
      async def validate_alpha(self, alpha: Alpha) -> ValidationResult
      async def batch_validate(self, alphas: List[Alpha]) -> List[ValidationResult]
      async def check_self_correlation(self, alpha_id: str, threshold: float = 0.7) -> TestResult
      async def check_prod_correlation(self, alpha_id: str, threshold: float = 0.7) -> TestResult
  ```

- [ ] **子任务 1.2**: 实现检验配置管理
  - ValidatorConfig类：检验阈值、模式设置
  - 支持USER和CONSULTANT两种模式
  - 支持自相关性和生产相关性阈值配置
  - 支持并发检验数量配置

- [ ] **子任务 1.3**: 实现检验结果模型
  - ValidationResult类：检验结果数据结构
  - TestResult类：单项测试结果
  - QualityGrade类：质量分级枚举

### 任务 2: 实现相关性检验算法

**基于**: `tutorial_v2_bmad/src_xin` 的成熟检验逻辑

- [ ] **子任务 2.1**: 实现自相关性检验
  ```python
  async def check_self_correlation(self, alpha_id: str, threshold: float = 0.7):
      """
      检查Alpha的自相关性
      threshold: 相关性阈值，默认0.7
      返回: TestResult包含pass/fail状态和相关性值
      """
  ```

- [ ] **子任务 2.2**: 实现生产相关性检验
  ```python
  async def check_prod_correlation(self, alpha_id: str, threshold: float = 0.7):
      """
      检查与生产Alpha的相关性
      需要CONSULTANT权限访问生产Alpha数据
      返回: TestResult包含最高相关性和相关Alpha列表
      """
  ```

- [ ] **子任务 2.3**: 实现综合质量评估
  - 基于WQ平台返回的检验结果
  - 结合自相关性和生产相关性结果
  - 生成GREEN/RED质量标记
  - 计算整体质量分数

### 任务 3: 实现并发检验系统

**基于**: SessionManager和异步处理

- [ ] **子任务 3.1**: 实现并发检验调度器
  - 基于ThreadPoolExecutor的并发控制
  - 支持可配置的并发检验数量（默认3-5个）
  - 基于SessionManager的认证集成
  - 检验任务队列和工作池管理

- [ ] **子任务 3.2**: 实现检验结果管理
  - 异步结果收集器
  - 检验失败重试机制
  - 结果数据格式化和存储
  - 实时进度监控和统计

- [ ] **子任务 3.3**: 实现错误处理机制
  - 网络超时和连接异常处理
  - 权限不足和认证错误处理
  - 单个Alpha检验失败的隔离
  - 错误日志记录和分析

### 任务 4: 实现结果持久化和恢复

**基于**: 断点续传和数据管理需求

- [ ] **子任务 4.1**: 实现检验结果存储
  ```python
  def save_validation_results(self, results: List[ValidationResult], filepath: str):
      """保存检验结果到文件，支持增量追加"""
      
  def load_validation_results(self, filepath: str) -> List[ValidationResult]:
      """从文件加载已有检验结果"""
  ```

- [ ] **子任务 4.2**: 实现可提交Alpha列表生成
  - 自动筛选通过检验的Alpha
  - 生成CSV格式的可提交列表
  - 包含Alpha表达式、质量分数、检验状态
  - 支持按质量分数排序

- [ ] **子任务 4.3**: 实现断点续传机制
  - 检测已检验的Alpha，避免重复处理
  - 支持从中断点继续批量检验
  - 进度状态持久化和恢复
  - 完整性验证和状态同步

## 📂 实现文件清单

### 新建文件
- [ ] `src/core/alpha_validator.py` - AlphaValidator核心类
- [ ] `src/core/validation_models.py` - 检验结果数据模型
- [ ] `src/core/correlation_checker.py` - 相关性检验算法
- [ ] `src/core/validation_scheduler.py` - 并发检验调度器

### 修改文件
- [ ] `src/config/config.json` - 添加Alpha检验配置
- [ ] `src/core/__init__.py` - 添加Alpha检验模块导出
- [ ] `requirements.txt` - 确认并发处理依赖

### 配置文件
- [ ] `validation_config.json` - Alpha检验配置
- [ ] `correlation_thresholds.json` - 相关性阈值配置

## 🔗 依赖和集成

### 上游依赖
- **AlphaEngine**: 来自 Story AE-002，提供待检验的Alpha
- **SessionManager**: 来自 Story SM-001，用于WQ平台认证
- **WQ平台API**: `/alpha/{alpha_id}/test` 检验接口

### 下游影响
- **Alpha提交系统**: 需要检验通过的Alpha列表
- **监控系统**: 需要检验统计和性能数据
- **数据管理系统**: 需要检验结果持久化

### 数据流集成
```
AlphaEngine ──► AlphaValidator ──► SubmitManager ──► Monitor
     │              │                    │              │
     │              ▼                    ▼              ▼
  生成Alpha列表   检验结果统计       可提交列表      检验报告
```

## 📊 性能要求

### 检验能力
- **单Alpha检验**: ≤30秒完成单个Alpha检验
- **批量检验**: 100个Alpha≤2小时完成
- **并发数**: 支持3-5个并发检验任务
- **失败率**: 检验失败率≤10%（排除网络异常）

### 准确性要求
- **自相关性检验**: 100%准确率（基于WQ平台结果）
- **生产相关性检验**: 100%准确率（需要CONSULTANT权限）
- **质量分级**: 与手动检验一致性≥95%
- **阈值控制**: 支持0.1-0.9范围的阈值配置

## 🧪 测试验证

### 功能测试 (使用真实数据)
- [ ] **单Alpha测试**: 使用已知Alpha测试检验流程
- [ ] **批量测试**: 50个Alpha的批量检验验证
- [ ] **阈值测试**: 验证不同阈值设置的检验结果
- [ ] **模式测试**: 验证USER和CONSULTANT模式差异

### 并发和性能测试
- [ ] **并发检验**: 5个并发任务稳定性测试
- [ ] **大批量测试**: 500个Alpha的批量检验
- [ ] **长时间运行**: 4小时连续检验稳定性
- [ ] **内存管理**: 大批量检验的内存使用

### 权限和异常测试
- [ ] **权限测试**: 验证CONSULTANT权限的生产相关性检验
- [ ] **网络异常**: 网络中断后的自动重试
- [ ] **部分失败**: 部分Alpha检验失败时的处理
- [ ] **数据完整性**: 断点续传后的数据一致性

### 业务逻辑测试
- [ ] **相关性计算**: 验证相关性检验算法准确性
- [ ] **质量分级**: 验证质量分级的业务合理性
- [ ] **配置驱动**: 不同配置下的检验行为
- [ ] **结果统计**: 检验统计和报告准确性

## 📋 Definition of Done

### 代码完成标准
- [ ] 所有验收条件实现并通过真实数据测试
- [ ] 严格按照 `architecture/02-核心模块设计.md` 实现
- [ ] 基于 `src_xin` 的成熟检验算法
- [ ] 与AlphaEngine和SessionManager的集成无缝对接

### 集成标准
- [ ] 提供完整的异步API接口
- [ ] 错误处理和异常管理完善
- [ ] 日志记录详细且可追踪
- [ ] 性能指标满足PRD要求

### 业务标准
- [ ] 检验结果与WQ平台结果一致
- [ ] 支持PRD定义的所有检验模式和配置
- [ ] 断点续传机制稳定可靠
- [ ] 可提交Alpha列表准确可信

### 文档标准
- [ ] 完整的API文档和使用示例
- [ ] 配置参数说明和检验阈值指南
- [ ] 故障排除指南和性能优化建议

---

## 🎯 开发指导

### 关键实现要点
1. **严格按照架构**: 实现必须遵循 `architecture/02-核心模块设计.md` 的检验流程设计
2. **复用成熟算法**: 直接使用 `src_xin` 中验证过的相关性检验逻辑
3. **异步优先**: 所有检验操作必须是异步的，避免阻塞
4. **真实验证**: 使用真实WQ平台进行检验，确保结果准确性

### 开发阶段建议
**阶段1** (2-3天): 核心AlphaValidator类和基础数据模型  
**阶段2** (3-4天): 相关性检验算法和API集成  
**阶段3** (2-3天): 并发检验系统和结果管理  
**阶段4** (1-2天): 持久化和断点续传机制  

### 避免的陷阱
- 不要重新实现已验证的相关性算法 (直接使用旧代码)
- 不要过度设计检验流程 (MVP原则)
- 不要添加单元测试 (PRD要求使用真实数据验证)
- 不要忽略权限管理 (CONSULTANT模式的生产相关性检验)

### 成功标准
- **100个Alpha检验**: 2小时内完成
- **检验准确性**: 与WQ平台结果100%一致
- **断点续传**: 30秒内恢复检验流程
- **业务价值**: 显著提高Alpha质量筛选效率

**Next Story建议**: 完成此Story后，下一个应该是"Alpha提交系统"，因为它直接使用Alpha检验的输出进行批量提交。