# Story AS-004: Alpha提交系统

## 📋 Story 信息

**作为** 量化研究员  
**我希望** 系统能自动提交通过检验的优质Alpha到WQ平台竞赛  
**以便** 参与量化竞赛获得收益，无需手动逐个提交Alpha

**Epic**: 核心业务功能  
**优先级**: P0 (必须有)  
**估算**: 5 Story Points  
**Status**: 🚧 Ready for Development  
**依赖**: Story AV-003 (Alpha检验系统)

## ✅ 验收条件

### 核心功能验收
- [ ] **单个提交**: 支持单个Alpha的可靠提交，包含完整的错误处理
- [ ] **批量提交**: 支持从CSV文件批量读取并提交多个Alpha
- [ ] **智能重试**: 提交失败时自动重试，最多5次尝试
- [ ] **状态跟踪**: 完整记录每个Alpha的提交状态和历史
- [ ] **断点续传**: 支持中断后从未完成的Alpha开始继续提交

### 技术验收
- [ ] **双阶段提交**: POST发起提交 + GET轮询等待，符合WQ平台协议
- [ ] **超时控制**: 支持30分钟超时机制，避免无限等待
- [ ] **会话管理**: 基于SessionManager的认证和频率控制
- [ ] **异步处理**: 支持异步提交，提升并发性能
- [ ] **错误分类**: 区分永久失败(403)和临时失败，采用不同策略

### 业务验收
- [ ] **智能排序**: 按自相关性升序排序，提高提交成功率
- [ ] **成功率统计**: 提供详细的提交成功率和失败原因分析
- [ ] **CSV管理**: 自动维护提交列表，删除已成功提交的Alpha
- [ ] **进度显示**: 实时显示批量提交进度和剩余时间

## 🔧 技术任务

### 任务 1: 实现SubmitManager核心类

**基于**: `tutorial_v2_bmad/architecture/02-核心模块设计.md` 第287-316行

- [ ] **子任务 1.1**: 创建SubmitManager基础架构
  ```python
  class SubmitManager:
      def __init__(self, session_manager: SessionManager, config: SubmitConfig)
      async def submit_alpha(self, alpha: Alpha, session: Session) -> SubmitResult
      async def batch_submit(self, alphas: List[Alpha]) -> List[SubmitResult]
      async def retry_failed_submissions(self, failed_submissions: List[SubmitResult]) -> List[SubmitResult]
      async def get_submission_statistics(self, date_range: DateRange) -> SubmissionStats
  ```

- [ ] **子任务 1.2**: 实现提交配置管理
  - SubmitConfig类：提交超时、重试次数、并发数配置
  - 支持单个和批量提交模式设置
  - 支持提交超时和重试策略配置
  - 支持CSV文件路径和格式配置

- [ ] **子任务 1.3**: 实现提交结果模型
  - SubmitResult类：提交结果数据结构
  - SubmissionStats类：提交统计数据
  - SubmitStatus枚举：提交状态管理

### 任务 2: 实现双阶段提交算法

**基于**: `tutorial_v2_bmad/src_xin` 的成熟提交逻辑

- [ ] **子任务 2.1**: 实现POST提交发起
  ```python
  async def submit_single_alpha(self, session, alpha_id):
      """
      POST /alphas/{alpha_id}/submit
      发起Alpha提交，获取提交ID
      处理403权限错误和其他异常
      """
  ```

- [ ] **子任务 2.2**: 实现GET状态轮询
  ```python
  async def wait_for_submission_completion(self, session, submission_id, timeout=1800):
      """
      GET轮询提交状态直到完成或超时
      处理PENDING, COMPLETED, FAILED状态
      支持30分钟超时控制
      """
  ```

- [ ] **子任务 2.3**: 实现智能重试机制
  - 区分永久失败(403)和临时失败
  - 支持指数退避重试策略
  - 最多5次重试限制
  - 重试间隔和策略配置

### 任务 3: 实现批量提交系统

**基于**: CSV文件管理和SessionManager集成

- [ ] **子任务 3.1**: 实现CSV文件管理
  ```python
  def read_submit_list(self, csv_file: str) -> List[AlphaSubmitInfo]:
      """从CSV读取待提交Alpha列表"""
      
  def update_submit_list(self, csv_file: str, completed_alphas: List[str]):
      """更新CSV文件，删除已处理的Alpha"""
  ```

- [ ] **子任务 3.2**: 实现批量提交调度
  - 基于ThreadPoolExecutor的并发提交
  - 支持可配置的并发提交数量（默认1-2个）
  - 实时进度监控和统计更新
  - 基于SessionManager的认证集成

- [ ] **子任务 3.3**: 实现断点续传机制
  - 检测已提交的Alpha，避免重复提交
  - 支持从中断点继续批量提交
  - 提交状态持久化和恢复
  - 完整性验证和状态同步

### 任务 4: 实现提交统计和监控

**基于**: 实时监控和报告需求

- [ ] **子任务 4.1**: 实现提交统计收集
  ```python
  def collect_submission_stats(self, results: List[SubmitResult]) -> SubmissionStats:
      """收集提交统计信息"""
      # 成功率、失败率、平均耗时
      # 失败原因分析和分类
      # 性能指标和趋势分析
  ```

- [ ] **子任务 4.2**: 实现实时进度监控
  - Rich库的进度条显示
  - 实时更新提交进度和剩余时间
  - 成功/失败计数和比率显示
  - 错误摘要和重试状态

- [ ] **子任务 4.3**: 实现提交报告生成
  - 详细的提交日志记录
  - 成功和失败Alpha的分类统计
  - 提交性能分析和优化建议
  - 可视化提交结果报告

## 📂 实现文件清单

### 新建文件
- [ ] `src/core/submit_manager.py` - SubmitManager核心类
- [ ] `src/core/submission_models.py` - 提交结果数据模型
- [ ] `src/core/submit_scheduler.py` - 批量提交调度器
- [ ] `src/core/csv_manager.py` - CSV文件管理器

### 修改文件
- [ ] `src/config/config.json` - 添加Alpha提交配置
- [ ] `src/core/__init__.py` - 添加Alpha提交模块导出
- [ ] `requirements.txt` - 确认异步处理依赖

### 配置文件
- [ ] `submit_config.json` - Alpha提交配置
- [ ] `submit_list.csv` - 待提交Alpha列表模板

## 🔗 依赖和集成

### 上游依赖
- **AlphaValidator**: 来自 Story AV-003，提供可提交的Alpha列表
- **SessionManager**: 来自 Story SM-001，用于WQ平台认证
- **WQ平台API**: `/alphas/{alpha_id}/submit` 提交接口

### 下游影响
- **监控系统**: 需要提交统计和性能数据
- **数据管理系统**: 需要提交记录持久化
- **报告系统**: 需要提交结果分析

### 数据流集成
```
AlphaValidator ──► SubmitManager ──► BatchSubmitter ──► SubmissionMonitor
     │                   │                │                     │
     │                   ▼                ▼                     ▼
可提交Alpha列表    SessionManager      提交队列            统计报告
```

## 📊 性能要求

### 提交能力
- **单Alpha提交**: ≤30分钟完成（包含30分钟超时）
- **批量提交**: 100个Alpha≤3天完成（假设70%成功率）
- **并发数**: 支持1-2个并发提交任务（避免频率限制）
- **重试效率**: 失败重试≤5分钟间隔

### 成功率要求
- **成功率**: 基于Alpha质量的正常成功率≥60%
- **重试成功率**: 临时失败重试成功率≥80%
- **超时率**: 超时提交比例≤5%
- **错误处理**: 100%错误正确分类和处理

## 🧪 测试验证

### 功能测试 (使用真实数据)
- [ ] **单Alpha测试**: 使用已检验Alpha测试单个提交流程
- [ ] **批量测试**: 10个Alpha的小批量提交验证
- [ ] **超时测试**: 验证30分钟超时机制
- [ ] **重试测试**: 验证智能重试逻辑

### 并发和性能测试
- [ ] **并发提交**: 2个并发任务稳定性测试
- [ ] **长时间运行**: 24小时连续提交稳定性
- [ ] **大批量测试**: 500个Alpha的批量提交
- [ ] **资源管理**: 长时间提交的内存和连接管理

### 异常和恢复测试
- [ ] **网络异常**: 网络中断后的自动重试
- [ ] **权限测试**: 403权限错误的正确处理
- [ ] **中断恢复**: 批量提交中断后的断点续传
- [ ] **数据完整性**: 恢复后的提交状态一致性

### 业务逻辑测试
- [ ] **CSV管理**: 验证CSV文件的正确更新
- [ ] **状态跟踪**: 验证提交状态的准确记录
- [ ] **统计分析**: 验证提交统计的准确性
- [ ] **错误分类**: 验证错误原因的正确分类

## 📋 Definition of Done

### 代码完成标准
- [ ] 所有验收条件实现并通过真实数据测试
- [ ] 严格按照 `architecture/02-核心模块设计.md` 实现
- [ ] 基于 `src_xin` 的成熟提交算法
- [ ] 与AlphaValidator和SessionManager的无缝集成

### 集成标准
- [ ] 提供完整的异步API接口
- [ ] 错误处理和异常管理完善
- [ ] 日志记录详细且可追踪
- [ ] 性能指标满足PRD要求

### 业务标准
- [ ] 提交流程与WQ平台协议完全兼容
- [ ] 支持PRD定义的所有提交模式和配置
- [ ] 断点续传机制稳定可靠
- [ ] 提交成功率符合预期（基于Alpha质量）

### 文档标准
- [ ] 完整的API文档和使用示例
- [ ] 配置参数说明和提交策略指南
- [ ] 故障排除指南和性能优化建议

---

## 🎯 开发指导

### 关键实现要点
1. **严格按照架构**: 实现必须遵循 `architecture/02-核心模块设计.md` 的提交流程设计
2. **复用成熟算法**: 直接使用 `src_xin` 中验证过的双阶段提交逻辑
3. **异步优先**: 所有提交操作必须是异步的，避免阻塞
4. **真实验证**: 使用真实WQ平台进行提交，确保流程可靠性

### 开发阶段建议
**阶段1** (1-2天): 核心SubmitManager类和基础数据模型  
**阶段2** (2-3天): 双阶段提交算法和API集成  
**阶段3** (1-2天): 批量提交系统和CSV管理  
**阶段4** (1天): 统计监控和断点续传机制  

### 避免的陷阱
- 不要重新实现已验证的提交算法 (直接使用旧代码)
- 不要过度设计提交流程 (MVP原则)
- 不要添加单元测试 (PRD要求使用真实数据验证)
- 不要忽略频率控制 (避免WQ平台限制)

### 成功标准
- **单Alpha提交**: 30分钟内完成或正确超时
- **批量提交**: 稳定的断点续传和状态管理
- **成功率**: 基于Alpha质量达到合理成功率
- **用户体验**: 清晰的进度显示和错误提示

**Next Story建议**: 完成此Story后，下一个应该是"数据管理系统"，因为它需要持久化所有Alpha的生命周期数据。