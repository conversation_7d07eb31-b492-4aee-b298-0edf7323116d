body {
    font-family: Arial, sans-serif;
    margin: 0;
    padding: 0;
    background-color: #f5f5f5;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

h1, h2, h3 {
    color: #2c3e50;
}

.task-controls, .task-list, .task-logs {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 15px;
}

label {
    display: inline-block;
    width: 150px;
    font-weight: bold;
}

input, select {
    padding: 8px;
    width: 250px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

button {
    background-color: #3498db;
    color: white;
    border: none;
    padding: 10px 15px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
}

button:hover {
    background-color: #2980b9;
}

.task-item {
    border: 1px solid #ddd;
    padding: 15px;
    margin-bottom: 10px;
    border-radius: 4px;
}

.status {
    padding: 3px 8px;
    border-radius: 4px;
    font-size: 14px;
}

.status.pending {
    background-color: #f39c12;
    color: white;
}

.status.running {
    background-color: #3498db;
    color: white;
}

.status.completed {
    background-color: #2ecc71;
    color: white;
}

.status.failed {
    background-color: #e74c3c;
    color: white;
}

#task-log {
    background-color: #1e1e1e;
    color: #d4d4d4;
    padding: 15px;
    border-radius: 4px;
    max-height: 500px;
    overflow-y: auto;
    font-family: monospace;
    white-space: pre-wrap;
}

/* 登录页面特定样式 */
.login-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 30px;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

.login-container h1 {
    text-align: center;
    margin-bottom: 25px;
    color: #333;
}

.login-container .form-group {
    margin-bottom: 20px;
}

.login-container label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.login-container input[type="text"],
.login-container input[type="password"] {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    box-sizing: border-box;
}

.login-container input[type="text"]:focus,
.login-container input[type="password"]:focus {
    border-color: #4CAF50;
    outline: none;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.2);
}

.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.remember-me input[type="checkbox"] {
    margin: 0 10px 0 0;
    width: auto;
    transform: scale(1.2);
}

.remember-me label {
    margin: 0;
    font-weight: normal;
    color: #666;
    cursor: pointer;
}

.login-btn {
    width: 100%;
    padding: 12px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.login-btn:hover {
    background-color: #45a049;
}

.error {
    color: #f44336;
    margin-bottom: 20px;
    padding: 10px;
    background-color: #ffebee;
    border-radius: 4px;
    text-align: center;
}

