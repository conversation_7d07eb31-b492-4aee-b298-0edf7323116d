#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Session管理器
提供全局的session管理功能，包括单例模式、自动登录、超时检查和错误重试
"""

import time
import threading
import requests
from typing import Optional, Dict, Any
from .logger import get_logger
from .config import get_account_config, get_api_config, validate_config

# 获取当前模块的日志器
logger = get_logger(__name__)


class SessionManager:
    """
    全局Session管理器
    实现单例模式，管理用户会话的生命周期
    """
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super(SessionManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化Session管理器"""
        if not hasattr(self, '_initialized'):
            self._session: Optional[requests.Session] = None
            self._login_time: Optional[float] = None
            self._session_data: Optional[Dict[str, Any]] = None
            
            # 从配置文件加载账户信息
            account_config = get_account_config()
            api_config = get_api_config()
            
            self._alias = account_config.get('alias', '')
            self._username = account_config.get('username', '')
            self._password = account_config.get('password', '')
            self._login_url = api_config.get('login', 'https://api.worldquantbrain.com/authentication')
            
            self._max_retries = 9999999
            self._retry_delay = 5  # 秒
            self._session_lock = threading.Lock()
            self._initialized = True
            
            # 验证配置
            if not validate_config():
                logger.error("配置文件验证失败，SessionManager可能无法正常工作")
            
            logger.info(f"[green]SessionManager 初始化完成，用户: {self._alias}[/green]")
            logger.info(f"登录API地址: {self._login_url}")
    
    def _perform_login(self) -> tuple[requests.Session, Dict[str, Any]]:
        """
        执行登录操作
        
        Returns:
            tuple: (session对象, 登录响应数据)
            
        Raises:
            Exception: 登录失败时抛出异常
        """
        logger.info("开始执行登录操作")
        
        # 创建新的session
        session = requests.Session()
        session.auth = (self._username, self._password)
        
        # 发送登录请求
        response = session.post(self._login_url)
        
        # 检查响应
        response_text = response.content.decode('utf-8')
        logger.info(f"登录响应: {response_text}")
        
        if "INVALID_CREDENTIALS" in response_text:
            raise Exception("账号密码有误，请检查凭据！")
        
        # 解析响应数据（假设返回JSON格式）
        try:
            # 尝试解析JSON响应
            session_data = response.json()
            logger.info(f"登录成功，获取到session数据: {session_data}")
        except Exception as e:
            # 如果不是JSON格式，创建默认的session数据
            logger.warning(f"无法解析登录响应为JSON: {e}，使用默认session数据")
            session_data = {
                "user": {"id": "unknown"},
                "token": {"expiry": 14400.0},  # 默认4小时过期
                "permissions": ["TUTORIAL", "WORKDAY"]
            }
        
        return session, session_data
    
    def _is_session_expired(self) -> bool:
        """
        检查session是否已过期
        
        Returns:
            bool: True表示已过期，False表示未过期
        """
        if self._login_time is None or self._session_data is None:
            return True
        
        # 获取过期时间（秒）
        expiry_seconds = self._session_data.get('token', {}).get('expiry', 14400.0)
        
        # 计算是否过期（提前5分钟刷新）
        elapsed_time = time.time() - self._login_time
        buffer_time = 300  # 5分钟缓冲
        
        is_expired = elapsed_time >= (expiry_seconds - buffer_time)
        
        if is_expired:
            logger.info(f"Session已过期，已使用时间: {elapsed_time:.2f}秒，过期时间: {expiry_seconds}秒")
        
        return is_expired
    
    def _login_with_retry(self) -> tuple[requests.Session, Dict[str, Any]]:
        """
        带重试机制的登录
        
        Returns:
            tuple: (session对象, 登录响应数据)
            
        Raises:
            Exception: 重试次数用尽后仍然失败时抛出异常
        """
        last_exception = None
        
        for attempt in range(self._max_retries):
            try:
                logger.info(f"登录尝试 {attempt + 1}/{self._max_retries}")
                return self._perform_login()
                
            except Exception as e:
                last_exception = e
                logger.warning(f"登录尝试 {attempt + 1} 失败: {str(e)}")
                
                if attempt < self._max_retries - 1:
                    logger.info(f"等待 {self._retry_delay} 秒后重试...")
                    time.sleep(self._retry_delay)
                else:
                    logger.error(f"所有登录尝试均失败，放弃重试")
        
        # 如果所有重试都失败，抛出最后一个异常
        raise last_exception
    
    def get_session(self) -> requests.Session:
        """
        获取有效的session对象
        如果session不存在或已过期，会自动登录获取新的session
        
        Returns:
            requests.Session: 有效的session对象
            
        Raises:
            Exception: 登录失败时抛出异常
        """
        with self._session_lock:
            # 检查是否需要登录或重新登录
            if self._session is None or self._is_session_expired():
                logger.info("需要获取新的session")
                
                try:
                    # 执行登录
                    self._session, self._session_data = self._login_with_retry()
                    self._login_time = time.time()
                    
                    logger.info("Session获取成功")
                    
                except Exception as e:
                    logger.error(f"获取session失败: {str(e)}")
                    raise
            
            return self._session
    
    def get_session_data(self) -> Optional[Dict[str, Any]]:
        """
        获取session相关数据
        
        Returns:
            Dict[str, Any]: session数据，如果未登录则返回None
        """
        # 确保有有效的session
        self.get_session()
        return self._session_data
    
    def invalidate_session(self):
        """
        手动使session失效，下次获取时会重新登录
        """
        with self._session_lock:
            logger.info("手动使session失效")
            self._session = None
            self._login_time = None
            self._session_data = None
    
    def is_logged_in(self) -> bool:
        """
        检查是否已登录且session有效
        
        Returns:
            bool: True表示已登录且有效，False表示未登录或已过期
        """
        return (self._session is not None and 
                not self._is_session_expired())
    
    def get_user_info(self) -> Optional[Dict[str, Any]]:
        """
        获取用户信息
        
        Returns:
            Dict[str, Any]: 用户信息，如果未登录则返回None
        """
        session_data = self.get_session_data()
        return session_data.get('user') if session_data else None
    
    def get_permissions(self) -> list:
        """
        获取用户权限列表
        
        Returns:
            list: 权限列表，如果未登录则返回空列表
        """
        session_data = self.get_session_data()
        return session_data.get('permissions', []) if session_data else []


# 全局session管理器实例
session_manager = SessionManager()


def get_session() -> requests.Session:
    """
    获取全局session对象的便捷函数
    
    Returns:
        requests.Session: 有效的session对象
    """
    return session_manager.get_session()


def get_session_data() -> Optional[Dict[str, Any]]:
    """
    获取session数据的便捷函数
    
    Returns:
        Dict[str, Any]: session数据
    """
    return session_manager.get_session_data()


def invalidate_session():
    """
    使session失效的便捷函数
    """
    session_manager.invalidate_session()