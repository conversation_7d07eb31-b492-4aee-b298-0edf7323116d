#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器
负责读取和管理应用程序配置
"""

import json
import os
from pathlib import Path
from typing import Dict, Any, Optional
from .logger import get_logger

# 获取当前模块的日志器
logger = get_logger(__name__)


class ConfigManager:
    """
    配置管理器
    负责加载和管理应用程序配置
    """
    
    _instance = None
    _config_data: Optional[Dict[str, Any]] = None
    
    def __new__(cls):
        """单例模式实现"""
        if cls._instance is None:
            cls._instance = super(ConfigManager, cls).__new__(cls)
        return cls._instance
    
    def __init__(self):
        """初始化配置管理器"""
        if not hasattr(self, '_initialized'):
            self._config_file_path = None
            self._initialized = True
            logger.info("ConfigManager 初始化完成")
    
    def load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """
        加载配置文件
        
        Args:
            config_path (str, optional): 配置文件路径，如果不提供则使用默认路径
            
        Returns:
            Dict[str, Any]: 配置数据
            
        Raises:
            FileNotFoundError: 配置文件不存在
            json.JSONDecodeError: 配置文件格式错误
        """
        if config_path is None:
            # 默认配置文件路径：当前文件所在目录的上级目录下的config.json
            current_dir = Path(__file__).parent.parent
            config_path = current_dir / "config.json"
        
        config_path = Path(config_path)
        self._config_file_path = str(config_path)
        
        logger.info(f"正在加载配置文件: {config_path}")
        
        if not config_path.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config_data = json.load(f)
            
            logger.info("配置文件加载成功")
            logger.debug(f"配置内容: {self._config_data}")
            
            return self._config_data
            
        except json.JSONDecodeError as e:
            logger.error(f"配置文件格式错误: {e}")
            raise
        except Exception as e:
            logger.error(f"加载配置文件时发生错误: {e}")
            raise
    
    def get_config(self, key_path: str = None, default: Any = None) -> Any:
        """
        获取配置值
        
        Args:
            key_path (str, optional): 配置键路径，使用点号分隔，如 'account.username'
            default (Any, optional): 默认值
            
        Returns:
            Any: 配置值
        """
        if self._config_data is None:
            self.load_config()
        
        if key_path is None:
            return self._config_data
        
        # 支持点号分隔的键路径
        keys = key_path.split('.')
        value = self._config_data
        
        try:
            for key in keys:
                value = value[key]
            return value
        except (KeyError, TypeError):
            logger.warning(f"配置键 '{key_path}' 不存在，返回默认值: {default}")
            return default
    
    def get_account_config(self) -> Dict[str, str]:
        """
        获取账户配置
        
        Returns:
            Dict[str, str]: 包含用户名、密码和别名的字典
        """
        account_config = self.get_config('account', {})
        
        return {
            'username': account_config.get('username', ''),
            'password': account_config.get('password', ''),
            'alias': account_config.get('alias', '')
        }
    
    def get_api_config(self) -> Dict[str, str]:
        """
        获取API配置
        
        Returns:
            Dict[str, str]: 包含API端点的字典
        """
        api_config = self.get_config('api', {})
        
        return {
            'login': api_config.get('login', ''),
            'dataset': api_config.get('dataset', ''),
            'datafields': api_config.get('datafields', '')
        }
    
    def get_dataset_id(self) -> str:
        """
        获取数据集ID
        
        Returns:
            str: 数据集ID
        """
        return self.get_config('dataset_id', '')

    def get_region(self) -> str:
        """
        获取region

        Returns:
            str: region
        """
        return self.get_config('region', '')

    def reload_config(self) -> Dict[str, Any]:
        """
        重新加载配置文件
        
        Returns:
            Dict[str, Any]: 配置数据
        """
        logger.info("重新加载配置文件")
        return self.load_config(self._config_file_path)
    
    def validate_config(self) -> bool:
        """
        验证配置文件的完整性
        
        Returns:
            bool: True表示配置有效，False表示配置无效
        """
        try:
            if self._config_data is None:
                self.load_config()
            
            # 检查必需的配置项
            required_keys = [
                'account.username',
                'account.password',
                'api.login',
                'api.dataset',
                'api.datafields',
                'dataset_id'
            ]
            
            missing_keys = []
            for key in required_keys:
                value = self.get_config(key)
                if not value:
                    missing_keys.append(key)
            
            if missing_keys:
                logger.error(f"配置文件缺少必需的配置项: {missing_keys}")
                return False
            
            logger.info("配置文件验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置文件验证失败: {e}")
            return False


# 全局配置管理器实例
config_manager = ConfigManager()


def get_config(key_path: str = None, default: Any = None) -> Any:
    """
    获取配置值的便捷函数
    
    Args:
        key_path (str, optional): 配置键路径
        default (Any, optional): 默认值
        
    Returns:
        Any: 配置值
    """
    return config_manager.get_config(key_path, default)


def get_account_config() -> Dict[str, str]:
    """
    获取账户配置的便捷函数
    
    Returns:
        Dict[str, str]: 账户配置
    """
    return config_manager.get_account_config()


def get_api_config() -> Dict[str, str]:
    """
    获取API配置的便捷函数
    
    Returns:
        Dict[str, str]: API配置
    """
    return config_manager.get_api_config()


def get_dataset_id() -> str:
    """
    获取数据集ID的便捷函数
    
    Returns:
        str: 数据集ID
    """
    return config_manager.get_dataset_id()


def validate_config() -> bool:
    """
    验证配置的便捷函数
    
    Returns:
        bool: 配置是否有效
    """
    return config_manager.validate_config()