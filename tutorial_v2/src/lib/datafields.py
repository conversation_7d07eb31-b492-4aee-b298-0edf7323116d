
from .logger import get_logger
from .config import get_config, get_api_config
from .session import get_session
import pandas as pd

# 获取当前模块的日志器
logger = get_logger(__name__)

def get_data_fields( search: str = ''):
    dataset_id = get_config("dataset_id")
    region = get_config("region")
    universe = get_config("universe")
    delay = get_config("delay")
    instrument_type = get_config("instrument_type")

    url_template = get_url_template(dataset_id, region, universe, delay, instrument_type, search)

    data_fields = get_session().get(url_template.format(x=0))
    # print(f'data_fields is ${data_fields}')
    data_fields_json = data_fields.json()
    logger.info(f'[get_data_fields] data_fields_json is ${data_fields_json}')
    count = data_fields_json['count']
    logger.info(f'[get_data_fields] count is ${count}')

    # 分页循环，加载所有的 数据字段
    data_fields_list = []
    for x in range(0, count, 50):
        logger.info(f'[get_data_fields] x is ${x}')
        data_fields = get_session().get(url_template.format(x=x))
        data_fields_json = data_fields.json()
        data_fields_list.append(data_fields_json['results'])

    logger.info(f'[get_data_fields] data_fields_list length={len(data_fields_list)}, data_fields_list is ${data_fields_list}')

    # 变成二维表格
    data_fields_list_flat = [
        item for sublist in data_fields_list for item in sublist]

    data_fields_df = pd.DataFrame(data_fields_list_flat)
    return data_fields_df

def get_url_template(dataset_id: str, region: str = 'USA', universe: str = 'TOP3000', delay: int = 1,
                    instrument_type: str = 'EQUITY', search: str = ''):
    api_config = get_api_config()
    base_url = api_config.get('datafields', '')
    logger.info(f'[data_fields] base_url is ${base_url}')

    if len(search) == 0:
        url_template = f"{base_url}?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
    else:
        url_template = f"{base_url}?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
    logger.info(f'[data_fields] url_template is ${url_template}')
    return url_template
