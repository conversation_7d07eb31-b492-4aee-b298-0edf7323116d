
from .logger import get_logger
from .config import get_config, get_api_config

# 获取当前模块的日志器
logger = get_logger(__name__)

def get_data_fields(session, search: str = ''):
    dataset_id = get_config("dataset_id")
    region = get_api_config("region")
    universe = get_api_config("universe")
    delay = get_api_config("delay")
    instrument_type = get_api_config("instrument_type")

    url_template = get_url_template(dataset_id, region, universe, delay, instrument_type, search)
    return

def get_url_template(dataset_id: str, region: str = 'USA', universe: str = 'TOP3000', delay: int = 1,
                    instrument_type: str = 'EQUITY', search: str = ''):
    api_config = get_api_config()
    base_url = api_config.get('datafields', '')
    logger.info(f'[data_fields] base_url is ${base_url}')

    if len(search) == 0:
        url_template = f"{base_url}?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&dataset.id={dataset_id}&limit=50" + \
                       "&offset={x}"
    else:
        url_template = f"{base_url}?" + \
                       f"&instrumentType={instrument_type}" + \
                       f"&region={region}&delay={str(delay)}&universe={universe}&limit=50" + \
                       f"&search={search}" + \
                       "&offset={x}"
    logger.info(f'[data_fields] url_template is ${url_template}')
    return url_template
