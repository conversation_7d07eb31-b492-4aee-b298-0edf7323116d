#%% md
# jupyter lab

https://jupyter.org/try-jupyter/lab/
#%% md
# 获取session
#%%
import requests

# Create a session to persistently store the headers
session = requests.Session()

# Save credentials into session
session.auth = ('<EMAIL>', 'bfj!YGB5mfr!vmv0ycd')

# Send a POST request to the /authentication API
response = session.post('https://api.worldquantbrain.com/authentication')

info_ = response.content.decode('utf-8')
print(info_)

# print(session)


#%% md
# 指定数据集
#%%
import os
os.environ['WQ_AUTO_LOGIN'] = '0'

from machine_lib import get_datafields

from machine_lib import login  # 如果需要
s = login()  # 显式登录
df = get_datafields(s, dataset_id=dataset_id, region='USA', universe='TOP3000', delay=1)

df = get_datafields(session, dataset_id=dataset_id,
                        region='USA', universe='TOP3000', delay=1)

print(df)
#%% md

#%%
# 常量
vec_ops = ["vec_avg", "vec_sum", "vec_ir", "vec_max",
           "vec_count", "vec_skewness", "vec_stddev", "vec_choose"]

from machine_lib import process_datafields
# digging_1step.py  L84
pc_fields = process_datafields(
        df, "matrix") + process_datafields(df, "vector")
print(f'pc_fields is {pc_fields}')
#%%
def first_order_factory(fields, ops_set):
    # 用于存储所有生成的因子表达式
    alpha_set = []
    # 遍历每个输入的数据字段
    for field in fields:
        # 将原始字段本身也作为一个因子
        # reverse op does the work
        alpha_set.append(field)
        # 注释掉的部分表明曾考虑过添加字段的负值作为因子
        # alpha_set.append("-%s"%field)
        for op in ops_set:

            # A. 时间序列算子（特殊参数）
            # ts_comp_factory
            # 这些是复合时间序列算子，需要额外参数
            # 通过 ts_comp_factory 生成多个变体
            if op == "ts_percentage":

                # lpha_set += ts_comp_factory(op, field, "percentage", [0.2, 0.5, 0.8])
                alpha_set += ts_comp_factory(op, field, "percentage", [0.5])

            elif op == "ts_decay_exp_window":

                # alpha_set += ts_comp_factory(op, field, "factor", [0.2, 0.5, 0.8])
                alpha_set += ts_comp_factory(op, field, "factor", [0.5])

            elif op == "ts_moment":

                alpha_set += ts_comp_factory(op, field, "k", [2, 3, 4])

            elif op == "ts_entropy":

                # alpha_set += ts_comp_factory(op, field, "buckets", [5, 10, 15, 20])
                alpha_set += ts_comp_factory(op, field, "buckets", [10])

            # B. 普通时间序列算子
            # 处理所有以 "ts_" 开头的时间序列算子
            # 包括即时成交率 "inst_tvr"
            elif op.startswith("ts_") or op == "inst_tvr":

                alpha_set += ts_factory(op, field)

            # C. 分组算子
            # 处理分组相关的算子，针对美国市场
            elif op.startswith("group_"):

                alpha_set += group_factory(op, field, "usa")

            # D. 向量算子
            # 处理向量运算相关的算子
            elif op.startswith("vector"):

                alpha_set += vector_factory(op, field)

            # E. 特殊算子
            # signed_power 算子固定使用指数 2
            elif op == "signed_power":

                alpha = "%s(%s, 2)" % (op, field)
                alpha_set.append(alpha)

            # F. 通用算子
            # 其他所有算子采用标准格式：算子名(字段名)
            else:
                alpha = "%s(%s)" % (op, field)
                alpha_set.append(alpha)

    return alpha_set


basic_ops = ["log", "sqrt", "reverse", "inverse", "rank", "zscore", "log_diff", "s_log_1p",
             'fraction', 'quantile', "normalize", "scale_down"]

ts_ops = ["ts_rank", "ts_zscore", "ts_delta", "ts_sum", "ts_product",
          "ts_ir", "ts_std_dev", "ts_mean", "ts_arg_min", "ts_arg_max", "ts_min_diff",
          "ts_max_diff", "ts_returns", "ts_scale", "ts_skewness", "ts_kurtosis",
          "ts_quantile"]
first_order = first_order_factory(pc_fields, ts_ops + basic_ops)
print(f'first_order is {first_order}')
#%%
