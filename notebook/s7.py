import datetime
import os
import time
import pandas as pd
from config import RECORDS_PATH
from machine_lib import login

pd.set_option('expand_frame_repr', False)
pd.set_option('display.max_rows', 1000)
pd.set_option('display.max_colwidth', 100)


def submit_alpha(session, alpha_id):
    """执行单个Alpha提交（30分钟超时版）"""
    submit_url = f"https://api.worldquantbrain.com/alphas/{alpha_id}/submit"

    # 第一阶段：发起提交
    for attempt in range(1, 6):
        response = session.post(submit_url)
        if response.status_code == 201:
            break
        elif response.status_code == 403:
            return 403, "提交被永久拒绝"
        time.sleep(3)
    else:
        return 408, "服务器连接超时"

    # 第二阶段：延长轮询时间
    start_time = datetime.datetime.now()
    timeout = start_time + datetime.timedelta(minutes=30)
    last_status_time = start_time

    while datetime.datetime.now() < timeout:
        response = session.get(submit_url)
        elapsed = datetime.datetime.now() - start_time

        # 显示等待状态（每5分钟）
        if (datetime.datetime.now() - last_status_time).seconds >= 300:
            print(f"  ⏳ 持续提交中 | 已等待 {elapsed.seconds // 60} 分钟")
            last_status_time = datetime.datetime.now()

        if response.status_code == 200:
            if 'Retry-After' in response.headers:
                time.sleep(float(response.headers['Retry-After']))
            else:
                return 200, "提交成功"
        elif response.status_code == 403:
            return 403, "合规检查未通过"
        else:
            time.sleep(10)  # 非200状态码时增加保护间隔

    return 408, "超时终止（30分钟未完成）"


def submit_batch(session, csv_path):
    """提交一批Alpha"""
    if not os.path.exists(csv_path):
        return False, 0, 0

    try:
        df = pd.read_csv(csv_path)
        if df.empty:
            return False, 0, 0

        df = df.sort_values('self_corr', ascending=True)
    except Exception as e:
        print(f"❌ 读取CSV文件失败: {e}")
        return False, 0, 0

    success_count = 0
    total_count = 0
    
    # 记录成功提交的alpha_id列表
    successfully_submitted_ids = []

    print(f"\n📁 发现待提交Alpha：{len(df)} 个")
    print("=" * 55)

    for idx, row in df.iterrows():
        total_count += 1
        alpha_id = row['id']
        print(f"🚀 开始提交第 {total_count} 个 | ID: {alpha_id}")
        print(f"   📊 自相关性：{row['self_corr']:.4f}")

        # 执行提交
        start_time = datetime.datetime.now()
        status, msg = submit_alpha(session, alpha_id)
        elapsed = datetime.datetime.now() - start_time

        # 处理结果
        if status == 200:
            success_count += 1
            successfully_submitted_ids.append(alpha_id)
            print(
                f"✅ 成功！累计成功 {success_count}/{total_count} | 耗时 {elapsed.seconds // 60}m{elapsed.seconds % 60}s")
        elif status == 408:
            # 服务器连接超时，不删除该alpha，返回需要重启
            print(f"⚠️  {msg} | 耗时 {elapsed.seconds // 60}m{elapsed.seconds % 60}s")
            print("🔄 检测到服务器连接超时，将保留未完成的Alpha并重启...")
            
            # 只删除成功提交的alpha
            if successfully_submitted_ids:
                df_original = pd.read_csv(csv_path)
                df_updated = df_original[~df_original['id'].isin(successfully_submitted_ids)]
                df_updated.to_csv(csv_path, index=False)
                print(f"✅ 已更新CSV，删除 {len(successfully_submitted_ids)} 个成功提交的Alpha")
            
            # 返回特殊标记表示需要重启
            return True, success_count, total_count, True
        else:
            # 其他失败情况（如403），删除该alpha
            successfully_submitted_ids.append(alpha_id)  # 标记为需要删除
            print(f"⚠️  失败：{msg} | 耗时 {elapsed.seconds // 60}m{elapsed.seconds % 60}s")

        print("-" * 55)
        time.sleep(5)  # 提交间隔保护

    # 批次完成后，删除所有已处理的alpha（成功的和永久失败的）
    if successfully_submitted_ids:
        df_original = pd.read_csv(csv_path)
        df_updated = df_original[~df_original['id'].isin(successfully_submitted_ids)]
        df_updated.to_csv(csv_path, index=False)

    # 批次报告
    print("\n" + "=" * 55)
    print(f"📊 本轮提交完成：成功 {success_count}/{total_count}")
    if total_count > 0:
        print(f"✅ 成功率：{success_count / total_count * 100:.1f}%")

    return True, success_count, total_count, False


def main():
    session = login()
    csv_path = os.path.join(RECORDS_PATH, 'submitable_alpha.csv')

    # 统计信息
    total_success = 0
    total_submitted = 0
    check_count = 0

    print("🔄 启动持续监控模式...")
    print(f"📍 监控文件：{csv_path}")
    print("💡 提示：程序将持续运行，按 Ctrl+C 退出")
    print("=" * 55)

    while True:
        try:
            check_count += 1
            current_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 检查文件是否存在且有内容
            if os.path.exists(csv_path):
                try:
                    df = pd.read_csv(csv_path)
                    if not df.empty:
                        print(f"\n⏰ [{current_time}] 检测到新的可提交Alpha！")
                        result = submit_batch(session, csv_path)
                        
                        if len(result) == 4:  # 新的返回格式
                            has_data, success, total, needs_restart = result
                            
                            if has_data:
                                total_success += success
                                total_submitted += total
                                print(f"\n📈 累计统计：总成功 {total_success}/{total_submitted}")
                                
                                if needs_restart:
                                    print("\n🔄 检测到服务器超时，正在重新登录...")
                                    time.sleep(5)
                                    try:
                                        session = login()  # 重新登录
                                        print("✅ 重新登录成功，继续处理剩余Alpha...")
                                    except Exception as e:
                                        print(f"❌ 重新登录失败: {e}")
                                        print("⏳ 30秒后将再次尝试...")
                                        time.sleep(30)
                                        session = login()
                        else:  # 兼容旧的返回格式
                            has_data, success, total = result
                            if has_data:
                                total_success += success
                                total_submitted += total
                                print(f"\n📈 累计统计：总成功 {total_success}/{total_submitted}")
                    else:
                        if check_count % 12 == 1:  # 每60秒显示一次
                            print(f"\r⏳ [{current_time}] 等待新的Alpha... (已检查 {check_count} 次)", end='',
                                  flush=True)
                except pd.errors.EmptyDataError:
                    if check_count % 12 == 1:
                        print(f"\r⏳ [{current_time}] CSV文件为空，等待新数据...", end='', flush=True)
                except Exception as e:
                    print(f"\n❌ [{current_time}] 读取CSV异常: {e}")
            else:
                if check_count % 12 == 1:
                    print(f"\r⏳ [{current_time}] 等待CSV文件创建...", end='', flush=True)

            # 等待5秒后再次检查
            time.sleep(5)

        except KeyboardInterrupt:
            print(f"\n\n🛑 程序被用户中断")
            print("=" * 55)
            print(f"📊 最终统计：")
            print(f"   - 总提交：{total_submitted} 个")
            print(f"   - 成功数：{total_success} 个")
            if total_submitted > 0:
                print(f"   - 成功率：{total_success / total_submitted * 100:.1f}%")
            print(f"   - 运行时长：已检查 {check_count} 次（约 {check_count * 5 // 60} 分钟）")
            break
        except Exception as e:
            print(f"\n❌ 发生未预期错误: {e}")
            print("⏳ 10秒后将重试...")
            time.sleep(10)


if __name__ == '__main__':
    main()