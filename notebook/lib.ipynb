#%% md
# 一阶因子生成工厂函数

### ts_comp_factory 时间序列复合算子工厂函数

除了时间序列，混合的是算子特定的参数值
例如：百分位数 [0.5]
例如：矩的阶数 [2, 3, 4]
例如：分桶数量 [10]
#%%
from itertools import product

def ts_comp_factory(op, field, factor, paras):
    output = []
    # l1, l2 = [3, 5, 10, 20, 60, 120, 240], paras
    l1, l2 = [5, 22, 66, 120, 240], paras
    comb = list(product(l1, l2))

    for day, para in comb:

        if type(para) == float:
            alpha = "%s(%s, %d, %s=%.1f)" % (op, field, day, factor, para)
        elif type(para) == int:
            alpha = "%s(%s, %d, %s=%d)" % (op, field, day, factor, para)

        output.append(alpha)

    return output
#%% md
我来为您详细解释 `ts_comp_factory` 函数的作用。这是一个**时间序列复合算子工厂**函数。

## 函数整体作用

`ts_comp_factory` 是一个**参数化因子生成器**，专门用于生成需要**多个参数**的时间序列算子因子表达式。

## 详细执行流程

### 1. 参数设置
```python
output = []  # 存储生成的因子表达式
l1, l2 = [5, 22, 66, 120, 240], paras  # 时间窗口和算子参数
```

- **`l1`**：时间窗口参数列表 `[5, 22, 66, 120, 240]`
  - 代表不同的时间长度（天数）
  - 5天≈1周，22天≈1月，66天≈3月，120天≈半年，240天≈1年
- **`l2 (paras)`**：算子特定参数
  - 从函数调用时传入的参数列表

### 2. 参数组合生成
```python
comb = list(product(l1, l2))  # 生成所有可能的参数组合
```

使用**笛卡尔积**生成所有时间窗口和算子参数的组合。

**示例**：
- 如果 `paras = [0.5]`
- 则 `comb = [(5, 0.5), (22, 0.5), (66, 0.5), (120, 0.5), (240, 0.5)]`

### 3. 因子表达式生成
```python
for day, para in comb:
    if type(para) == float:
        alpha = "%s(%s, %d, %s=%.1f)" % (op, field, day, factor, para)
    elif type(para) == int:
        alpha = "%s(%s, %d, %s=%d)" % (op, field, day, factor, para)
    output.append(alpha)
```

根据参数类型生成不同格式的因子表达式：
- **浮点数参数**：保留一位小数 `%.1f`
- **整数参数**：整数格式 `%d`

## 实际应用示例

### 示例1：百分位数算子
调用：`ts_comp_factory("ts_percentage", "close_price", "percentage", [0.5])`

生成：
```python
[
    "ts_percentage(close_price, 5, percentage=0.5)",
    "ts_percentage(close_price, 22, percentage=0.5)", 
    "ts_percentage(close_price, 66, percentage=0.5)",
    "ts_percentage(close_price, 120, percentage=0.5)",
    "ts_percentage(close_price, 240, percentage=0.5)"
]
```

### 示例2：矩算子
调用：`ts_comp_factory("ts_moment", "volume", "k", [2, 3, 4])`

生成：
```python
[
    "ts_moment(volume, 5, k=2)",
    "ts_moment(volume, 22, k=2)",
    "ts_moment(volume, 66, k=2)",
    "ts_moment(volume, 120, k=2)",
    "ts_moment(volume, 240, k=2)",
    "ts_moment(volume, 5, k=3)",
    "ts_moment(volume, 22, k=3)",
    # ... 总共15个组合 (5×3)
]
```

### 示例3：熵算子
调用：`ts_comp_factory("ts_entropy", "returns", "buckets", [10])`

生成：
```python
[
    "ts_entropy(returns, 5, buckets=10)",
    "ts_entropy(returns, 22, buckets=10)",
    "ts_entropy(returns, 66, buckets=10)", 
    "ts_entropy(returns, 120, buckets=10)",
    "ts_entropy(returns, 240, buckets=10)"
]
```

## 核心意义

这个函数解决了**时间序列因子的参数化问题**：

1. **多维度探索**：系统性地探索不同时间窗口和算子参数的组合
2. **标准化时间框架**：使用统一的时间窗口设置（周、月、季、半年、年）
3. **批量生成**：一次调用生成多个相关因子，提高效率
4. **参数优化**：通过不同参数组合寻找最优的因子配置

## 与 `first_order_factory` 的配合

在 `first_order_factory` 中，这个函数被用于处理需要额外参数的复杂时间序列算子：

```python
# 在 first_order_factory 中的调用
if op == "ts_percentage":
    alpha_set += ts_comp_factory(op, field, "percentage", [0.5])
elif op == "ts_moment":
    alpha_set += ts_comp_factory(op, field, "k", [2, 3, 4])
```

这种设计使得因子生成既**系统化**又**精细化**，能够充分挖掘时间序列数据中的信息。我来为您详细解释 `ts_comp_factory` 函数的作用。这是一个**时间序列复合算子工厂**函数。

## 函数整体作用

`ts_comp_factory` 是一个**参数化因子生成器**，专门用于生成需要**多个参数**的时间序列算子因子表达式。

## 详细执行流程

### 1. 参数设置
```python
output = []  # 存储生成的因子表达式
l1, l2 = [5, 22, 66, 120, 240], paras  # 时间窗口和算子参数
```

- **`l1`**：时间窗口参数列表 `[5, 22, 66, 120, 240]`
  - 代表不同的时间长度（天数）
  - 5天≈1周，22天≈1月，66天≈3月，120天≈半年，240天≈1年
- **`l2 (paras)`**：算子特定参数
  - 从函数调用时传入的参数列表

### 2. 参数组合生成
```python
comb = list(product(l1, l2))  # 生成所有可能的参数组合
```

使用**笛卡尔积**生成所有时间窗口和算子参数的组合。

**示例**：
- 如果 `paras = [0.5]`
- 则 `comb = [(5, 0.5), (22, 0.5), (66, 0.5), (120, 0.5), (240, 0.5)]`

### 3. 因子表达式生成
```python
for day, para in comb:
    if type(para) == float:
        alpha = "%s(%s, %d, %s=%.1f)" % (op, field, day, factor, para)
    elif type(para) == int:
        alpha = "%s(%s, %d, %s=%d)" % (op, field, day, factor, para)
    output.append(alpha)
```

根据参数类型生成不同格式的因子表达式：
- **浮点数参数**：保留一位小数 `%.1f`
- **整数参数**：整数格式 `%d`

## 实际应用示例

### 示例1：百分位数算子
调用：`ts_comp_factory("ts_percentage", "close_price", "percentage", [0.5])`

生成：
```python
[
    "ts_percentage(close_price, 5, percentage=0.5)",
    "ts_percentage(close_price, 22, percentage=0.5)", 
    "ts_percentage(close_price, 66, percentage=0.5)",
    "ts_percentage(close_price, 120, percentage=0.5)",
    "ts_percentage(close_price, 240, percentage=0.5)"
]
```

### 示例2：矩算子
调用：`ts_comp_factory("ts_moment", "volume", "k", [2, 3, 4])`

生成：
```python
[
    "ts_moment(volume, 5, k=2)",
    "ts_moment(volume, 22, k=2)",
    "ts_moment(volume, 66, k=2)",
    "ts_moment(volume, 120, k=2)",
    "ts_moment(volume, 240, k=2)",
    "ts_moment(volume, 5, k=3)",
    "ts_moment(volume, 22, k=3)",
    # ... 总共15个组合 (5×3)
]
```

### 示例3：熵算子
调用：`ts_comp_factory("ts_entropy", "returns", "buckets", [10])`

生成：
```python
[
    "ts_entropy(returns, 5, buckets=10)",
    "ts_entropy(returns, 22, buckets=10)",
    "ts_entropy(returns, 66, buckets=10)", 
    "ts_entropy(returns, 120, buckets=10)",
    "ts_entropy(returns, 240, buckets=10)"
]
```

## 核心意义

这个函数解决了**时间序列因子的参数化问题**：

1. **多维度探索**：系统性地探索不同时间窗口和算子参数的组合
2. **标准化时间框架**：使用统一的时间窗口设置（周、月、季、半年、年）
3. **批量生成**：一次调用生成多个相关因子，提高效率
4. **参数优化**：通过不同参数组合寻找最优的因子配置

## 与 `first_order_factory` 的配合

在 `first_order_factory` 中，这个函数被用于处理需要额外参数的复杂时间序列算子：

```python
# 在 first_order_factory 中的调用
if op == "ts_percentage":
    alpha_set += ts_comp_factory(op, field, "percentage", [0.5])
elif op == "ts_moment":
    alpha_set += ts_comp_factory(op, field, "k", [2, 3, 4])
```

这种设计使得因子生成既**系统化**又**精细化**，能够充分挖掘时间序列数据中的信息。
#%%
## ts_factory 时间序列算子工厂函数


#%%
def ts_factory(op, field):
    output = []
    # days = [3, 5, 10, 20, 60, 120, 240]
    days = [5, 22, 66, 120, 240]

    for day in days:
        alpha = "%s(%s, %d)" % (op, field, day)
        output.append(alpha)

    return output
#%% md
我来详细解释 `ts_factory` 函数的作用。这是一个**时间序列算子工厂**函数。

## 函数整体作用

`ts_factory` 是一个**简单时间序列因子生成器**，用于为单一算子生成不同时间窗口的因子表达式。

## 详细执行流程

### 1. 初始化设置
```python
output = []  # 存储生成的因子表达式
days = [5, 22, 66, 120, 240]  # 预定义的时间窗口列表
```

### 2. 时间窗口参数
`days = [5, 22, 66, 120, 240]` 代表不同的时间尺度：
- **5天** ≈ 1个交易周
- **22天** ≈ 1个交易月 
- **66天** ≈ 3个交易月（1个季度）
- **120天** ≈ 半年
- **240天** ≈ 1年

### 3. 因子表达式生成
```python
for day in days:
    alpha = "%s(%s, %d)" % (op, field, day)
    output.append(alpha)
```

为每个时间窗口生成标准格式的因子表达式：`算子名(字段名, 天数)`

## 实际应用示例

### 示例1：时间序列均值
调用：`ts_factory("ts_mean", "close_price")`

生成：
```python
[
    "ts_mean(close_price, 5)",      # 5日均价
    "ts_mean(close_price, 22)",     # 22日均价  
    "ts_mean(close_price, 66)",     # 66日均价
    "ts_mean(close_price, 120)",    # 120日均价
    "ts_mean(close_price, 240)"     # 240日均价
]
```

### 示例2：时间序列排名
调用：`ts_factory("ts_rank", "volume")`

生成：
```python
[
    "ts_rank(volume, 5)",           # 5日成交量排名
    "ts_rank(volume, 22)",          # 22日成交量排名
    "ts_rank(volume, 66)",          # 66日成交量排名  
    "ts_rank(volume, 120)",         # 120日成交量排名
    "ts_rank(volume, 240)"          # 240日成交量排名
]
```

### 示例3：时间序列标准差
调用：`ts_factory("ts_std_dev", "returns")`

生成：
```python
[
    "ts_std_dev(returns, 5)",       # 5日收益率标准差
    "ts_std_dev(returns, 22)",      # 22日收益率标准差
    "ts_std_dev(returns, 66)",      # 66日收益率标准差
    "ts_std_dev(returns, 120)",     # 120日收益率标准差
    "ts_std_dev(returns, 240)"      # 240日收益率标准差
]
```

## 与 `ts_comp_factory` 的区别

| 特性 | `ts_factory` | `ts_comp_factory` |
|------|-------------|------------------|
| **参数复杂度** | 简单：只有时间窗口 | 复合：时间窗口 + 算子参数 |
| **输出格式** | `算子(字段, 天数)` | `算子(字段, 天数, 参数=值)` |
| **适用算子** | 标准时间序列算子 | 需要额外参数的复杂算子 |
| **生成数量** | 5个（固定时间窗口数） | 5×参数数量 |

## 在 `first_order_factory` 中的使用

```python
elif op.startswith("ts_") or op == "inst_tvr":
    alpha_set += ts_factory(op, field)
```

这行代码表明：
- 所有以 "ts_" 开头的标准时间序列算子
- 以及 "inst_tvr"（即时成交率）算子
- 都使用 `ts_factory` 进行处理

## 核心意义

1. **时间尺度多样化**：为同一个算子提供多个时间维度的视角
2. **标准化处理**：统一的时间窗口设置确保因子间的可比性  
3. **简洁高效**：对于不需要额外参数的算子，提供简单直接的生成方式
4. **策略灵活性**：不同时间窗口适应不同的交易策略需求
   - 短期窗口（5-22天）：适合高频交易
   - 中期窗口（66天）：适合中期趋势跟踪
   - 长期窗口（120-240天）：适合长期投资策略

这个函数体现了量化投资中**多时间框架分析**的核心理念，通过系统性地覆盖不同时间尺度来捕捉市场的多层次信息。
#%% md
## group_factory 分组算子工厂函数

#%%
def group_factory(op, field, region):
    output = []
    vectors = ["cap"]

    chn_group_13 = ['pv13_h_min2_sector', 'pv13_di_6l', 'pv13_rcsed_6l', 'pv13_di_5l', 'pv13_di_4l',
                    'pv13_di_3l', 'pv13_di_2l', 'pv13_di_1l', 'pv13_parent', 'pv13_level']

    chn_group_1 = ['sta1_top3000c30', 'sta1_top3000c20',
                   'sta1_top3000c10', 'sta1_top3000c2', 'sta1_top3000c5']

    chn_group_2 = ['sta2_top3000_fact4_c10',
                   'sta2_top2000_fact4_c50', 'sta2_top3000_fact3_c20']

    chn_group_7 = ['oth171_region_sector_long_d1_sector', 'oth171_region_sector_short_d1_sector',
                   'oth171_sector_long_d1_sector', 'oth171_sector_short_d1_sector']

    hkg_group_13 = ['pv13_10_f3_g2_minvol_1m_sector', 'pv13_10_minvol_1m_sector', 'pv13_20_minvol_1m_sector',
                    'pv13_2_minvol_1m_sector', 'pv13_5_minvol_1m_sector', 'pv13_1l_scibr', 'pv13_3l_scibr',
                    'pv13_2l_scibr', 'pv13_4l_scibr', 'pv13_5l_scibr']

    hkg_group_1 = ['sta1_allc50', 'sta1_allc5',
                   'sta1_allxjp_513_c20', 'sta1_top2000xjp_513_c5']

    hkg_group_2 = ['sta2_all_xjp_513_all_fact4_c10', 'sta2_top2000_xjp_513_top2000_fact3_c10',
                   'sta2_allfactor_xjp_513_13', 'sta2_top2000_xjp_513_top2000_fact3_c20']

    hkg_group_8 = ['oth455_relation_n2v_p10_q50_w5_kmeans_cluster_5',
                   'oth455_relation_n2v_p10_q50_w4_kmeans_cluster_10',
                   'oth455_relation_n2v_p10_q50_w1_kmeans_cluster_20',
                   'oth455_partner_n2v_p50_q200_w4_kmeans_cluster_5',
                   'oth455_partner_n2v_p10_q50_w4_pca_fact3_cluster_10',
                   'oth455_customer_n2v_p50_q50_w1_kmeans_cluster_5']

    twn_group_13 = ['pv13_2_minvol_1m_sector', 'pv13_20_minvol_1m_sector', 'pv13_10_minvol_1m_sector',
                    'pv13_5_minvol_1m_sector', 'pv13_10_f3_g2_minvol_1m_sector', 'pv13_5_f3_g2_minvol_1m_sector',
                    'pv13_2_f4_g3_minvol_1m_sector']

    twn_group_1 = ['sta1_allc50', 'sta1_allxjp_513_c50', 'sta1_allxjp_513_c20', 'sta1_allxjp_513_c2',
                   'sta1_allc20', 'sta1_allxjp_513_c5', 'sta1_allxjp_513_c10', 'sta1_allc2', 'sta1_allc5']

    twn_group_2 = ['sta2_allfactor_xjp_513_0', 'sta2_all_xjp_513_all_fact3_c20',
                   'sta2_all_xjp_513_all_fact4_c20', 'sta2_all_xjp_513_all_fact4_c50']

    twn_group_8 = ['oth455_relation_n2v_p50_q200_w1_pca_fact1_cluster_20',
                   'oth455_relation_n2v_p10_q50_w3_kmeans_cluster_20',
                   'oth455_relation_roam_w3_pca_fact2_cluster_5',
                   'oth455_relation_n2v_p50_q50_w2_pca_fact2_cluster_10',
                   'oth455_relation_n2v_p10_q200_w5_pca_fact2_cluster_20',
                   'oth455_relation_n2v_p50_q50_w5_kmeans_cluster_5']

    usa_group_13 = ['pv13_h_min2_3000_sector', 'pv13_r2_min20_3000_sector', 'pv13_r2_min2_3000_sector',
                    'pv13_r2_min2_3000_sector', 'pv13_h_min2_focused_pureplay_3000_sector']

    usa_group_1 = ['sta1_top3000c50', 'sta1_allc20',
                   'sta1_allc10', 'sta1_top3000c20', 'sta1_allc5']

    usa_group_2 = ['sta2_top3000_fact3_c50',
                   'sta2_top3000_fact4_c20', 'sta2_top3000_fact4_c10']

    usa_group_3 = ['sta3_2_sector', 'sta3_3_sector', 'sta3_news_sector', 'sta3_peer_sector',
                   'sta3_pvgroup1_sector', 'sta3_pvgroup2_sector', 'sta3_pvgroup3_sector', 'sta3_sec_sector']

    usa_group_4 = ['rsk69_01c_1m', 'rsk69_57c_1m', 'rsk69_02c_2m', 'rsk69_5c_2m', 'rsk69_02c_1m',
                   'rsk69_05c_2m', 'rsk69_57c_2m', 'rsk69_5c_1m', 'rsk69_05c_1m', 'rsk69_01c_2m']

    usa_group_5 = ['anl52_2000_backfill_d1_05c', 'anl52_3000_d1_05c', 'anl52_3000_backfill_d1_02c',
                   'anl52_3000_backfill_d1_5c', 'anl52_3000_backfill_d1_05c', 'anl52_3000_d1_5c']

    usa_group_6 = ['mdl10_group_name']

    usa_group_7 = ['oth171_region_sector_long_d1_sector', 'oth171_region_sector_short_d1_sector',
                   'oth171_sector_long_d1_sector', 'oth171_sector_short_d1_sector']

    usa_group_8 = ['oth455_competitor_n2v_p10_q50_w1_kmeans_cluster_10',
                   'oth455_customer_n2v_p10_q50_w5_kmeans_cluster_10',
                   'oth455_relation_n2v_p50_q200_w5_kmeans_cluster_20',
                   'oth455_competitor_n2v_p50_q50_w3_kmeans_cluster_10',
                   'oth455_relation_n2v_p50_q50_w3_pca_fact2_cluster_10',
                   'oth455_partner_n2v_p10_q50_w2_pca_fact2_cluster_5',
                   'oth455_customer_n2v_p50_q50_w3_kmeans_cluster_5',
                   'oth455_competitor_n2v_p50_q200_w5_kmeans_cluster_20']

    asi_group_13 = ['pv13_20_minvol_1m_sector', 'pv13_5_f3_g2_minvol_1m_sector', 'pv13_10_f3_g2_minvol_1m_sector',
                    'pv13_2_f4_g3_minvol_1m_sector', 'pv13_10_minvol_1m_sector', 'pv13_5_minvol_1m_sector']

    asi_group_1 = ['sta1_allc50', 'sta1_allc10', 'sta1_minvol1mc50', 'sta1_minvol1mc20',
                   'sta1_minvol1m_normc20', 'sta1_minvol1m_normc50']
    asi_group_1 = []

    asi_group_8 = ['oth455_partner_roam_w3_pca_fact1_cluster_5',
                   'oth455_relation_roam_w3_pca_fact1_cluster_20',
                   'oth455_relation_roam_w3_kmeans_cluster_20',
                   'oth455_relation_n2v_p10_q200_w5_pca_fact1_cluster_20',
                   'oth455_relation_n2v_p10_q200_w5_pca_fact1_cluster_20',
                   'oth455_competitor_n2v_p10_q200_w1_kmeans_cluster_10']
    asi_group_8 = []

    jpn_group_1 = ['sta1_alljpn_513_c5', 'sta1_alljpn_513_c50',
                   'sta1_alljpn_513_c2', 'sta1_alljpn_513_c20']

    jpn_group_2 = ['sta2_top2000_jpn_513_top2000_fact3_c20', 'sta2_all_jpn_513_all_fact1_c5',
                   'sta2_allfactor_jpn_513_9', 'sta2_all_jpn_513_all_fact1_c10']

    jpn_group_8 = ['oth455_customer_n2v_p50_q50_w5_kmeans_cluster_10',
                   'oth455_customer_n2v_p50_q50_w4_kmeans_cluster_10',
                   'oth455_customer_n2v_p50_q50_w3_kmeans_cluster_10',
                   'oth455_customer_n2v_p50_q50_w2_kmeans_cluster_10',
                   'oth455_customer_n2v_p50_q200_w5_kmeans_cluster_10',
                   'oth455_customer_n2v_p50_q200_w5_kmeans_cluster_10']

    jpn_group_13 = ['pv13_2_minvol_1m_sector', 'pv13_2_f4_g3_minvol_1m_sector', 'pv13_10_minvol_1m_sector',
                    'pv13_10_f3_g2_minvol_1m_sector', 'pv13_all_delay_1_parent', 'pv13_all_delay_1_level']

    kor_group_13 = ['pv13_10_f3_g2_minvol_1m_sector', 'pv13_5_minvol_1m_sector', 'pv13_5_f3_g2_minvol_1m_sector',
                    'pv13_2_minvol_1m_sector', 'pv13_20_minvol_1m_sector', 'pv13_2_f4_g3_minvol_1m_sector']

    kor_group_1 = ['sta1_allc20', 'sta1_allc50', 'sta1_allc2', 'sta1_allc10', 'sta1_minvol1mc50',
                   'sta1_allxjp_513_c10', 'sta1_top2000xjp_513_c50']

    kor_group_2 = ['sta2_all_xjp_513_all_fact1_c50', 'sta2_top2000_xjp_513_top2000_fact2_c50',
                   'sta2_all_xjp_513_all_fact4_c50', 'sta2_all_xjp_513_all_fact4_c5']

    kor_group_8 = ['oth455_relation_n2v_p50_q200_w3_pca_fact3_cluster_5',
                   'oth455_relation_n2v_p50_q50_w4_pca_fact2_cluster_10',
                   'oth455_relation_n2v_p50_q200_w5_pca_fact2_cluster_5',
                   'oth455_relation_n2v_p50_q200_w4_kmeans_cluster_10',
                   'oth455_relation_n2v_p10_q50_w1_kmeans_cluster_10',
                   'oth455_relation_n2v_p50_q50_w5_pca_fact1_cluster_20']

    eur_group_13 = ['pv13_5_sector', 'pv13_2_sector', 'pv13_v3_3l_scibr', 'pv13_v3_2l_scibr', 'pv13_2l_scibr',
                    'pv13_52_sector', 'pv13_v3_6l_scibr', 'pv13_v3_4l_scibr', 'pv13_v3_1l_scibr']

    eur_group_1 = ['sta1_allc10', 'sta1_allc2',
                   'sta1_top1200c2', 'sta1_allc20', 'sta1_top1200c10']

    eur_group_2 = ['sta2_top1200_fact3_c50',
                   'sta2_top1200_fact3_c20', 'sta2_top1200_fact4_c50']

    eur_group_3 = ['sta3_6_sector',
                   'sta3_pvgroup4_sector', 'sta3_pvgroup5_sector']

    # eur_group_7 = ['oth171_region_sector_long_d1_sector', 'oth171_region_sector_short_d1_sector',
    #                'oth171_sector_long_d1_sector', 'oth171_sector_short_d1_sector']
    eur_group_7 = []

    eur_group_8 = ['oth455_relation_n2v_p50_q200_w3_pca_fact1_cluster_5',
                   'oth455_competitor_n2v_p50_q200_w4_kmeans_cluster_20',
                   'oth455_competitor_n2v_p50_q200_w5_pca_fact1_cluster_10',
                   'oth455_competitor_roam_w4_pca_fact2_cluster_20',
                   'oth455_relation_n2v_p10_q200_w2_pca_fact2_cluster_20',
                   'oth455_competitor_roam_w2_pca_fact3_cluster_20']

    glb_group_13 = ["pv13_10_f2_g3_sector", "pv13_2_f3_g2_sector",
                    "pv13_2_sector", "pv13_52_all_delay_1_sector"]

    glb_group_3 = ['sta3_2_sector', 'sta3_3_sector', 'sta3_news_sector', 'sta3_peer_sector',
                   'sta3_pvgroup1_sector', 'sta3_pvgroup2_sector', 'sta3_pvgroup3_sector', 'sta3_sec_sector']

    glb_group_1 = ['sta1_allc20', 'sta1_allc10', 'sta1_allc50', 'sta1_allc5']

    glb_group_2 = ['sta2_all_fact4_c50', 'sta2_all_fact4_c20',
                   'sta2_all_fact3_c20', 'sta2_all_fact4_c10']

    glb_group_13 = ['pv13_2_sector', 'pv13_10_sector', 'pv13_3l_scibr', 'pv13_2l_scibr', 'pv13_1l_scibr',
                    'pv13_52_minvol_1m_all_delay_1_sector', 'pv13_52_minvol_1m_sector', 'pv13_52_minvol_1m_sector']

    # glb_group_7 = ['oth171_region_sector_long_d1_sector', 'oth171_region_sector_short_d1_sector',
    #                'oth171_sector_long_d1_sector', 'oth171_sector_short_d1_sector']
    glb_group_7 = []  # 字段消失了

    glb_group_8 = ['oth455_relation_n2v_p10_q200_w5_kmeans_cluster_5',
                   'oth455_relation_n2v_p10_q50_w2_kmeans_cluster_5',
                   'oth455_relation_n2v_p50_q200_w5_kmeans_cluster_5',
                   'oth455_customer_n2v_p10_q50_w4_pca_fact3_cluster_20',
                   'oth455_competitor_roam_w2_pca_fact1_cluster_10',
                   'oth455_relation_n2v_p10_q200_w2_kmeans_cluster_5']

    amr_group_13 = ['pv13_4l_scibr', 'pv13_1l_scibr', 'pv13_hierarchy_min51_f1_sector',
                    'pv13_hierarchy_min2_600_sector', 'pv13_r2_min2_sector', 'pv13_h_min20_600_sector']

    amr_group_3 = ['sta3_news_sector', 'sta3_peer_sector', 'sta3_pvgroup1_sector', 'sta3_pvgroup2_sector',
                   'sta3_pvgroup3_sector']

    amr_group_8 = ['oth455_relation_roam_w1_pca_fact2_cluster_10',
                   'oth455_competitor_n2v_p50_q50_w4_kmeans_cluster_10',
                   'oth455_competitor_n2v_p50_q50_w3_kmeans_cluster_10',
                   'oth455_competitor_n2v_p50_q50_w2_kmeans_cluster_10',
                   'oth455_competitor_n2v_p50_q50_w1_kmeans_cluster_10',
                   'oth455_competitor_n2v_p50_q200_w5_kmeans_cluster_10']

    group_3 = ["oth171_region_sector_long_d1_sector", "oth171_region_sector_short_d1_sector",
               "oth171_sector_long_d1_sector", "oth171_sector_short_d1_sector"]

    bps_group = "bucket(rank(fnd28_value_05480/close), range='0.2, 1, 0.2')"
    cap_group = "bucket(rank(cap), range='0.1, 1, 0.1')"
    sector_cap_group = "bucket(group_rank(cap,sector),range='0,1,0.1')"
    vol_group = "bucket(rank(ts_std_dev(ts_returns(close,1),20)),range = '0.1,1,0.1')"

    groups = ["market", "sector", "industry", "subindustry",
              bps_group, cap_group, sector_cap_group]

    # if region == "chn" or region.lower() == "chn":
    #     groups += chn_group_13 + chn_group_1 + chn_group_2
    # if region == "twn" or region.lower() == "twn":
    #     groups += twn_group_13 + twn_group_1 + twn_group_2 + twn_group_8
    # if region == "asi" or region.lower() == "asi":
    #     groups += asi_group_13 + asi_group_1 + asi_group_8
    # if region == "usa" or region.lower() == "usa":
    #     groups += usa_group_13 + usa_group_2 + usa_group_4 + usa_group_8
    #     groups += usa_group_5 + usa_group_6
    #     # + usa_group_1 + usa_group_3 + usa_group_7
    # if region == "hkg" or region.lower() == "hkg":
    #     groups += hkg_group_13 + hkg_group_1 + hkg_group_2 + hkg_group_8
    # if region == "kor" or region.lower() == "kor":
    #     groups += kor_group_13 + kor_group_1 + kor_group_2 + kor_group_8
    # if region == "eur" or region.lower() == "eur":
    #     groups += eur_group_13 + eur_group_1 + eur_group_2 + eur_group_3 + eur_group_8 + eur_group_7
    # if region == "glb" or region.lower() == "glb":
    #     # groups += glb_group_13 + glb_group_8 + glb_group_3 + glb_group_1 + glb_group_7
    #     groups += []
    # if region == "amr" or region.lower() == "amr":
    #     groups += amr_group_3 + amr_group_13
    # if region == "jpn" or region.lower() == "jpn":
    #     groups += jpn_group_1 + jpn_group_2 + jpn_group_13 + jpn_group_8

    for group in groups:
        if op.startswith("group_vector"):
            for vector in vectors:
                alpha = "%s(%s,%s,densify(%s))" % (op, field, vector, group)
                output.append(alpha)
        elif op.startswith("group_percentage"):
            alpha = "%s(%s,densify(%s),percentage=0.5)" % (op, field, group)
            output.append(alpha)
        else:
            alpha = "%s(%s,densify(%s))" % (op, field, group)
            output.append(alpha)

    return output
#%% md
我来详细解释 `group_factory` 函数的作用。这是一个**分组算子工厂**函数，用于生成基于不同分组维度的因子表达式。

## 函数整体作用

`group_factory` 是一个**分组因子生成器**，它根据不同的市场区域和分组类别，为字段生成各种分组相关的因子表达式。

## 核心组成部分

### 1. 向量字段定义
```python
vectors = ["cap"]  # 市值向量，用于向量化分组操作
```

### 2. 区域特定分组集合

函数定义了大量特定于不同市场区域的分组字段：

- **中国市场 (CHN)**：`chn_group_13`, `chn_group_1`, `chn_group_2`
- **台湾市场 (TWN)**：`twn_group_13`, `twn_group_1`, `twn_group_2`, `twn_group_8`
- **美国市场 (USA)**：`usa_group_1` 到 `usa_group_8`
- **香港市场 (HKG)**：`hkg_group_1`, `hkg_group_2`, `hkg_group_8`, `hkg_group_13`
- **韩国市场 (KOR)**：`kor_group_1`, `kor_group_2`, `kor_group_8`, `kor_group_13`
- **欧洲市场 (EUR)**：`eur_group_1` 到 `eur_group_8`
- **全球市场 (GLB)**：`glb_group_1` 到 `glb_group_8`
- **美洲市场 (AMR)**：`amr_group_3`, `amr_group_8`, `amr_group_13`
- **日本市场 (JPN)**：`jpn_group_1`, `jpn_group_2`, `jpn_group_8`, `jpn_group_13`
- **亚洲市场 (ASI)**：`asi_group_1`, `asi_group_8`, `asi_group_13`

### 3. 动态分组定义
```python
bps_group = "bucket(rank(fnd28_value_05480/close), range='0.2, 1, 0.2')"      # 账面价值比分组
cap_group = "bucket(rank(cap), range='0.1, 1, 0.1')"                        # 市值分组
sector_cap_group = "bucket(group_rank(cap,sector),range='0,1,0.1')"         # 行业内市值分组
vol_group = "bucket(rank(ts_std_dev(ts_returns(close,1),20)),range = '0.1,1,0.1')" # 波动率分组
```

### 4. 基础分组类别
```python
groups = ["market", "sector", "industry", "subindustry",
          bps_group, cap_group, sector_cap_group]
```

## 因子生成逻辑

### 1. 不同算子的处理方式

```python
for group in groups:
    if op.startswith("group_vector"):
        # 向量化分组算子
        for vector in vectors:
            alpha = "%s(%s,%s,densify(%s))" % (op, field, vector, group)
            output.append(alpha)
    elif op.startswith("group_percentage"):
        # 百分位数分组算子
        alpha = "%s(%s,densify(%s),percentage=0.5)" % (op, field, group)
        output.append(alpha)
    else:
        # 标准分组算子
        alpha = "%s(%s,densify(%s))" % (op, field, group)
        output.append(alpha)
```

### 2. 生成的因子表达式格式

- **向量化分组**：`group_vector_xxx(field, cap, densify(group))`
- **百分位数分组**：`group_percentage(field, densify(group), percentage=0.5)`
- **标准分组**：`group_xxx(field, densify(group))`

## 实际应用示例

### 示例1：标准分组算子
调用：`group_factory("group_rank", "close_price", "usa")`

生成因子包括：
```python
[
    "group_rank(close_price, densify(market))",           # 市场内排名
    "group_rank(close_price, densify(sector))",          # 行业内排名
    "group_rank(close_price, densify(industry))",        # 产业内排名
    "group_rank(close_price, densify(subindustry))",     # 子行业内排名
    "group_rank(close_price, densify(bucket(rank(cap), range='0.1, 1, 0.1')))", # 市值分组内排名
    # ... 更多分组
]
```

### 示例2：向量化分组算子
调用：`group_factory("group_vector_neutralize", "returns", "usa")`

生成因子包括：
```python
[
    "group_vector_neutralize(returns, cap, densify(market))",
    "group_vector_neutralize(returns, cap, densify(sector))",
    "group_vector_neutralize(returns, cap, densify(industry))",
    # ... 更多组合
]
```

### 示例3：百分位数分组算子
调用：`group_factory("group_percentage", "volume", "usa")`

生成因子包括：
```python
[
    "group_percentage(volume, densify(market), percentage=0.5)",
    "group_percentage(volume, densify(sector), percentage=0.5)",
    "group_percentage(volume, densify(industry), percentage=0.5)",
    # ... 更多分组
]
```

## 核心意义和价值

### 1. 多维度分组分析
- **行业分组**：sector, industry, subindustry
- **基本面分组**：账面价值比、市值分组
- **技术面分组**：波动率分组
- **区域特定分组**：各市场专有的分组标准

### 2. 相对化处理
通过分组操作，将绝对值转换为相对排名或相对位置，消除市场整体趋势的影响。

### 3. 风险控制
分组中性化处理有助于：
- 消除行业偏差
- 控制市值偏差
- 降低风格暴露

### 4. 区域适应性
针对不同市场的特点，提供专门的分组标准和因子构建方式。

### 5. `densify()` 函数的作用

densify(x) 是WQ平台的一个基础级（base）算术算子，专门用于优化分组字段的计算效率。

传统分组操作中，如果分组字段有很多可能的值（比如行业分类从0到99），但实际数据中只使用了少数几个值，就会产生大量空的桶，造成计算资源浪费。

核心作用

densify 函数的主要作用是：

> 将具有多个桶（buckets）的分组字段转换为更少数量的可用桶，使分组字段的计算更加高效。


## 注释掉的区域特定逻辑

代码中大量注释掉的区域特定分组逻辑表明：
- 原本设计支持针对不同区域使用不同的分组集合
- 当前版本简化为只使用基础分组类别
- 为未来扩展保留了区域特定的分组定义

这个函数体现了量化投资中**分组中性化**和**相对价值分析**的核心思想，通过系统性地构建各种分组维度的因子，为后续的风险控制和alpha挖掘提供了丰富的工具。
#%% md
## vector_factory 向量算子工厂函数
#%%

def vector_factory(op, field):
    output = []
    vectors = ["cap"]

    for vector in vectors:
        alpha = "%s(%s, %s)" % (op, field, vector)
        output.append(alpha)

    return output

#%% md
我来详细解释 `vector_factory` 函数的作用。这是一个**向量算子工厂**函数。

## 函数整体作用

`vector_factory` 是一个**向量化因子生成器**，用于生成需要**向量参数**的因子表达式。

## 详细执行流程

### 1. 初始化设置
```python
output = []  # 存储生成的因子表达式
vectors = ["cap"]  # 预定义的向量字段列表
```

### 2. 向量字段说明
`vectors = ["cap"]` 中的 `"cap"` 代表：
- **市值（Market Capitalization）**向量
- 这是一个**横截面向量**，包含所有股票在同一时间点的市值数据
- 用于向量化操作中的**权重**或**标准化**参考

### 3. 因子表达式生成
```python
for vector in vectors:
    alpha = "%s(%s, %s)" % (op, field, vector)
    output.append(alpha)
```

为每个向量字段生成标准格式的因子表达式：`算子名(字段名, 向量名)`

## 实际应用示例

### 示例1：向量中性化
调用：`vector_factory("vector_neut", "returns")`

生成：
```python
[
    "vector_neut(returns, cap)"  # 基于市值向量的收益率中性化
]
```

### 示例2：向量投影
调用：`vector_factory("vector_proj", "volume")`

生成：
```python
[
    "vector_proj(volume, cap)"  # 基于市值向量的成交量投影
]
```

## 与 `first_order_factory` 的配合

在 `first_order_factory` 中的调用：
```python
elif op.startswith("vector"):
    alpha_set += vector_factory(op, field)
```

这表明所有以 "vector" 开头的算子都通过 `vector_factory` 处理。

## 核心意义和价值

### 1. **向量化处理**
- 将**横截面数据**（所有股票在同一时间点）作为向量处理
- 支持向量间的**数学运算**和**统计操作**

### 2. **市值权重化**
- 使用市值作为**权重向量**
- 实现**市值加权**的各种操作

### 3. **风险中性化**
- 通过向量操作实现**风险因子中性化**
- 消除特定风险因子的影响

### 4. **标准化处理**
- 基于向量进行**标准化**和**归一化**
- 提高因子的**可比性**和**稳定性**

## 实际应用场景

### 场景1：市值中性化
```python
"vector_neut(returns, cap)"
# 消除收益率中的市值因子影响
# 使因子在不同市值股票间具有可比性
```

### 场景2：向量投影
```python
"vector_proj(volume, cap)"
# 将成交量投影到市值空间
# 分析成交量与市值的相关性
```

### 场景3：向量标准化
```python
"vector_scale(price, cap)"
# 基于市值对价格进行标准化
# 消除市值大小对价格的影响
```

## 设计特点

### 1. **简洁性**
- 函数结构简单明了
- 只处理一个向量字段（cap）
- 输出格式统一

### 2. **扩展性**
- 可以轻松添加更多向量字段
- 支持各种向量算子

### 3. **标准化**
- 所有向量因子都使用相同的向量参数
- 确保因子间的一致性

## 与其他工厂函数的区别

| 特性 | `vector_factory` | `ts_factory` | `group_factory` |
|------|----------------|-------------|----------------|
| **参数类型** | 向量字段 | 时间窗口 | 分组类别 |
| **输出格式** | `算子(字段, 向量)` | `算子(字段, 天数)` | `算子(字段, 分组)` |
| **应用场景** | 横截面向量操作 | 时间序列操作 | 分组中性化 |
| **核心作用** | 向量化处理 | 时间维度扩展 | 分组维度扩展 |

## 总结

`vector_factory` 函数是**向量化因子构建**的核心工具，它通过市值向量实现了：

1. **风险中性化**：消除市值因子的影响
2. **标准化处理**：基于市值进行数据标准化
3. **向量运算**：支持复杂的向量数学操作
4. **一致性保证**：确保所有向量因子使用统一的参考向量

这个函数体现了量化投资中**多因子模型**和**风险中性化**的核心理念，通过向量化操作提高因子的质量和稳定性。
#%%
