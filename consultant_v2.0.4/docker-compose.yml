version: "3.8"

services:
  wq-xiong:
    image: worldquant-factor-tool:latest
    container_name: wq-xiong
    platform: linux/amd64
    restart: always
    ports:
      - "5001:5000"
    environment:
      - TZ=Asia/Shanghai
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONPATH=/app
      - LD_LIBRARY_PATH=/app/pytransform:/usr/local/lib/pyarmor_runtime_000000:/usr/lib/x86_64-linux-gnu:/lib/x86_64-linux-gnu
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - /Users/<USER>/800_Code/Mei6-Code/wq/consultant_v2.0.4_yanshangxiong:/app
    networks:
      - wq-network

  wq-long:
    image: worldquant-factor-tool:latest
    container_name: wq-long
    platform: linux/amd64
    restart: always
    ports:
      - "5002:5000"
    environment:
      - TZ=Asia/Shanghai
      - PYTHONUNBUFFERED=1
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONPATH=/app
      - LD_LIBRARY_PATH=/app/pytransform:/usr/local/lib/pyarmor_runtime_000000:/usr/lib/x86_64-linux-gnu:/lib/x86_64-linux-gnu
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - /etc/timezone:/etc/timezone:ro
      - /Users/<USER>/800_Code/Mei6-Code/wq/consultant_v2.0.4_yankelong:/app
    networks:
      - wq-network

networks:
  wq-network:
    driver: bridge
