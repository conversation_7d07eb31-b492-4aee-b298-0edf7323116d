"""
鑫鑫鑫｜Quant--WQ
世坤因子挖掘

版权所有 ©️ 鑫鑫鑫
微信: xinxinjijin8

本代码仅供个人学习使用，未经授权不得复制、修改或用于商业用途。

Author: 鑫鑫鑫
"""

更新日志

v2.0.4 2025.07.16
1. dig1支持推荐字段，需要开启recommend为ON，否则无效
2. dig2需要dataset_id输入对应的字段的tag，比如recommend1，就可以跑二阶段
3. dig3，4以此类推

v2.0.3 2025.07.14
1. check升级，提速10倍+

v2.0.2 2025.07.11
1. 修复了1000个以后停下来，现在可以一直跑

v2.0.1 2025.07.07
1. 增加maxTrade

v2.0.0 2025.07.01
1. 网页版全面上新

v1.2.1 2025.06.27
1. machine_lib.py 删除冗余
2. logger换成loguru

v1.2.0 2025.06.05
1. check.py支持PPAC
2. 回测支持description

v1.1.9 2025.05.30
1. 对日志进行了修改，提示词更加完善
2. digging_consultant_4step.py增加了部分表达式


v1.1.8 2025.05.15
1. digging_consultant_1step.py 增加改进了session的复用
2. digging_consultant_2step.py 增加改进了session的复用
3. digging_consultant_3step.py 增加改进了session的复用
4. digging_consultant_4step.py 增加改进了session的复用
5. machine_lib.py 增加部分适配改动
6. check.py 修复了一些bug

v1.1.7 2025.05.08
1. check.py 改成本地测self corr，速度提升10000%，稳定性提升300%
2. machine_lib.py 增加部分适配改动

v1.1.6 2025.04.23
特别鸣谢：【JS53917】对代码的支持
1. digging_consultant_1step.py 增加logging，降低了报错的可能性，
2. digging_consultant_2step.py 增加logging，降低了报错的可能性
3. digging_consultant_3step.py 增加logging，降低了报错的可能性
4. digging_consultant_4step.py 增加logging，降低了报错的可能性
5. digging_consultant_templates.py 增加logging，降低了报错的可能性

v1.1.5 2025.03.15
1. digging_consultant_4step.py 修复BUG，删除不能跑的pv和regression

v1.1.4 2025.03.14
1. digging_consultant_2step.py 第二轮挖掘升级，增加新的GROUP类型，自适应自己已有的ops
2. digging_consultant_1step.py 在低内存机器上运行不稳定，每次random一部分跑

v1.1.3 2025.03.12
1. digging_consultant_1step.py 第一轮挖掘升级，双重时序降低prod

v1.1.2 2025.03.04
1. digging_consultant_templates.py 自己定义模板挖掘
2. digging_consultant_4step.py 第四轮挖掘

v1.1.1 2025.02.24
1. machine_lib.py  增加：conn = aiohttp.TCPConnector(ssl=False) 解决ssl问题

v1.0 2025.01.02
初始版本
功能：
1. digging_consultant_1step.py 第一轮挖掘
2. digging_consultant_2step.py 第二轮挖掘
3. digging_consultant_3step.py 第三轮挖掘
4. check.py自动获取可以提交的因子
5. submit_alpha.py自动提交因子
"""

# 简易使用教程
1. python3
2. 配置好python环境， pip install -r requirements.txt


    
