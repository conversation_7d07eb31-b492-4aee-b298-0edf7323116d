# Workday 教程

## [① 鹏飞顾问群-入群指南](https://docs.qq.com/doc/DRnZmVW5Hb3Fza0tW)

## ② 激活码

## ③ [世坤因子挖掘工具\_鑫版本顾问级别 V2.0.4 使用手册](https://docs.qq.com/doc/DVkFIdGRoeU1GcGFt?g=MC0yOA%253D%253D#g=MC0yOA%253D%253D)

### 视频

#### 2.0.4 顾问代码操作指南.MOV

录制文件：https://meeting.tencent.com/crm/2a8z19Jv84
访问密码：1QSZ

#### 顾问代码安装教学.MOV

录制文件：https://meeting.tencent.com/cw/Noq33D1Obf
访问密码：8TCB

### 代码

#### 跑代码常用工具安装包

[https://pan.baidu.com/s/1AaJk9U_y5IytFUzgQqhyTg?pwd=7am8](https://pan.baidu.com/s/1AaJk9U_y5IytFUzgQqhyTg?pwd=7am8)

#### 代码文件

> 代码会不定期的发送更新到邮箱

/Users/<USER>/200*Life*生活/230*Documents*文档/239*Private_Document*私人文档/239.1*世坤量化\_WorldQuantBrain*兼职/世坤因子挖掘工具\_鑫版本顾问级别 V2.0.4_Linux_20250726.zip

#### MacMini 执行过程

手动配置 Docker Desktop 使用国内镜像源

1. 打开 Docker Desktop
2. 点击设置图标 (齿轮图标)
3. 选择 "Docker Engine"
4. 在配置文件中添加镜像源配置：

```json
{
 "registry-mirrors": [
   "https://docker.mirrors.ustc.edu.cn",
   "https://hub-mirror.c.163.com",
   "https://mirror.baidubce.com"
 ]
}
```

Prompt

```
需要构建一个python运行环境的docker镜像，用来运行脚本工程

注意
1. 必须挂载外部代码文件，不能把文件打包到镜像内部，因为代码会发生变更
2. 缺少内容的话，需要在 dockerfile 中补充，在 docker 镜像中保存完整的 python 运行环境
3. 不要所有的 bash、zsh 脚本，直接给出 docker run 命令
4. 注意运行平台，docker实例可能会在mac的m1芯片平台，Intel平台运行，
5. 注意Pyarmor 在环境中的版本
6. python 基础镜像是 3.9.13

最终结果只需要 docker run 命令就可以正常启动
```

```bash
# 直接启动，使用默认基础路径
cd /Users/<USER>/800_Code/Mei6-Code/wq/consultant_v2.0.4
docker build -t worldquant-factor-tool:latest .
docker-compose up -d
```

```bash
# 1. 构建Docker镜像
# docker build --platform=linux/amd64 -t worldquant-factor-tool .
# 构建多架构支持的Docker镜像
docker build -t worldquant-factor-tool:latest .

# 2. 运行容器（挂载外部文件方式）
# 颜尚雄 5001
docker run -d \
  --name wq-xiong \
  -p 5001:5000 \
  -e TZ=Asia/Shanghai \
  -v /etc/localtime:/etc/localtime:ro \
  -v /etc/timezone:/etc/timezone:ro \
  -v "/Users/<USER>/800_Code/Mei6-Code/wq/consultant_v2.0.4_yanshangxiong:/app" \
  --platform linux/amd64 \
  --restart always \
  worldquant-factor-tool:latest
# 颜克龙 5002
docker run -d \
  --name wq-long \
  -p 5002:5000 \
  -e TZ=Asia/Shanghai \
  -v /etc/localtime:/etc/localtime:ro \
  -v /etc/timezone:/etc/timezone:ro \
  -v "/Users/<USER>/800_Code/Mei6-Code/wq/consultant_v2.0.4_yankelong:/app" \
  --platform linux/amd64 \
  --restart always \
  worldquant-factor-tool:latest
```

```bash
cd '/Users/<USER>/Downloads/[Xiong]世坤因子挖掘工具_鑫版本顾问级别V2.0.4_Linux_20250726'

# 检查python版本是否大于3.9.3
python3 --version

# 虚拟环境
python3 -m venv .venv
source .venv/bin/activate

# 升级pip
python3 -m pip install --upgrade pip

# pip换源 - 清华
# pip3 config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple
# pip换源 - 阿里
pip3 config set global.index-url https://mirrors.aliyun.com/pypi/simple/

# pip安装库
pip3 install -r requirements.txt

# 启动服务
python3 app.py
```

#### 任务参数

- 总体线程数不能超过 8
- 模式使用 PPAC，Grant Master 再使用 consultant 模式
- 只跑一二阶段，Grandmaster 后再跑三四阶段

##### 1.选择数据集

https://platform.worldquantbrain.com/data

> 二阶段的数据集 ID，取决于一阶段是否开启了“数据字段”
>
> - 如果一阶段没有开启“数据字段”，二阶段数据集 ID 就和一阶段一样
> - 如果一阶段开启了“数据字段”，二阶段数据集 ID 就是一阶段*数据字段 ID*

##### 2.选择数据字段

点击进入数据集，复制描述，再结合[fields.py](/Users/<USER>/800_Code/Mei6-Code/wq/tutorial_v1/xin/fields.py)让模型挑选字段组合。

提示词示例：

```
使用数据集
Dataset description
Category:   sentiment/ sentiment1
Dataset ID: sentiment1

This dataset holds earning estimation/surprise and sentiment of each company in USA.

Sentiment represents the aggregate market mood about stocks.

Rising stock price is characterized by positive or "bullish" sentiment, while a declining stock price is characterized by negative or "bearish" sentiment.

The intensity of sentiment represents the strength of the sentiment.

A high intensity of sentiment, regardless of the mood or direction, exists when strong market moves occur.

For example, positive market sentiment combined with high intensity is typically a characteristic of strong bullish moves in prices.

Sentiment data can help us better predict market behavior and improve our forecasting, not only for price direction but also for volatility and volume traded.

Sentiment data have data fields of sentiment scores of an event. Unlike fundamental data, these data naturally have high frequencies, which leads to high turnovers in an Alpha.

To achieve reasonable margin rates, you are advised to use the following operations: hump_decay, ts_decay_linear, and ts_decay_exp_window. But you should be careful with the usage of lookback days greater than 63 (i.e., one quarter) as older events may have no impact.

Data may be based on information from newspapers, news websites, Facebook, Twitter, blog posts, discussion groups, and forums.

Social sentiment indicators help investors identify information in social media that could cause a stock’s price to increase or decrease in the near future. They also help businesses understand how they may be performing in the eyes of their consumers.

在 fields.py 中应该选取哪些数据字段来处理，给出效果最好的 10 个字段组合，返回字符串数组的格式

例如：['close/open', 'liabilities/cap']

使用中文回答
```

返回结果示例：

```python
['anl4_bvps_low/news_eod_close','anl4_capex_high/anl4_netprofit_value', 'anl4_cfo_high/anl4_netprofit_median','anl4_capex_low/news_dividend_yield','anl4_cff_low/news_eps_actual','anl4_capex_value/news_main_vwap','anl4_cfo_high/fscore_profitability','anl4_capex_low/star_val_earnings_projection_fy1','anl4_cfi_low/star_val_earnings_projection_fy2','anl4_capex_high/star_val_earnings_projection_fy3']
```

这些字段组合的选择理由：

1. 新闻和市场情绪相关 ：包含 news_eod_close 、 news_dividend_yield 、 news_eps_actual 、 news_main_vwap 等字段，直接反映市场情绪和新闻影响
2. 盈利预估相关 ：包含多个 star_val_earnings_projection_fy\* 字段，与数据集的盈利预估特性高度匹配
3. 财务健康度指标 ：使用 fscore_profitability 等评分指标，有助于评估公司基本面
4. 现金流分析 ：结合 anl4_cfo 、 anl4_cfi 、 anl4_cff 等现金流指标与盈利数据的比值，提供全面的财务视角
5. 高频特性适配 ：这些组合适合使用推荐的 hump_decay 、 ts_decay_linear 、 ts_decay_exp_window 等操作来处理高频情绪数据
   这些字段组合能够有效捕捉市场情绪变化、盈利预期差异，并为预测股价方向、波动性和交易量提供有价值的信号。

##### 3.check 使用 PPAC

##### python 版本 3.9.3

## ④ [WQ 插件](https://docs.qq.com/doc/DRnFhbnpGUXpaaGVE)

### 下载浏览器插件压缩包

百度网盘“我的网盘/我的资源/世坤量化/WQ 插件-WebDataScope(3).zip”

下载后，解压缩，安装插件

## ⑤ [搞钱指南](https://docs.qq.com/doc/DRmJNQ1NrQnpEdUZP)

### Value Factor 质量 VF

VF 决定了 Base Payment，每天收入有多少

### RA

普通因子 Regular Alpha

### 季度奖

四个等级：Gold、Expert、Master、GrandMaster。每个季度会定级。
每个等级对应的季度奖不同。
定级与 VF 无关！
定级主要看 genius 中的六维。

### PPAC

WQ 举办的常规比赛。
点开因子，如果有 `Power Poll Alpha`，就代表他是参赛的因子。

### 视频讲解

#### 1.顾问 Genius 详解第一部分

地址：https://meeting.tencent.com/cw/N14XEbBPb1
密码：4768

#### 2.顾问 Genius 详解第二部分

地址：https://meeting.tencent.com/cw/lv9xd4W40f
密码：5139

#### 3.伟杰顾问培训

地址：https://pan.quark.cn/s/5d67b78a6999
密码：7tti

下载到本地路径：`/Users/<USER>/200_Life_生活/230_Documents_文档/239_Private_Document_私人文档/239.1_世坤量化_WorldQuantBrain_兼职/consultant/顾问培训`

### SA

#### [伟杰 Super Alpha 教学](https://pan.baidu.com/s/17jZSyULji9LKJNo-seUWzQ?pwd=w4ua)

- 可以提升 Base Payment
- 有权限，尽可能多提交 SA
- SA 计费和 RA 计费是分开的，
  - 组成结构：由 Regular Alpha 和 Super Alpha 两部分组成，每日最高可获得 60）
  - 支付范围：每日最低支付 1），2022 年数据显示前 25%顾问平均收入约 210
  - 动态特性：每日奖金池规模随参与顾问人数变化，每日表现最佳者可能获得$60
- 一天交 4 个 alpha（highest quantity factor） 收益最高
- theme alpha: 与主题有关的 alpha
- 质量因子：使用主题数据集 1-2 个字段构建的 Alpha 可获得 5 倍质量因子提升
- 构建限制：除分组字段外，不得使用 pv1 数据集中的任何字段才能获得乘数效应
- 效果验证：multiplier 低于 1.2 的主题 Alpha 基本无提升效果

SuperAlpha 的提交标准

- 与常规 Alpha 的区别：主要区别在于换手率(Turnover)要求不同
- 常规 Alpha：Turnover 上限为 70%
- SuperAlpha：Turnover 范围在 2%至 40%之间
- 必须写描述，Write descriptions for your selection expression and combo expression
- 提交前检查：必须使用"Check Submission"按钮确保通过所有测试

SuperAlpha 的技术实现：

- 提供 API 接口（与常规 Alpha 类似）
- 支持同时运行三个 SuperAlpha
- 可通过"阿尔法创建引擎"(ACE)获取模板
- 操作入口：在账号界面点击加号可激活 SuperAlpha 权限

SA 提交 `Turnover` min = 2% and max = 40%， _一定要写 description_
write descriptions for your selection expression and combo expression

#### 如果解锁了 SA 权限，会在平台的 learn 页面看到关于 SA 的 select 和 combo 解释

[SA 详细教学](https://support.worldquantbrain.com/hc/zh-cn/community/posts/32759852398487--%E8%BF%9B%E9%98%B6%E5%AD%A6%E4%B9%A0-Super-Alpha-%E6%AF%8F%E6%97%A5%E8%8E%B7%E5%BE%97%E9%A2%9D%E5%A4%961-60USD-%E6%9D%A5%E6%AD%A4%E5%AD%A6%E4%B9%A0)

## ⑥ [顾问专属中文论坛](https://support.worldquantbrain.com/hc/en-us/community/topics/18910956638743-%E9%A1%BE%E9%97%AE%E4%B8%93%E5%B1%9E%E4%B8%AD%E6%96%87%E8%AE%BA%E5%9D%9B)

#### [一键检验 alpha 稳定性](https://support.worldquantbrain.com/hc/en-us/community/posts/32008506789655-%E4%B8%80%E9%94%AE%E6%A3%80%E9%AA%8Calpha%E7%A8%B3%E5%AE%9A%E6%80%A7?input_string=%E4%B8%80%E9%94%AE%E6%A3%80%E9%AA%8Calpha%E7%A8%B3%E5%AE%9Aplus+%E7%89%88%E6%9C%AC)

#### [已选 RA 组合 PnL 模拟 SA 等权 PnL 表现](https://support.worldquantbrain.com/hc/en-us/community/posts/33659784999959-%E5%B7%B2%E9%80%89RA%E7%BB%84%E5%90%88PnL%E6%A8%A1%E6%8B%9FSA%E7%AD%89%E6%9D%83PnL%E8%A1%A8%E7%8E%B0-%E4%B8%80%E7%A7%8D%E6%9C%AC%E5%9C%B0%E9%AA%8C%E8%AF%81%E6%96%B9%E6%B3%95)
