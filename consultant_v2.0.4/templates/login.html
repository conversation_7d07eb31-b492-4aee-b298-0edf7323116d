<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录 - 量化因子挖掘平台</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='style.css') }}">
</head>
<body>
    <div class="login-container">
        <h1>量化因子挖掘平台</h1>

        {% if error %}
        <div class="error">{{ error }}</div>
        {% endif %}

        <form method="POST">
            <div class="form-group">
                <label for="user_id">用户ID:</label>
                <input type="text" id="user_id" name="user_id" value="{{ user_id }}" required>
            </div>

            <div class="form-group">
                <label for="license_key">激活码:</label>
                <input type="password" id="license_key" name="license_key" value="{{ license_key }}" required>
            </div>

            <div class="remember-me">
                <input type="checkbox" id="remember" name="remember">
                <label for="remember">记住我</label>
            </div>

            <button type="submit" class="login-btn">登录</button>
        </form>
    </div>
    <button onclick="shutdown()">安全关闭</button>
    <script>
    function shutdown() {
        fetch('/shutdown', { method: 'POST' })
        setTimeout(() => window.close(), 1000)
    }
    </script>
</body>
</html>